version: '3.8'

services:
  # MySQL Database
  db:
    image: mysql:8.0
    container_name: clothes_db
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: hieu@1010
      MYSQL_DATABASE: clothes_db
      MYSQL_USER: user
      MYSQL_PASSWORD: password
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - app_network

  # phpMyAdmin để quản lý database
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: clothes_phpmyadmin
    restart: always
    environment:
      PMA_HOST: db
      PMA_PORT: 3306
      MYSQL_ROOT_PASSWORD: hieu@1010
    ports:
      - "8080:80"
    depends_on:
      - db
    networks:
      - app_network

  # Backend API (Node.js)
  backend:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: clothes_backend
    restart: always
    environment:
      PORT: 5000
      DB_HOST: db
      DB_USER: root
      DB_PASSWORD: hieu@1010
      DB_NAME: clothes_db
      JWT_SECRET: your_jwt_secret_key
    ports:
      - "5000:5000"
    depends_on:
      - db
    networks:
      - app_network
    volumes:
      - ./server:/app
      - /app/node_modules

  # Frontend (React/Vite)
  frontend:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: clothes_frontend
    restart: always
    ports:
      - "5001:80"
    depends_on:
      - backend
    networks:
      - app_network

  server:
    build: ./server
    ports:
      - "3001:3000"
    env_file:
      - ./server/.env
    restart: unless-stopped

  client:
    build: ./client
    ports:
      - "80:80"
    restart: unless-stopped

networks:
  app_network:
    driver: bridge

volumes:
  mysql_data: