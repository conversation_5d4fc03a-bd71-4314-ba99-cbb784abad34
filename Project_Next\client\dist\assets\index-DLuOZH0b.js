function Xp(l,r){for(var u=0;u<r.length;u++){const c=r[u];if(typeof c!="string"&&!Array.isArray(c)){for(const f in c)if(f!=="default"&&!(f in l)){const d=Object.getOwnPropertyDescriptor(c,f);d&&Object.defineProperty(l,f,d.get?d:{enumerable:!0,get:()=>c[f]})}}}return Object.freeze(Object.defineProperty(l,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const f of document.querySelectorAll('link[rel="modulepreload"]'))c(f);new MutationObserver(f=>{for(const d of f)if(d.type==="childList")for(const m of d.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&c(m)}).observe(document,{childList:!0,subtree:!0});function u(f){const d={};return f.integrity&&(d.integrity=f.integrity),f.referrerPolicy&&(d.referrerPolicy=f.referrerPolicy),f.crossOrigin==="use-credentials"?d.credentials="include":f.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function c(f){if(f.ep)return;f.ep=!0;const d=u(f);fetch(f.href,d)}})();function Vp(l){return l&&l.__esModule&&Object.prototype.hasOwnProperty.call(l,"default")?l.default:l}var co={exports:{}},cs={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lh;function Qp(){if(Lh)return cs;Lh=1;var l=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function u(c,f,d){var m=null;if(d!==void 0&&(m=""+d),f.key!==void 0&&(m=""+f.key),"key"in f){d={};for(var x in f)x!=="key"&&(d[x]=f[x])}else d=f;return f=d.ref,{$$typeof:l,type:c,key:m,ref:f!==void 0?f:null,props:d}}return cs.Fragment=r,cs.jsx=u,cs.jsxs=u,cs}var Hh;function Zp(){return Hh||(Hh=1,co.exports=Qp()),co.exports}var s=Zp(),oo={exports:{}},de={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kh;function Kp(){if(kh)return de;kh=1;var l=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),f=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),m=Symbol.for("react.context"),x=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),b=Symbol.iterator;function O(S){return S===null||typeof S!="object"?null:(S=b&&S[b]||S["@@iterator"],typeof S=="function"?S:null)}var H={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},U=Object.assign,E={};function M(S,Q,F){this.props=S,this.context=Q,this.refs=E,this.updater=F||H}M.prototype.isReactComponent={},M.prototype.setState=function(S,Q){if(typeof S!="object"&&typeof S!="function"&&S!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,S,Q,"setState")},M.prototype.forceUpdate=function(S){this.updater.enqueueForceUpdate(this,S,"forceUpdate")};function R(){}R.prototype=M.prototype;function D(S,Q,F){this.props=S,this.context=Q,this.refs=E,this.updater=F||H}var w=D.prototype=new R;w.constructor=D,U(w,M.prototype),w.isPureReactComponent=!0;var q=Array.isArray,C={H:null,A:null,T:null,S:null,V:null},W=Object.prototype.hasOwnProperty;function $(S,Q,F,Z,ae,xe){return F=xe.ref,{$$typeof:l,type:S,key:Q,ref:F!==void 0?F:null,props:xe}}function K(S,Q){return $(S.type,Q,void 0,void 0,void 0,S.props)}function oe(S){return typeof S=="object"&&S!==null&&S.$$typeof===l}function Ce(S){var Q={"=":"=0",":":"=2"};return"$"+S.replace(/[=:]/g,function(F){return Q[F]})}var be=/\/+/g;function ie(S,Q){return typeof S=="object"&&S!==null&&S.key!=null?Ce(""+S.key):Q.toString(36)}function rt(){}function Ze(S){switch(S.status){case"fulfilled":return S.value;case"rejected":throw S.reason;default:switch(typeof S.status=="string"?S.then(rt,rt):(S.status="pending",S.then(function(Q){S.status==="pending"&&(S.status="fulfilled",S.value=Q)},function(Q){S.status==="pending"&&(S.status="rejected",S.reason=Q)})),S.status){case"fulfilled":return S.value;case"rejected":throw S.reason}}throw S}function Ue(S,Q,F,Z,ae){var xe=typeof S;(xe==="undefined"||xe==="boolean")&&(S=null);var re=!1;if(S===null)re=!0;else switch(xe){case"bigint":case"string":case"number":re=!0;break;case"object":switch(S.$$typeof){case l:case r:re=!0;break;case v:return re=S._init,Ue(re(S._payload),Q,F,Z,ae)}}if(re)return ae=ae(S),re=Z===""?"."+ie(S,0):Z,q(ae)?(F="",re!=null&&(F=re.replace(be,"$&/")+"/"),Ue(ae,Q,F,"",function(St){return St})):ae!=null&&(oe(ae)&&(ae=K(ae,F+(ae.key==null||S&&S.key===ae.key?"":(""+ae.key).replace(be,"$&/")+"/")+re)),Q.push(ae)),1;re=0;var $e=Z===""?".":Z+":";if(q(S))for(var ge=0;ge<S.length;ge++)Z=S[ge],xe=$e+ie(Z,ge),re+=Ue(Z,Q,F,xe,ae);else if(ge=O(S),typeof ge=="function")for(S=ge.call(S),ge=0;!(Z=S.next()).done;)Z=Z.value,xe=$e+ie(Z,ge++),re+=Ue(Z,Q,F,xe,ae);else if(xe==="object"){if(typeof S.then=="function")return Ue(Ze(S),Q,F,Z,ae);throw Q=String(S),Error("Objects are not valid as a React child (found: "+(Q==="[object Object]"?"object with keys {"+Object.keys(S).join(", ")+"}":Q)+"). If you meant to render a collection of children, use an array instead.")}return re}function G(S,Q,F){if(S==null)return S;var Z=[],ae=0;return Ue(S,Z,"","",function(xe){return Q.call(F,xe,ae++)}),Z}function J(S){if(S._status===-1){var Q=S._result;Q=Q(),Q.then(function(F){(S._status===0||S._status===-1)&&(S._status=1,S._result=F)},function(F){(S._status===0||S._status===-1)&&(S._status=2,S._result=F)}),S._status===-1&&(S._status=0,S._result=Q)}if(S._status===1)return S._result.default;throw S._result}var ee=typeof reportError=="function"?reportError:function(S){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var Q=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof S=="object"&&S!==null&&typeof S.message=="string"?String(S.message):String(S),error:S});if(!window.dispatchEvent(Q))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",S);return}console.error(S)};function je(){}return de.Children={map:G,forEach:function(S,Q,F){G(S,function(){Q.apply(this,arguments)},F)},count:function(S){var Q=0;return G(S,function(){Q++}),Q},toArray:function(S){return G(S,function(Q){return Q})||[]},only:function(S){if(!oe(S))throw Error("React.Children.only expected to receive a single React element child.");return S}},de.Component=M,de.Fragment=u,de.Profiler=f,de.PureComponent=D,de.StrictMode=c,de.Suspense=y,de.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=C,de.__COMPILER_RUNTIME={__proto__:null,c:function(S){return C.H.useMemoCache(S)}},de.cache=function(S){return function(){return S.apply(null,arguments)}},de.cloneElement=function(S,Q,F){if(S==null)throw Error("The argument must be a React element, but you passed "+S+".");var Z=U({},S.props),ae=S.key,xe=void 0;if(Q!=null)for(re in Q.ref!==void 0&&(xe=void 0),Q.key!==void 0&&(ae=""+Q.key),Q)!W.call(Q,re)||re==="key"||re==="__self"||re==="__source"||re==="ref"&&Q.ref===void 0||(Z[re]=Q[re]);var re=arguments.length-2;if(re===1)Z.children=F;else if(1<re){for(var $e=Array(re),ge=0;ge<re;ge++)$e[ge]=arguments[ge+2];Z.children=$e}return $(S.type,ae,void 0,void 0,xe,Z)},de.createContext=function(S){return S={$$typeof:m,_currentValue:S,_currentValue2:S,_threadCount:0,Provider:null,Consumer:null},S.Provider=S,S.Consumer={$$typeof:d,_context:S},S},de.createElement=function(S,Q,F){var Z,ae={},xe=null;if(Q!=null)for(Z in Q.key!==void 0&&(xe=""+Q.key),Q)W.call(Q,Z)&&Z!=="key"&&Z!=="__self"&&Z!=="__source"&&(ae[Z]=Q[Z]);var re=arguments.length-2;if(re===1)ae.children=F;else if(1<re){for(var $e=Array(re),ge=0;ge<re;ge++)$e[ge]=arguments[ge+2];ae.children=$e}if(S&&S.defaultProps)for(Z in re=S.defaultProps,re)ae[Z]===void 0&&(ae[Z]=re[Z]);return $(S,xe,void 0,void 0,null,ae)},de.createRef=function(){return{current:null}},de.forwardRef=function(S){return{$$typeof:x,render:S}},de.isValidElement=oe,de.lazy=function(S){return{$$typeof:v,_payload:{_status:-1,_result:S},_init:J}},de.memo=function(S,Q){return{$$typeof:p,type:S,compare:Q===void 0?null:Q}},de.startTransition=function(S){var Q=C.T,F={};C.T=F;try{var Z=S(),ae=C.S;ae!==null&&ae(F,Z),typeof Z=="object"&&Z!==null&&typeof Z.then=="function"&&Z.then(je,ee)}catch(xe){ee(xe)}finally{C.T=Q}},de.unstable_useCacheRefresh=function(){return C.H.useCacheRefresh()},de.use=function(S){return C.H.use(S)},de.useActionState=function(S,Q,F){return C.H.useActionState(S,Q,F)},de.useCallback=function(S,Q){return C.H.useCallback(S,Q)},de.useContext=function(S){return C.H.useContext(S)},de.useDebugValue=function(){},de.useDeferredValue=function(S,Q){return C.H.useDeferredValue(S,Q)},de.useEffect=function(S,Q,F){var Z=C.H;if(typeof F=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return Z.useEffect(S,Q)},de.useId=function(){return C.H.useId()},de.useImperativeHandle=function(S,Q,F){return C.H.useImperativeHandle(S,Q,F)},de.useInsertionEffect=function(S,Q){return C.H.useInsertionEffect(S,Q)},de.useLayoutEffect=function(S,Q){return C.H.useLayoutEffect(S,Q)},de.useMemo=function(S,Q){return C.H.useMemo(S,Q)},de.useOptimistic=function(S,Q){return C.H.useOptimistic(S,Q)},de.useReducer=function(S,Q,F){return C.H.useReducer(S,Q,F)},de.useRef=function(S){return C.H.useRef(S)},de.useState=function(S){return C.H.useState(S)},de.useSyncExternalStore=function(S,Q,F){return C.H.useSyncExternalStore(S,Q,F)},de.useTransition=function(){return C.H.useTransition()},de.version="19.1.0",de}var qh;function Uo(){return qh||(qh=1,oo.exports=Kp()),oo.exports}var N=Uo();const me=Vp(N),Jp=Xp({__proto__:null,default:me},[N]);var uo={exports:{}},os={},fo={exports:{}},ho={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gh;function $p(){return Gh||(Gh=1,function(l){function r(G,J){var ee=G.length;G.push(J);e:for(;0<ee;){var je=ee-1>>>1,S=G[je];if(0<f(S,J))G[je]=J,G[ee]=S,ee=je;else break e}}function u(G){return G.length===0?null:G[0]}function c(G){if(G.length===0)return null;var J=G[0],ee=G.pop();if(ee!==J){G[0]=ee;e:for(var je=0,S=G.length,Q=S>>>1;je<Q;){var F=2*(je+1)-1,Z=G[F],ae=F+1,xe=G[ae];if(0>f(Z,ee))ae<S&&0>f(xe,Z)?(G[je]=xe,G[ae]=ee,je=ae):(G[je]=Z,G[F]=ee,je=F);else if(ae<S&&0>f(xe,ee))G[je]=xe,G[ae]=ee,je=ae;else break e}}return J}function f(G,J){var ee=G.sortIndex-J.sortIndex;return ee!==0?ee:G.id-J.id}if(l.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;l.unstable_now=function(){return d.now()}}else{var m=Date,x=m.now();l.unstable_now=function(){return m.now()-x}}var y=[],p=[],v=1,b=null,O=3,H=!1,U=!1,E=!1,M=!1,R=typeof setTimeout=="function"?setTimeout:null,D=typeof clearTimeout=="function"?clearTimeout:null,w=typeof setImmediate<"u"?setImmediate:null;function q(G){for(var J=u(p);J!==null;){if(J.callback===null)c(p);else if(J.startTime<=G)c(p),J.sortIndex=J.expirationTime,r(y,J);else break;J=u(p)}}function C(G){if(E=!1,q(G),!U)if(u(y)!==null)U=!0,W||(W=!0,ie());else{var J=u(p);J!==null&&Ue(C,J.startTime-G)}}var W=!1,$=-1,K=5,oe=-1;function Ce(){return M?!0:!(l.unstable_now()-oe<K)}function be(){if(M=!1,W){var G=l.unstable_now();oe=G;var J=!0;try{e:{U=!1,E&&(E=!1,D($),$=-1),H=!0;var ee=O;try{t:{for(q(G),b=u(y);b!==null&&!(b.expirationTime>G&&Ce());){var je=b.callback;if(typeof je=="function"){b.callback=null,O=b.priorityLevel;var S=je(b.expirationTime<=G);if(G=l.unstable_now(),typeof S=="function"){b.callback=S,q(G),J=!0;break t}b===u(y)&&c(y),q(G)}else c(y);b=u(y)}if(b!==null)J=!0;else{var Q=u(p);Q!==null&&Ue(C,Q.startTime-G),J=!1}}break e}finally{b=null,O=ee,H=!1}J=void 0}}finally{J?ie():W=!1}}}var ie;if(typeof w=="function")ie=function(){w(be)};else if(typeof MessageChannel<"u"){var rt=new MessageChannel,Ze=rt.port2;rt.port1.onmessage=be,ie=function(){Ze.postMessage(null)}}else ie=function(){R(be,0)};function Ue(G,J){$=R(function(){G(l.unstable_now())},J)}l.unstable_IdlePriority=5,l.unstable_ImmediatePriority=1,l.unstable_LowPriority=4,l.unstable_NormalPriority=3,l.unstable_Profiling=null,l.unstable_UserBlockingPriority=2,l.unstable_cancelCallback=function(G){G.callback=null},l.unstable_forceFrameRate=function(G){0>G||125<G?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):K=0<G?Math.floor(1e3/G):5},l.unstable_getCurrentPriorityLevel=function(){return O},l.unstable_next=function(G){switch(O){case 1:case 2:case 3:var J=3;break;default:J=O}var ee=O;O=J;try{return G()}finally{O=ee}},l.unstable_requestPaint=function(){M=!0},l.unstable_runWithPriority=function(G,J){switch(G){case 1:case 2:case 3:case 4:case 5:break;default:G=3}var ee=O;O=G;try{return J()}finally{O=ee}},l.unstable_scheduleCallback=function(G,J,ee){var je=l.unstable_now();switch(typeof ee=="object"&&ee!==null?(ee=ee.delay,ee=typeof ee=="number"&&0<ee?je+ee:je):ee=je,G){case 1:var S=-1;break;case 2:S=250;break;case 5:S=1073741823;break;case 4:S=1e4;break;default:S=5e3}return S=ee+S,G={id:v++,callback:J,priorityLevel:G,startTime:ee,expirationTime:S,sortIndex:-1},ee>je?(G.sortIndex=ee,r(p,G),u(y)===null&&G===u(p)&&(E?(D($),$=-1):E=!0,Ue(C,ee-je))):(G.sortIndex=S,r(y,G),U||H||(U=!0,W||(W=!0,ie()))),G},l.unstable_shouldYield=Ce,l.unstable_wrapCallback=function(G){var J=O;return function(){var ee=O;O=J;try{return G.apply(this,arguments)}finally{O=ee}}}}(ho)),ho}var Yh;function Pp(){return Yh||(Yh=1,fo.exports=$p()),fo.exports}var mo={exports:{}},ut={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xh;function Fp(){if(Xh)return ut;Xh=1;var l=Uo();function r(y){var p="https://react.dev/errors/"+y;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)p+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+y+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var c={d:{f:u,r:function(){throw Error(r(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},f=Symbol.for("react.portal");function d(y,p,v){var b=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:f,key:b==null?null:""+b,children:y,containerInfo:p,implementation:v}}var m=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function x(y,p){if(y==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return ut.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=c,ut.createPortal=function(y,p){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(r(299));return d(y,p,null,v)},ut.flushSync=function(y){var p=m.T,v=c.p;try{if(m.T=null,c.p=2,y)return y()}finally{m.T=p,c.p=v,c.d.f()}},ut.preconnect=function(y,p){typeof y=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,c.d.C(y,p))},ut.prefetchDNS=function(y){typeof y=="string"&&c.d.D(y)},ut.preinit=function(y,p){if(typeof y=="string"&&p&&typeof p.as=="string"){var v=p.as,b=x(v,p.crossOrigin),O=typeof p.integrity=="string"?p.integrity:void 0,H=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;v==="style"?c.d.S(y,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:b,integrity:O,fetchPriority:H}):v==="script"&&c.d.X(y,{crossOrigin:b,integrity:O,fetchPriority:H,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},ut.preinitModule=function(y,p){if(typeof y=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var v=x(p.as,p.crossOrigin);c.d.M(y,{crossOrigin:v,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&c.d.M(y)},ut.preload=function(y,p){if(typeof y=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var v=p.as,b=x(v,p.crossOrigin);c.d.L(y,v,{crossOrigin:b,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},ut.preloadModule=function(y,p){if(typeof y=="string")if(p){var v=x(p.as,p.crossOrigin);c.d.m(y,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:v,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else c.d.m(y)},ut.requestFormReset=function(y){c.d.r(y)},ut.unstable_batchedUpdates=function(y,p){return y(p)},ut.useFormState=function(y,p,v){return m.H.useFormState(y,p,v)},ut.useFormStatus=function(){return m.H.useHostTransitionStatus()},ut.version="19.1.0",ut}var Vh;function Em(){if(Vh)return mo.exports;Vh=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(r){console.error(r)}}return l(),mo.exports=Fp(),mo.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qh;function Wp(){if(Qh)return os;Qh=1;var l=Pp(),r=Uo(),u=Em();function c(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function d(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function m(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function x(e){if(d(e)!==e)throw Error(c(188))}function y(e){var t=e.alternate;if(!t){if(t=d(e),t===null)throw Error(c(188));return t!==e?null:e}for(var a=e,n=t;;){var i=a.return;if(i===null)break;var o=i.alternate;if(o===null){if(n=i.return,n!==null){a=n;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===a)return x(i),e;if(o===n)return x(i),t;o=o.sibling}throw Error(c(188))}if(a.return!==n.return)a=i,n=o;else{for(var h=!1,g=i.child;g;){if(g===a){h=!0,a=i,n=o;break}if(g===n){h=!0,n=i,a=o;break}g=g.sibling}if(!h){for(g=o.child;g;){if(g===a){h=!0,a=o,n=i;break}if(g===n){h=!0,n=o,a=i;break}g=g.sibling}if(!h)throw Error(c(189))}}if(a.alternate!==n)throw Error(c(190))}if(a.tag!==3)throw Error(c(188));return a.stateNode.current===a?e:t}function p(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=p(e),t!==null)return t;e=e.sibling}return null}var v=Object.assign,b=Symbol.for("react.element"),O=Symbol.for("react.transitional.element"),H=Symbol.for("react.portal"),U=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),M=Symbol.for("react.profiler"),R=Symbol.for("react.provider"),D=Symbol.for("react.consumer"),w=Symbol.for("react.context"),q=Symbol.for("react.forward_ref"),C=Symbol.for("react.suspense"),W=Symbol.for("react.suspense_list"),$=Symbol.for("react.memo"),K=Symbol.for("react.lazy"),oe=Symbol.for("react.activity"),Ce=Symbol.for("react.memo_cache_sentinel"),be=Symbol.iterator;function ie(e){return e===null||typeof e!="object"?null:(e=be&&e[be]||e["@@iterator"],typeof e=="function"?e:null)}var rt=Symbol.for("react.client.reference");function Ze(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===rt?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case U:return"Fragment";case M:return"Profiler";case E:return"StrictMode";case C:return"Suspense";case W:return"SuspenseList";case oe:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case H:return"Portal";case w:return(e.displayName||"Context")+".Provider";case D:return(e._context.displayName||"Context")+".Consumer";case q:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case $:return t=e.displayName||null,t!==null?t:Ze(e.type)||"Memo";case K:t=e._payload,e=e._init;try{return Ze(e(t))}catch{}}return null}var Ue=Array.isArray,G=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,J=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ee={pending:!1,data:null,method:null,action:null},je=[],S=-1;function Q(e){return{current:e}}function F(e){0>S||(e.current=je[S],je[S]=null,S--)}function Z(e,t){S++,je[S]=e.current,e.current=t}var ae=Q(null),xe=Q(null),re=Q(null),$e=Q(null);function ge(e,t){switch(Z(re,t),Z(xe,e),Z(ae,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?fh(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=fh(t),e=dh(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}F(ae),Z(ae,e)}function St(){F(ae),F(xe),F(re)}function na(e){e.memoizedState!==null&&Z($e,e);var t=ae.current,a=dh(t,e.type);t!==a&&(Z(xe,e),Z(ae,a))}function P(e){xe.current===e&&(F(ae),F(xe)),$e.current===e&&(F($e),ns._currentValue=ee)}var Ne=Object.prototype.hasOwnProperty,He=l.unstable_scheduleCallback,dt=l.unstable_cancelCallback,ne=l.unstable_shouldYield,Ee=l.unstable_requestPaint,_e=l.unstable_now,We=l.unstable_getCurrentPriorityLevel,Pt=l.unstable_ImmediatePriority,ht=l.unstable_UserBlockingPriority,la=l.unstable_NormalPriority,w0=l.unstable_LowPriority,Zo=l.unstable_IdlePriority,T0=l.log,E0=l.unstable_setDisableYieldValue,fl=null,wt=null;function Na(e){if(typeof T0=="function"&&E0(e),wt&&typeof wt.setStrictMode=="function")try{wt.setStrictMode(fl,e)}catch{}}var Tt=Math.clz32?Math.clz32:C0,_0=Math.log,O0=Math.LN2;function C0(e){return e>>>=0,e===0?32:31-(_0(e)/O0|0)|0}var bs=256,js=4194304;function $a(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Ns(e,t,a){var n=e.pendingLanes;if(n===0)return 0;var i=0,o=e.suspendedLanes,h=e.pingedLanes;e=e.warmLanes;var g=n&134217727;return g!==0?(n=g&~o,n!==0?i=$a(n):(h&=g,h!==0?i=$a(h):a||(a=g&~e,a!==0&&(i=$a(a))))):(g=n&~o,g!==0?i=$a(g):h!==0?i=$a(h):a||(a=n&~e,a!==0&&(i=$a(a)))),i===0?0:t!==0&&t!==i&&(t&o)===0&&(o=i&-i,a=t&-t,o>=a||o===32&&(a&4194048)!==0)?t:i}function dl(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function A0(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ko(){var e=bs;return bs<<=1,(bs&4194048)===0&&(bs=256),e}function Jo(){var e=js;return js<<=1,(js&62914560)===0&&(js=4194304),e}function Fi(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function hl(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function R0(e,t,a,n,i,o){var h=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var g=e.entanglements,j=e.expirationTimes,z=e.hiddenUpdates;for(a=h&~a;0<a;){var Y=31-Tt(a),V=1<<Y;g[Y]=0,j[Y]=-1;var B=z[Y];if(B!==null)for(z[Y]=null,Y=0;Y<B.length;Y++){var L=B[Y];L!==null&&(L.lane&=-536870913)}a&=~V}n!==0&&$o(e,n,0),o!==0&&i===0&&e.tag!==0&&(e.suspendedLanes|=o&~(h&~t))}function $o(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var n=31-Tt(t);e.entangledLanes|=t,e.entanglements[n]=e.entanglements[n]|1073741824|a&4194090}function Po(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var n=31-Tt(a),i=1<<n;i&t|e[n]&t&&(e[n]|=t),a&=~i}}function Wi(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Ii(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Fo(){var e=J.p;return e!==0?e:(e=window.event,e===void 0?32:Rh(e.type))}function M0(e,t){var a=J.p;try{return J.p=e,t()}finally{J.p=a}}var Sa=Math.random().toString(36).slice(2),ct="__reactFiber$"+Sa,pt="__reactProps$"+Sa,vn="__reactContainer$"+Sa,er="__reactEvents$"+Sa,z0="__reactListeners$"+Sa,D0="__reactHandles$"+Sa,Wo="__reactResources$"+Sa,ml="__reactMarker$"+Sa;function tr(e){delete e[ct],delete e[pt],delete e[er],delete e[z0],delete e[D0]}function bn(e){var t=e[ct];if(t)return t;for(var a=e.parentNode;a;){if(t=a[vn]||a[ct]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=ph(e);e!==null;){if(a=e[ct])return a;e=ph(e)}return t}e=a,a=e.parentNode}return null}function jn(e){if(e=e[ct]||e[vn]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function gl(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(c(33))}function Nn(e){var t=e[Wo];return t||(t=e[Wo]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ie(e){e[ml]=!0}var Io=new Set,eu={};function Pa(e,t){Sn(e,t),Sn(e+"Capture",t)}function Sn(e,t){for(eu[e]=t,e=0;e<t.length;e++)Io.add(t[e])}var U0=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),tu={},au={};function B0(e){return Ne.call(au,e)?!0:Ne.call(tu,e)?!1:U0.test(e)?au[e]=!0:(tu[e]=!0,!1)}function Ss(e,t,a){if(B0(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var n=t.toLowerCase().slice(0,5);if(n!=="data-"&&n!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function ws(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function sa(e,t,a,n){if(n===null)e.removeAttribute(a);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+n)}}var ar,nu;function wn(e){if(ar===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);ar=t&&t[1]||"",nu=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+ar+e+nu}var nr=!1;function lr(e,t){if(!e||nr)return"";nr=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var n={DetermineComponentFrameRoot:function(){try{if(t){var V=function(){throw Error()};if(Object.defineProperty(V.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(V,[])}catch(L){var B=L}Reflect.construct(e,[],V)}else{try{V.call()}catch(L){B=L}e.call(V.prototype)}}else{try{throw Error()}catch(L){B=L}(V=e())&&typeof V.catch=="function"&&V.catch(function(){})}}catch(L){if(L&&B&&typeof L.stack=="string")return[L.stack,B.stack]}return[null,null]}};n.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(n.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(n.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var o=n.DetermineComponentFrameRoot(),h=o[0],g=o[1];if(h&&g){var j=h.split(`
`),z=g.split(`
`);for(i=n=0;n<j.length&&!j[n].includes("DetermineComponentFrameRoot");)n++;for(;i<z.length&&!z[i].includes("DetermineComponentFrameRoot");)i++;if(n===j.length||i===z.length)for(n=j.length-1,i=z.length-1;1<=n&&0<=i&&j[n]!==z[i];)i--;for(;1<=n&&0<=i;n--,i--)if(j[n]!==z[i]){if(n!==1||i!==1)do if(n--,i--,0>i||j[n]!==z[i]){var Y=`
`+j[n].replace(" at new "," at ");return e.displayName&&Y.includes("<anonymous>")&&(Y=Y.replace("<anonymous>",e.displayName)),Y}while(1<=n&&0<=i);break}}}finally{nr=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?wn(a):""}function L0(e){switch(e.tag){case 26:case 27:case 5:return wn(e.type);case 16:return wn("Lazy");case 13:return wn("Suspense");case 19:return wn("SuspenseList");case 0:case 15:return lr(e.type,!1);case 11:return lr(e.type.render,!1);case 1:return lr(e.type,!0);case 31:return wn("Activity");default:return""}}function lu(e){try{var t="";do t+=L0(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Ut(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function su(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function H0(e){var t=su(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var i=a.get,o=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(h){n=""+h,o.call(this,h)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return n},setValue:function(h){n=""+h},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ts(e){e._valueTracker||(e._valueTracker=H0(e))}function iu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),n="";return e&&(n=su(e)?e.checked?"true":"false":e.value),e=n,e!==a?(t.setValue(e),!0):!1}function Es(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var k0=/[\n"\\]/g;function Bt(e){return e.replace(k0,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function sr(e,t,a,n,i,o,h,g){e.name="",h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?e.type=h:e.removeAttribute("type"),t!=null?h==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Ut(t)):e.value!==""+Ut(t)&&(e.value=""+Ut(t)):h!=="submit"&&h!=="reset"||e.removeAttribute("value"),t!=null?ir(e,h,Ut(t)):a!=null?ir(e,h,Ut(a)):n!=null&&e.removeAttribute("value"),i==null&&o!=null&&(e.defaultChecked=!!o),i!=null&&(e.checked=i&&typeof i!="function"&&typeof i!="symbol"),g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"?e.name=""+Ut(g):e.removeAttribute("name")}function ru(e,t,a,n,i,o,h,g){if(o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(e.type=o),t!=null||a!=null){if(!(o!=="submit"&&o!=="reset"||t!=null))return;a=a!=null?""+Ut(a):"",t=t!=null?""+Ut(t):a,g||t===e.value||(e.value=t),e.defaultValue=t}n=n??i,n=typeof n!="function"&&typeof n!="symbol"&&!!n,e.checked=g?e.checked:!!n,e.defaultChecked=!!n,h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"&&(e.name=h)}function ir(e,t,a){t==="number"&&Es(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function Tn(e,t,a,n){if(e=e.options,t){t={};for(var i=0;i<a.length;i++)t["$"+a[i]]=!0;for(a=0;a<e.length;a++)i=t.hasOwnProperty("$"+e[a].value),e[a].selected!==i&&(e[a].selected=i),i&&n&&(e[a].defaultSelected=!0)}else{for(a=""+Ut(a),t=null,i=0;i<e.length;i++){if(e[i].value===a){e[i].selected=!0,n&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function cu(e,t,a){if(t!=null&&(t=""+Ut(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+Ut(a):""}function ou(e,t,a,n){if(t==null){if(n!=null){if(a!=null)throw Error(c(92));if(Ue(n)){if(1<n.length)throw Error(c(93));n=n[0]}a=n}a==null&&(a=""),t=a}a=Ut(t),e.defaultValue=a,n=e.textContent,n===a&&n!==""&&n!==null&&(e.value=n)}function En(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var q0=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function uu(e,t,a){var n=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?n?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":n?e.setProperty(t,a):typeof a!="number"||a===0||q0.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function fu(e,t,a){if(t!=null&&typeof t!="object")throw Error(c(62));if(e=e.style,a!=null){for(var n in a)!a.hasOwnProperty(n)||t!=null&&t.hasOwnProperty(n)||(n.indexOf("--")===0?e.setProperty(n,""):n==="float"?e.cssFloat="":e[n]="");for(var i in t)n=t[i],t.hasOwnProperty(i)&&a[i]!==n&&uu(e,i,n)}else for(var o in t)t.hasOwnProperty(o)&&uu(e,o,t[o])}function rr(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var G0=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Y0=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function _s(e){return Y0.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var cr=null;function or(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var _n=null,On=null;function du(e){var t=jn(e);if(t&&(e=t.stateNode)){var a=e[pt]||null;e:switch(e=t.stateNode,t.type){case"input":if(sr(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Bt(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var n=a[t];if(n!==e&&n.form===e.form){var i=n[pt]||null;if(!i)throw Error(c(90));sr(n,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<a.length;t++)n=a[t],n.form===e.form&&iu(n)}break e;case"textarea":cu(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&Tn(e,!!a.multiple,t,!1)}}}var ur=!1;function hu(e,t,a){if(ur)return e(t,a);ur=!0;try{var n=e(t);return n}finally{if(ur=!1,(_n!==null||On!==null)&&(di(),_n&&(t=_n,e=On,On=_n=null,du(t),e)))for(t=0;t<e.length;t++)du(e[t])}}function pl(e,t){var a=e.stateNode;if(a===null)return null;var n=a[pt]||null;if(n===null)return null;a=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(e=e.type,n=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!n;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(c(231,t,typeof a));return a}var ia=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),fr=!1;if(ia)try{var xl={};Object.defineProperty(xl,"passive",{get:function(){fr=!0}}),window.addEventListener("test",xl,xl),window.removeEventListener("test",xl,xl)}catch{fr=!1}var wa=null,dr=null,Os=null;function mu(){if(Os)return Os;var e,t=dr,a=t.length,n,i="value"in wa?wa.value:wa.textContent,o=i.length;for(e=0;e<a&&t[e]===i[e];e++);var h=a-e;for(n=1;n<=h&&t[a-n]===i[o-n];n++);return Os=i.slice(e,1<n?1-n:void 0)}function Cs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function As(){return!0}function gu(){return!1}function xt(e){function t(a,n,i,o,h){this._reactName=a,this._targetInst=i,this.type=n,this.nativeEvent=o,this.target=h,this.currentTarget=null;for(var g in e)e.hasOwnProperty(g)&&(a=e[g],this[g]=a?a(o):o[g]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?As:gu,this.isPropagationStopped=gu,this}return v(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=As)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=As)},persist:function(){},isPersistent:As}),t}var Fa={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Rs=xt(Fa),yl=v({},Fa,{view:0,detail:0}),X0=xt(yl),hr,mr,vl,Ms=v({},yl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:pr,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==vl&&(vl&&e.type==="mousemove"?(hr=e.screenX-vl.screenX,mr=e.screenY-vl.screenY):mr=hr=0,vl=e),hr)},movementY:function(e){return"movementY"in e?e.movementY:mr}}),pu=xt(Ms),V0=v({},Ms,{dataTransfer:0}),Q0=xt(V0),Z0=v({},yl,{relatedTarget:0}),gr=xt(Z0),K0=v({},Fa,{animationName:0,elapsedTime:0,pseudoElement:0}),J0=xt(K0),$0=v({},Fa,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),P0=xt($0),F0=v({},Fa,{data:0}),xu=xt(F0),W0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},I0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},eg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function tg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=eg[e])?!!t[e]:!1}function pr(){return tg}var ag=v({},yl,{key:function(e){if(e.key){var t=W0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Cs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?I0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:pr,charCode:function(e){return e.type==="keypress"?Cs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Cs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ng=xt(ag),lg=v({},Ms,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),yu=xt(lg),sg=v({},yl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:pr}),ig=xt(sg),rg=v({},Fa,{propertyName:0,elapsedTime:0,pseudoElement:0}),cg=xt(rg),og=v({},Ms,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ug=xt(og),fg=v({},Fa,{newState:0,oldState:0}),dg=xt(fg),hg=[9,13,27,32],xr=ia&&"CompositionEvent"in window,bl=null;ia&&"documentMode"in document&&(bl=document.documentMode);var mg=ia&&"TextEvent"in window&&!bl,vu=ia&&(!xr||bl&&8<bl&&11>=bl),bu=" ",ju=!1;function Nu(e,t){switch(e){case"keyup":return hg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Su(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Cn=!1;function gg(e,t){switch(e){case"compositionend":return Su(t);case"keypress":return t.which!==32?null:(ju=!0,bu);case"textInput":return e=t.data,e===bu&&ju?null:e;default:return null}}function pg(e,t){if(Cn)return e==="compositionend"||!xr&&Nu(e,t)?(e=mu(),Os=dr=wa=null,Cn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return vu&&t.locale!=="ko"?null:t.data;default:return null}}var xg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function wu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!xg[e.type]:t==="textarea"}function Tu(e,t,a,n){_n?On?On.push(n):On=[n]:_n=n,t=yi(t,"onChange"),0<t.length&&(a=new Rs("onChange","change",null,a,n),e.push({event:a,listeners:t}))}var jl=null,Nl=null;function yg(e){ih(e,0)}function zs(e){var t=gl(e);if(iu(t))return e}function Eu(e,t){if(e==="change")return t}var _u=!1;if(ia){var yr;if(ia){var vr="oninput"in document;if(!vr){var Ou=document.createElement("div");Ou.setAttribute("oninput","return;"),vr=typeof Ou.oninput=="function"}yr=vr}else yr=!1;_u=yr&&(!document.documentMode||9<document.documentMode)}function Cu(){jl&&(jl.detachEvent("onpropertychange",Au),Nl=jl=null)}function Au(e){if(e.propertyName==="value"&&zs(Nl)){var t=[];Tu(t,Nl,e,or(e)),hu(yg,t)}}function vg(e,t,a){e==="focusin"?(Cu(),jl=t,Nl=a,jl.attachEvent("onpropertychange",Au)):e==="focusout"&&Cu()}function bg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return zs(Nl)}function jg(e,t){if(e==="click")return zs(t)}function Ng(e,t){if(e==="input"||e==="change")return zs(t)}function Sg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Et=typeof Object.is=="function"?Object.is:Sg;function Sl(e,t){if(Et(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),n=Object.keys(t);if(a.length!==n.length)return!1;for(n=0;n<a.length;n++){var i=a[n];if(!Ne.call(t,i)||!Et(e[i],t[i]))return!1}return!0}function Ru(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Mu(e,t){var a=Ru(e);e=0;for(var n;a;){if(a.nodeType===3){if(n=e+a.textContent.length,e<=t&&n>=t)return{node:a,offset:t-e};e=n}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=Ru(a)}}function zu(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?zu(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Du(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Es(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=Es(e.document)}return t}function br(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var wg=ia&&"documentMode"in document&&11>=document.documentMode,An=null,jr=null,wl=null,Nr=!1;function Uu(e,t,a){var n=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;Nr||An==null||An!==Es(n)||(n=An,"selectionStart"in n&&br(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),wl&&Sl(wl,n)||(wl=n,n=yi(jr,"onSelect"),0<n.length&&(t=new Rs("onSelect","select",null,t,a),e.push({event:t,listeners:n}),t.target=An)))}function Wa(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var Rn={animationend:Wa("Animation","AnimationEnd"),animationiteration:Wa("Animation","AnimationIteration"),animationstart:Wa("Animation","AnimationStart"),transitionrun:Wa("Transition","TransitionRun"),transitionstart:Wa("Transition","TransitionStart"),transitioncancel:Wa("Transition","TransitionCancel"),transitionend:Wa("Transition","TransitionEnd")},Sr={},Bu={};ia&&(Bu=document.createElement("div").style,"AnimationEvent"in window||(delete Rn.animationend.animation,delete Rn.animationiteration.animation,delete Rn.animationstart.animation),"TransitionEvent"in window||delete Rn.transitionend.transition);function Ia(e){if(Sr[e])return Sr[e];if(!Rn[e])return e;var t=Rn[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in Bu)return Sr[e]=t[a];return e}var Lu=Ia("animationend"),Hu=Ia("animationiteration"),ku=Ia("animationstart"),Tg=Ia("transitionrun"),Eg=Ia("transitionstart"),_g=Ia("transitioncancel"),qu=Ia("transitionend"),Gu=new Map,wr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");wr.push("scrollEnd");function Vt(e,t){Gu.set(e,t),Pa(t,[e])}var Yu=new WeakMap;function Lt(e,t){if(typeof e=="object"&&e!==null){var a=Yu.get(e);return a!==void 0?a:(t={value:e,source:t,stack:lu(t)},Yu.set(e,t),t)}return{value:e,source:t,stack:lu(t)}}var Ht=[],Mn=0,Tr=0;function Ds(){for(var e=Mn,t=Tr=Mn=0;t<e;){var a=Ht[t];Ht[t++]=null;var n=Ht[t];Ht[t++]=null;var i=Ht[t];Ht[t++]=null;var o=Ht[t];if(Ht[t++]=null,n!==null&&i!==null){var h=n.pending;h===null?i.next=i:(i.next=h.next,h.next=i),n.pending=i}o!==0&&Xu(a,i,o)}}function Us(e,t,a,n){Ht[Mn++]=e,Ht[Mn++]=t,Ht[Mn++]=a,Ht[Mn++]=n,Tr|=n,e.lanes|=n,e=e.alternate,e!==null&&(e.lanes|=n)}function Er(e,t,a,n){return Us(e,t,a,n),Bs(e)}function zn(e,t){return Us(e,null,null,t),Bs(e)}function Xu(e,t,a){e.lanes|=a;var n=e.alternate;n!==null&&(n.lanes|=a);for(var i=!1,o=e.return;o!==null;)o.childLanes|=a,n=o.alternate,n!==null&&(n.childLanes|=a),o.tag===22&&(e=o.stateNode,e===null||e._visibility&1||(i=!0)),e=o,o=o.return;return e.tag===3?(o=e.stateNode,i&&t!==null&&(i=31-Tt(a),e=o.hiddenUpdates,n=e[i],n===null?e[i]=[t]:n.push(t),t.lane=a|536870912),o):null}function Bs(e){if(50<$l)throw $l=0,Mc=null,Error(c(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Dn={};function Og(e,t,a,n){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function _t(e,t,a,n){return new Og(e,t,a,n)}function _r(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ra(e,t){var a=e.alternate;return a===null?(a=_t(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function Vu(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ls(e,t,a,n,i,o){var h=0;if(n=e,typeof e=="function")_r(e)&&(h=1);else if(typeof e=="string")h=Ap(e,a,ae.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case oe:return e=_t(31,a,t,i),e.elementType=oe,e.lanes=o,e;case U:return en(a.children,i,o,t);case E:h=8,i|=24;break;case M:return e=_t(12,a,t,i|2),e.elementType=M,e.lanes=o,e;case C:return e=_t(13,a,t,i),e.elementType=C,e.lanes=o,e;case W:return e=_t(19,a,t,i),e.elementType=W,e.lanes=o,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case R:case w:h=10;break e;case D:h=9;break e;case q:h=11;break e;case $:h=14;break e;case K:h=16,n=null;break e}h=29,a=Error(c(130,e===null?"null":typeof e,"")),n=null}return t=_t(h,a,t,i),t.elementType=e,t.type=n,t.lanes=o,t}function en(e,t,a,n){return e=_t(7,e,n,t),e.lanes=a,e}function Or(e,t,a){return e=_t(6,e,null,t),e.lanes=a,e}function Cr(e,t,a){return t=_t(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Un=[],Bn=0,Hs=null,ks=0,kt=[],qt=0,tn=null,ca=1,oa="";function an(e,t){Un[Bn++]=ks,Un[Bn++]=Hs,Hs=e,ks=t}function Qu(e,t,a){kt[qt++]=ca,kt[qt++]=oa,kt[qt++]=tn,tn=e;var n=ca;e=oa;var i=32-Tt(n)-1;n&=~(1<<i),a+=1;var o=32-Tt(t)+i;if(30<o){var h=i-i%5;o=(n&(1<<h)-1).toString(32),n>>=h,i-=h,ca=1<<32-Tt(t)+i|a<<i|n,oa=o+e}else ca=1<<o|a<<i|n,oa=e}function Ar(e){e.return!==null&&(an(e,1),Qu(e,1,0))}function Rr(e){for(;e===Hs;)Hs=Un[--Bn],Un[Bn]=null,ks=Un[--Bn],Un[Bn]=null;for(;e===tn;)tn=kt[--qt],kt[qt]=null,oa=kt[--qt],kt[qt]=null,ca=kt[--qt],kt[qt]=null}var mt=null,Ge=null,Oe=!1,nn=null,Ft=!1,Mr=Error(c(519));function ln(e){var t=Error(c(418,""));throw _l(Lt(t,e)),Mr}function Zu(e){var t=e.stateNode,a=e.type,n=e.memoizedProps;switch(t[ct]=e,t[pt]=n,a){case"dialog":ve("cancel",t),ve("close",t);break;case"iframe":case"object":case"embed":ve("load",t);break;case"video":case"audio":for(a=0;a<Fl.length;a++)ve(Fl[a],t);break;case"source":ve("error",t);break;case"img":case"image":case"link":ve("error",t),ve("load",t);break;case"details":ve("toggle",t);break;case"input":ve("invalid",t),ru(t,n.value,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name,!0),Ts(t);break;case"select":ve("invalid",t);break;case"textarea":ve("invalid",t),ou(t,n.value,n.defaultValue,n.children),Ts(t)}a=n.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||n.suppressHydrationWarning===!0||uh(t.textContent,a)?(n.popover!=null&&(ve("beforetoggle",t),ve("toggle",t)),n.onScroll!=null&&ve("scroll",t),n.onScrollEnd!=null&&ve("scrollend",t),n.onClick!=null&&(t.onclick=vi),t=!0):t=!1,t||ln(e)}function Ku(e){for(mt=e.return;mt;)switch(mt.tag){case 5:case 13:Ft=!1;return;case 27:case 3:Ft=!0;return;default:mt=mt.return}}function Tl(e){if(e!==mt)return!1;if(!Oe)return Ku(e),Oe=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||Jc(e.type,e.memoizedProps)),a=!a),a&&Ge&&ln(e),Ku(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(c(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){Ge=Zt(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}Ge=null}}else t===27?(t=Ge,qa(e.type)?(e=Wc,Wc=null,Ge=e):Ge=t):Ge=mt?Zt(e.stateNode.nextSibling):null;return!0}function El(){Ge=mt=null,Oe=!1}function Ju(){var e=nn;return e!==null&&(bt===null?bt=e:bt.push.apply(bt,e),nn=null),e}function _l(e){nn===null?nn=[e]:nn.push(e)}var zr=Q(null),sn=null,ua=null;function Ta(e,t,a){Z(zr,t._currentValue),t._currentValue=a}function fa(e){e._currentValue=zr.current,F(zr)}function Dr(e,t,a){for(;e!==null;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,n!==null&&(n.childLanes|=t)):n!==null&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===a)break;e=e.return}}function Ur(e,t,a,n){var i=e.child;for(i!==null&&(i.return=e);i!==null;){var o=i.dependencies;if(o!==null){var h=i.child;o=o.firstContext;e:for(;o!==null;){var g=o;o=i;for(var j=0;j<t.length;j++)if(g.context===t[j]){o.lanes|=a,g=o.alternate,g!==null&&(g.lanes|=a),Dr(o.return,a,e),n||(h=null);break e}o=g.next}}else if(i.tag===18){if(h=i.return,h===null)throw Error(c(341));h.lanes|=a,o=h.alternate,o!==null&&(o.lanes|=a),Dr(h,a,e),h=null}else h=i.child;if(h!==null)h.return=i;else for(h=i;h!==null;){if(h===e){h=null;break}if(i=h.sibling,i!==null){i.return=h.return,h=i;break}h=h.return}i=h}}function Ol(e,t,a,n){e=null;for(var i=t,o=!1;i!==null;){if(!o){if((i.flags&524288)!==0)o=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var h=i.alternate;if(h===null)throw Error(c(387));if(h=h.memoizedProps,h!==null){var g=i.type;Et(i.pendingProps.value,h.value)||(e!==null?e.push(g):e=[g])}}else if(i===$e.current){if(h=i.alternate,h===null)throw Error(c(387));h.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(e!==null?e.push(ns):e=[ns])}i=i.return}e!==null&&Ur(t,e,a,n),t.flags|=262144}function qs(e){for(e=e.firstContext;e!==null;){if(!Et(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function rn(e){sn=e,ua=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function ot(e){return $u(sn,e)}function Gs(e,t){return sn===null&&rn(e),$u(e,t)}function $u(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},ua===null){if(e===null)throw Error(c(308));ua=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else ua=ua.next=t;return a}var Cg=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},Ag=l.unstable_scheduleCallback,Rg=l.unstable_NormalPriority,Pe={$$typeof:w,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Br(){return{controller:new Cg,data:new Map,refCount:0}}function Cl(e){e.refCount--,e.refCount===0&&Ag(Rg,function(){e.controller.abort()})}var Al=null,Lr=0,Ln=0,Hn=null;function Mg(e,t){if(Al===null){var a=Al=[];Lr=0,Ln=kc(),Hn={status:"pending",value:void 0,then:function(n){a.push(n)}}}return Lr++,t.then(Pu,Pu),t}function Pu(){if(--Lr===0&&Al!==null){Hn!==null&&(Hn.status="fulfilled");var e=Al;Al=null,Ln=0,Hn=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function zg(e,t){var a=[],n={status:"pending",value:null,reason:null,then:function(i){a.push(i)}};return e.then(function(){n.status="fulfilled",n.value=t;for(var i=0;i<a.length;i++)(0,a[i])(t)},function(i){for(n.status="rejected",n.reason=i,i=0;i<a.length;i++)(0,a[i])(void 0)}),n}var Fu=G.S;G.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Mg(e,t),Fu!==null&&Fu(e,t)};var cn=Q(null);function Hr(){var e=cn.current;return e!==null?e:Le.pooledCache}function Ys(e,t){t===null?Z(cn,cn.current):Z(cn,t.pool)}function Wu(){var e=Hr();return e===null?null:{parent:Pe._currentValue,pool:e}}var Rl=Error(c(460)),Iu=Error(c(474)),Xs=Error(c(542)),kr={then:function(){}};function ef(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Vs(){}function tf(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(Vs,Vs),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,nf(e),e;default:if(typeof t.status=="string")t.then(Vs,Vs);else{if(e=Le,e!==null&&100<e.shellSuspendCounter)throw Error(c(482));e=t,e.status="pending",e.then(function(n){if(t.status==="pending"){var i=t;i.status="fulfilled",i.value=n}},function(n){if(t.status==="pending"){var i=t;i.status="rejected",i.reason=n}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,nf(e),e}throw Ml=t,Rl}}var Ml=null;function af(){if(Ml===null)throw Error(c(459));var e=Ml;return Ml=null,e}function nf(e){if(e===Rl||e===Xs)throw Error(c(483))}var Ea=!1;function qr(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Gr(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function _a(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Oa(e,t,a){var n=e.updateQueue;if(n===null)return null;if(n=n.shared,(Ae&2)!==0){var i=n.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),n.pending=t,t=Bs(e),Xu(e,null,a),t}return Us(e,n,t,a),Bs(e)}function zl(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var n=t.lanes;n&=e.pendingLanes,a|=n,t.lanes=a,Po(e,a)}}function Yr(e,t){var a=e.updateQueue,n=e.alternate;if(n!==null&&(n=n.updateQueue,a===n)){var i=null,o=null;if(a=a.firstBaseUpdate,a!==null){do{var h={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};o===null?i=o=h:o=o.next=h,a=a.next}while(a!==null);o===null?i=o=t:o=o.next=t}else i=o=t;a={baseState:n.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:n.shared,callbacks:n.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var Xr=!1;function Dl(){if(Xr){var e=Hn;if(e!==null)throw e}}function Ul(e,t,a,n){Xr=!1;var i=e.updateQueue;Ea=!1;var o=i.firstBaseUpdate,h=i.lastBaseUpdate,g=i.shared.pending;if(g!==null){i.shared.pending=null;var j=g,z=j.next;j.next=null,h===null?o=z:h.next=z,h=j;var Y=e.alternate;Y!==null&&(Y=Y.updateQueue,g=Y.lastBaseUpdate,g!==h&&(g===null?Y.firstBaseUpdate=z:g.next=z,Y.lastBaseUpdate=j))}if(o!==null){var V=i.baseState;h=0,Y=z=j=null,g=o;do{var B=g.lane&-536870913,L=B!==g.lane;if(L?(Se&B)===B:(n&B)===B){B!==0&&B===Ln&&(Xr=!0),Y!==null&&(Y=Y.next={lane:0,tag:g.tag,payload:g.payload,callback:null,next:null});e:{var ce=e,le=g;B=t;var De=a;switch(le.tag){case 1:if(ce=le.payload,typeof ce=="function"){V=ce.call(De,V,B);break e}V=ce;break e;case 3:ce.flags=ce.flags&-65537|128;case 0:if(ce=le.payload,B=typeof ce=="function"?ce.call(De,V,B):ce,B==null)break e;V=v({},V,B);break e;case 2:Ea=!0}}B=g.callback,B!==null&&(e.flags|=64,L&&(e.flags|=8192),L=i.callbacks,L===null?i.callbacks=[B]:L.push(B))}else L={lane:B,tag:g.tag,payload:g.payload,callback:g.callback,next:null},Y===null?(z=Y=L,j=V):Y=Y.next=L,h|=B;if(g=g.next,g===null){if(g=i.shared.pending,g===null)break;L=g,g=L.next,L.next=null,i.lastBaseUpdate=L,i.shared.pending=null}}while(!0);Y===null&&(j=V),i.baseState=j,i.firstBaseUpdate=z,i.lastBaseUpdate=Y,o===null&&(i.shared.lanes=0),Ba|=h,e.lanes=h,e.memoizedState=V}}function lf(e,t){if(typeof e!="function")throw Error(c(191,e));e.call(t)}function sf(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)lf(a[e],t)}var kn=Q(null),Qs=Q(0);function rf(e,t){e=ya,Z(Qs,e),Z(kn,t),ya=e|t.baseLanes}function Vr(){Z(Qs,ya),Z(kn,kn.current)}function Qr(){ya=Qs.current,F(kn),F(Qs)}var Ca=0,he=null,Me=null,Ke=null,Zs=!1,qn=!1,on=!1,Ks=0,Bl=0,Gn=null,Dg=0;function Xe(){throw Error(c(321))}function Zr(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!Et(e[a],t[a]))return!1;return!0}function Kr(e,t,a,n,i,o){return Ca=o,he=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,G.H=e===null||e.memoizedState===null?Vf:Qf,on=!1,o=a(n,i),on=!1,qn&&(o=of(t,a,n,i)),cf(e),o}function cf(e){G.H=Is;var t=Me!==null&&Me.next!==null;if(Ca=0,Ke=Me=he=null,Zs=!1,Bl=0,Gn=null,t)throw Error(c(300));e===null||et||(e=e.dependencies,e!==null&&qs(e)&&(et=!0))}function of(e,t,a,n){he=e;var i=0;do{if(qn&&(Gn=null),Bl=0,qn=!1,25<=i)throw Error(c(301));if(i+=1,Ke=Me=null,e.updateQueue!=null){var o=e.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,o.memoCache!=null&&(o.memoCache.index=0)}G.H=Gg,o=t(a,n)}while(qn);return o}function Ug(){var e=G.H,t=e.useState()[0];return t=typeof t.then=="function"?Ll(t):t,e=e.useState()[0],(Me!==null?Me.memoizedState:null)!==e&&(he.flags|=1024),t}function Jr(){var e=Ks!==0;return Ks=0,e}function $r(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function Pr(e){if(Zs){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Zs=!1}Ca=0,Ke=Me=he=null,qn=!1,Bl=Ks=0,Gn=null}function yt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ke===null?he.memoizedState=Ke=e:Ke=Ke.next=e,Ke}function Je(){if(Me===null){var e=he.alternate;e=e!==null?e.memoizedState:null}else e=Me.next;var t=Ke===null?he.memoizedState:Ke.next;if(t!==null)Ke=t,Me=e;else{if(e===null)throw he.alternate===null?Error(c(467)):Error(c(310));Me=e,e={memoizedState:Me.memoizedState,baseState:Me.baseState,baseQueue:Me.baseQueue,queue:Me.queue,next:null},Ke===null?he.memoizedState=Ke=e:Ke=Ke.next=e}return Ke}function Fr(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Ll(e){var t=Bl;return Bl+=1,Gn===null&&(Gn=[]),e=tf(Gn,e,t),t=he,(Ke===null?t.memoizedState:Ke.next)===null&&(t=t.alternate,G.H=t===null||t.memoizedState===null?Vf:Qf),e}function Js(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Ll(e);if(e.$$typeof===w)return ot(e)}throw Error(c(438,String(e)))}function Wr(e){var t=null,a=he.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var n=he.alternate;n!==null&&(n=n.updateQueue,n!==null&&(n=n.memoCache,n!=null&&(t={data:n.data.map(function(i){return i.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Fr(),he.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),n=0;n<e;n++)a[n]=Ce;return t.index++,a}function da(e,t){return typeof t=="function"?t(e):t}function $s(e){var t=Je();return Ir(t,Me,e)}function Ir(e,t,a){var n=e.queue;if(n===null)throw Error(c(311));n.lastRenderedReducer=a;var i=e.baseQueue,o=n.pending;if(o!==null){if(i!==null){var h=i.next;i.next=o.next,o.next=h}t.baseQueue=i=o,n.pending=null}if(o=e.baseState,i===null)e.memoizedState=o;else{t=i.next;var g=h=null,j=null,z=t,Y=!1;do{var V=z.lane&-536870913;if(V!==z.lane?(Se&V)===V:(Ca&V)===V){var B=z.revertLane;if(B===0)j!==null&&(j=j.next={lane:0,revertLane:0,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null}),V===Ln&&(Y=!0);else if((Ca&B)===B){z=z.next,B===Ln&&(Y=!0);continue}else V={lane:0,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},j===null?(g=j=V,h=o):j=j.next=V,he.lanes|=B,Ba|=B;V=z.action,on&&a(o,V),o=z.hasEagerState?z.eagerState:a(o,V)}else B={lane:V,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},j===null?(g=j=B,h=o):j=j.next=B,he.lanes|=V,Ba|=V;z=z.next}while(z!==null&&z!==t);if(j===null?h=o:j.next=g,!Et(o,e.memoizedState)&&(et=!0,Y&&(a=Hn,a!==null)))throw a;e.memoizedState=o,e.baseState=h,e.baseQueue=j,n.lastRenderedState=o}return i===null&&(n.lanes=0),[e.memoizedState,n.dispatch]}function ec(e){var t=Je(),a=t.queue;if(a===null)throw Error(c(311));a.lastRenderedReducer=e;var n=a.dispatch,i=a.pending,o=t.memoizedState;if(i!==null){a.pending=null;var h=i=i.next;do o=e(o,h.action),h=h.next;while(h!==i);Et(o,t.memoizedState)||(et=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),a.lastRenderedState=o}return[o,n]}function uf(e,t,a){var n=he,i=Je(),o=Oe;if(o){if(a===void 0)throw Error(c(407));a=a()}else a=t();var h=!Et((Me||i).memoizedState,a);h&&(i.memoizedState=a,et=!0),i=i.queue;var g=hf.bind(null,n,i,e);if(Hl(2048,8,g,[e]),i.getSnapshot!==t||h||Ke!==null&&Ke.memoizedState.tag&1){if(n.flags|=2048,Yn(9,Ps(),df.bind(null,n,i,a,t),null),Le===null)throw Error(c(349));o||(Ca&124)!==0||ff(n,t,a)}return a}function ff(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=he.updateQueue,t===null?(t=Fr(),he.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function df(e,t,a,n){t.value=a,t.getSnapshot=n,mf(t)&&gf(e)}function hf(e,t,a){return a(function(){mf(t)&&gf(e)})}function mf(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!Et(e,a)}catch{return!0}}function gf(e){var t=zn(e,2);t!==null&&Mt(t,e,2)}function tc(e){var t=yt();if(typeof e=="function"){var a=e;if(e=a(),on){Na(!0);try{a()}finally{Na(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:da,lastRenderedState:e},t}function pf(e,t,a,n){return e.baseState=a,Ir(e,Me,typeof n=="function"?n:da)}function Bg(e,t,a,n,i){if(Ws(e))throw Error(c(485));if(e=t.action,e!==null){var o={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(h){o.listeners.push(h)}};G.T!==null?a(!0):o.isTransition=!1,n(o),a=t.pending,a===null?(o.next=t.pending=o,xf(t,o)):(o.next=a.next,t.pending=a.next=o)}}function xf(e,t){var a=t.action,n=t.payload,i=e.state;if(t.isTransition){var o=G.T,h={};G.T=h;try{var g=a(i,n),j=G.S;j!==null&&j(h,g),yf(e,t,g)}catch(z){ac(e,t,z)}finally{G.T=o}}else try{o=a(i,n),yf(e,t,o)}catch(z){ac(e,t,z)}}function yf(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(n){vf(e,t,n)},function(n){return ac(e,t,n)}):vf(e,t,a)}function vf(e,t,a){t.status="fulfilled",t.value=a,bf(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,xf(e,a)))}function ac(e,t,a){var n=e.pending;if(e.pending=null,n!==null){n=n.next;do t.status="rejected",t.reason=a,bf(t),t=t.next;while(t!==n)}e.action=null}function bf(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function jf(e,t){return t}function Nf(e,t){if(Oe){var a=Le.formState;if(a!==null){e:{var n=he;if(Oe){if(Ge){t:{for(var i=Ge,o=Ft;i.nodeType!==8;){if(!o){i=null;break t}if(i=Zt(i.nextSibling),i===null){i=null;break t}}o=i.data,i=o==="F!"||o==="F"?i:null}if(i){Ge=Zt(i.nextSibling),n=i.data==="F!";break e}}ln(n)}n=!1}n&&(t=a[0])}}return a=yt(),a.memoizedState=a.baseState=t,n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:jf,lastRenderedState:t},a.queue=n,a=Gf.bind(null,he,n),n.dispatch=a,n=tc(!1),o=rc.bind(null,he,!1,n.queue),n=yt(),i={state:t,dispatch:null,action:e,pending:null},n.queue=i,a=Bg.bind(null,he,i,o,a),i.dispatch=a,n.memoizedState=e,[t,a,!1]}function Sf(e){var t=Je();return wf(t,Me,e)}function wf(e,t,a){if(t=Ir(e,t,jf)[0],e=$s(da)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var n=Ll(t)}catch(h){throw h===Rl?Xs:h}else n=t;t=Je();var i=t.queue,o=i.dispatch;return a!==t.memoizedState&&(he.flags|=2048,Yn(9,Ps(),Lg.bind(null,i,a),null)),[n,o,e]}function Lg(e,t){e.action=t}function Tf(e){var t=Je(),a=Me;if(a!==null)return wf(t,a,e);Je(),t=t.memoizedState,a=Je();var n=a.queue.dispatch;return a.memoizedState=e,[t,n,!1]}function Yn(e,t,a,n){return e={tag:e,create:a,deps:n,inst:t,next:null},t=he.updateQueue,t===null&&(t=Fr(),he.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(n=a.next,a.next=e,e.next=n,t.lastEffect=e),e}function Ps(){return{destroy:void 0,resource:void 0}}function Ef(){return Je().memoizedState}function Fs(e,t,a,n){var i=yt();n=n===void 0?null:n,he.flags|=e,i.memoizedState=Yn(1|t,Ps(),a,n)}function Hl(e,t,a,n){var i=Je();n=n===void 0?null:n;var o=i.memoizedState.inst;Me!==null&&n!==null&&Zr(n,Me.memoizedState.deps)?i.memoizedState=Yn(t,o,a,n):(he.flags|=e,i.memoizedState=Yn(1|t,o,a,n))}function _f(e,t){Fs(8390656,8,e,t)}function Of(e,t){Hl(2048,8,e,t)}function Cf(e,t){return Hl(4,2,e,t)}function Af(e,t){return Hl(4,4,e,t)}function Rf(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Mf(e,t,a){a=a!=null?a.concat([e]):null,Hl(4,4,Rf.bind(null,t,e),a)}function nc(){}function zf(e,t){var a=Je();t=t===void 0?null:t;var n=a.memoizedState;return t!==null&&Zr(t,n[1])?n[0]:(a.memoizedState=[e,t],e)}function Df(e,t){var a=Je();t=t===void 0?null:t;var n=a.memoizedState;if(t!==null&&Zr(t,n[1]))return n[0];if(n=e(),on){Na(!0);try{e()}finally{Na(!1)}}return a.memoizedState=[n,t],n}function lc(e,t,a){return a===void 0||(Ca&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=Ld(),he.lanes|=e,Ba|=e,a)}function Uf(e,t,a,n){return Et(a,t)?a:kn.current!==null?(e=lc(e,a,n),Et(e,t)||(et=!0),e):(Ca&42)===0?(et=!0,e.memoizedState=a):(e=Ld(),he.lanes|=e,Ba|=e,t)}function Bf(e,t,a,n,i){var o=J.p;J.p=o!==0&&8>o?o:8;var h=G.T,g={};G.T=g,rc(e,!1,t,a);try{var j=i(),z=G.S;if(z!==null&&z(g,j),j!==null&&typeof j=="object"&&typeof j.then=="function"){var Y=zg(j,n);kl(e,t,Y,Rt(e))}else kl(e,t,n,Rt(e))}catch(V){kl(e,t,{then:function(){},status:"rejected",reason:V},Rt())}finally{J.p=o,G.T=h}}function Hg(){}function sc(e,t,a,n){if(e.tag!==5)throw Error(c(476));var i=Lf(e).queue;Bf(e,i,t,ee,a===null?Hg:function(){return Hf(e),a(n)})}function Lf(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:ee,baseState:ee,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:da,lastRenderedState:ee},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:da,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Hf(e){var t=Lf(e).next.queue;kl(e,t,{},Rt())}function ic(){return ot(ns)}function kf(){return Je().memoizedState}function qf(){return Je().memoizedState}function kg(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=Rt();e=_a(a);var n=Oa(t,e,a);n!==null&&(Mt(n,t,a),zl(n,t,a)),t={cache:Br()},e.payload=t;return}t=t.return}}function qg(e,t,a){var n=Rt();a={lane:n,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Ws(e)?Yf(t,a):(a=Er(e,t,a,n),a!==null&&(Mt(a,e,n),Xf(a,t,n)))}function Gf(e,t,a){var n=Rt();kl(e,t,a,n)}function kl(e,t,a,n){var i={lane:n,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Ws(e))Yf(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var h=t.lastRenderedState,g=o(h,a);if(i.hasEagerState=!0,i.eagerState=g,Et(g,h))return Us(e,t,i,0),Le===null&&Ds(),!1}catch{}finally{}if(a=Er(e,t,i,n),a!==null)return Mt(a,e,n),Xf(a,t,n),!0}return!1}function rc(e,t,a,n){if(n={lane:2,revertLane:kc(),action:n,hasEagerState:!1,eagerState:null,next:null},Ws(e)){if(t)throw Error(c(479))}else t=Er(e,a,n,2),t!==null&&Mt(t,e,2)}function Ws(e){var t=e.alternate;return e===he||t!==null&&t===he}function Yf(e,t){qn=Zs=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function Xf(e,t,a){if((a&4194048)!==0){var n=t.lanes;n&=e.pendingLanes,a|=n,t.lanes=a,Po(e,a)}}var Is={readContext:ot,use:Js,useCallback:Xe,useContext:Xe,useEffect:Xe,useImperativeHandle:Xe,useLayoutEffect:Xe,useInsertionEffect:Xe,useMemo:Xe,useReducer:Xe,useRef:Xe,useState:Xe,useDebugValue:Xe,useDeferredValue:Xe,useTransition:Xe,useSyncExternalStore:Xe,useId:Xe,useHostTransitionStatus:Xe,useFormState:Xe,useActionState:Xe,useOptimistic:Xe,useMemoCache:Xe,useCacheRefresh:Xe},Vf={readContext:ot,use:Js,useCallback:function(e,t){return yt().memoizedState=[e,t===void 0?null:t],e},useContext:ot,useEffect:_f,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Fs(4194308,4,Rf.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Fs(4194308,4,e,t)},useInsertionEffect:function(e,t){Fs(4,2,e,t)},useMemo:function(e,t){var a=yt();t=t===void 0?null:t;var n=e();if(on){Na(!0);try{e()}finally{Na(!1)}}return a.memoizedState=[n,t],n},useReducer:function(e,t,a){var n=yt();if(a!==void 0){var i=a(t);if(on){Na(!0);try{a(t)}finally{Na(!1)}}}else i=t;return n.memoizedState=n.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},n.queue=e,e=e.dispatch=qg.bind(null,he,e),[n.memoizedState,e]},useRef:function(e){var t=yt();return e={current:e},t.memoizedState=e},useState:function(e){e=tc(e);var t=e.queue,a=Gf.bind(null,he,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:nc,useDeferredValue:function(e,t){var a=yt();return lc(a,e,t)},useTransition:function(){var e=tc(!1);return e=Bf.bind(null,he,e.queue,!0,!1),yt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var n=he,i=yt();if(Oe){if(a===void 0)throw Error(c(407));a=a()}else{if(a=t(),Le===null)throw Error(c(349));(Se&124)!==0||ff(n,t,a)}i.memoizedState=a;var o={value:a,getSnapshot:t};return i.queue=o,_f(hf.bind(null,n,o,e),[e]),n.flags|=2048,Yn(9,Ps(),df.bind(null,n,o,a,t),null),a},useId:function(){var e=yt(),t=Le.identifierPrefix;if(Oe){var a=oa,n=ca;a=(n&~(1<<32-Tt(n)-1)).toString(32)+a,t="«"+t+"R"+a,a=Ks++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=Dg++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:ic,useFormState:Nf,useActionState:Nf,useOptimistic:function(e){var t=yt();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=rc.bind(null,he,!0,a),a.dispatch=t,[e,t]},useMemoCache:Wr,useCacheRefresh:function(){return yt().memoizedState=kg.bind(null,he)}},Qf={readContext:ot,use:Js,useCallback:zf,useContext:ot,useEffect:Of,useImperativeHandle:Mf,useInsertionEffect:Cf,useLayoutEffect:Af,useMemo:Df,useReducer:$s,useRef:Ef,useState:function(){return $s(da)},useDebugValue:nc,useDeferredValue:function(e,t){var a=Je();return Uf(a,Me.memoizedState,e,t)},useTransition:function(){var e=$s(da)[0],t=Je().memoizedState;return[typeof e=="boolean"?e:Ll(e),t]},useSyncExternalStore:uf,useId:kf,useHostTransitionStatus:ic,useFormState:Sf,useActionState:Sf,useOptimistic:function(e,t){var a=Je();return pf(a,Me,e,t)},useMemoCache:Wr,useCacheRefresh:qf},Gg={readContext:ot,use:Js,useCallback:zf,useContext:ot,useEffect:Of,useImperativeHandle:Mf,useInsertionEffect:Cf,useLayoutEffect:Af,useMemo:Df,useReducer:ec,useRef:Ef,useState:function(){return ec(da)},useDebugValue:nc,useDeferredValue:function(e,t){var a=Je();return Me===null?lc(a,e,t):Uf(a,Me.memoizedState,e,t)},useTransition:function(){var e=ec(da)[0],t=Je().memoizedState;return[typeof e=="boolean"?e:Ll(e),t]},useSyncExternalStore:uf,useId:kf,useHostTransitionStatus:ic,useFormState:Tf,useActionState:Tf,useOptimistic:function(e,t){var a=Je();return Me!==null?pf(a,Me,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:Wr,useCacheRefresh:qf},Xn=null,ql=0;function ei(e){var t=ql;return ql+=1,Xn===null&&(Xn=[]),tf(Xn,e,t)}function Gl(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function ti(e,t){throw t.$$typeof===b?Error(c(525)):(e=Object.prototype.toString.call(t),Error(c(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Zf(e){var t=e._init;return t(e._payload)}function Kf(e){function t(_,T){if(e){var A=_.deletions;A===null?(_.deletions=[T],_.flags|=16):A.push(T)}}function a(_,T){if(!e)return null;for(;T!==null;)t(_,T),T=T.sibling;return null}function n(_){for(var T=new Map;_!==null;)_.key!==null?T.set(_.key,_):T.set(_.index,_),_=_.sibling;return T}function i(_,T){return _=ra(_,T),_.index=0,_.sibling=null,_}function o(_,T,A){return _.index=A,e?(A=_.alternate,A!==null?(A=A.index,A<T?(_.flags|=67108866,T):A):(_.flags|=67108866,T)):(_.flags|=1048576,T)}function h(_){return e&&_.alternate===null&&(_.flags|=67108866),_}function g(_,T,A,X){return T===null||T.tag!==6?(T=Or(A,_.mode,X),T.return=_,T):(T=i(T,A),T.return=_,T)}function j(_,T,A,X){var I=A.type;return I===U?Y(_,T,A.props.children,X,A.key):T!==null&&(T.elementType===I||typeof I=="object"&&I!==null&&I.$$typeof===K&&Zf(I)===T.type)?(T=i(T,A.props),Gl(T,A),T.return=_,T):(T=Ls(A.type,A.key,A.props,null,_.mode,X),Gl(T,A),T.return=_,T)}function z(_,T,A,X){return T===null||T.tag!==4||T.stateNode.containerInfo!==A.containerInfo||T.stateNode.implementation!==A.implementation?(T=Cr(A,_.mode,X),T.return=_,T):(T=i(T,A.children||[]),T.return=_,T)}function Y(_,T,A,X,I){return T===null||T.tag!==7?(T=en(A,_.mode,X,I),T.return=_,T):(T=i(T,A),T.return=_,T)}function V(_,T,A){if(typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint")return T=Or(""+T,_.mode,A),T.return=_,T;if(typeof T=="object"&&T!==null){switch(T.$$typeof){case O:return A=Ls(T.type,T.key,T.props,null,_.mode,A),Gl(A,T),A.return=_,A;case H:return T=Cr(T,_.mode,A),T.return=_,T;case K:var X=T._init;return T=X(T._payload),V(_,T,A)}if(Ue(T)||ie(T))return T=en(T,_.mode,A,null),T.return=_,T;if(typeof T.then=="function")return V(_,ei(T),A);if(T.$$typeof===w)return V(_,Gs(_,T),A);ti(_,T)}return null}function B(_,T,A,X){var I=T!==null?T.key:null;if(typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint")return I!==null?null:g(_,T,""+A,X);if(typeof A=="object"&&A!==null){switch(A.$$typeof){case O:return A.key===I?j(_,T,A,X):null;case H:return A.key===I?z(_,T,A,X):null;case K:return I=A._init,A=I(A._payload),B(_,T,A,X)}if(Ue(A)||ie(A))return I!==null?null:Y(_,T,A,X,null);if(typeof A.then=="function")return B(_,T,ei(A),X);if(A.$$typeof===w)return B(_,T,Gs(_,A),X);ti(_,A)}return null}function L(_,T,A,X,I){if(typeof X=="string"&&X!==""||typeof X=="number"||typeof X=="bigint")return _=_.get(A)||null,g(T,_,""+X,I);if(typeof X=="object"&&X!==null){switch(X.$$typeof){case O:return _=_.get(X.key===null?A:X.key)||null,j(T,_,X,I);case H:return _=_.get(X.key===null?A:X.key)||null,z(T,_,X,I);case K:var pe=X._init;return X=pe(X._payload),L(_,T,A,X,I)}if(Ue(X)||ie(X))return _=_.get(A)||null,Y(T,_,X,I,null);if(typeof X.then=="function")return L(_,T,A,ei(X),I);if(X.$$typeof===w)return L(_,T,A,Gs(T,X),I);ti(T,X)}return null}function ce(_,T,A,X){for(var I=null,pe=null,te=T,se=T=0,at=null;te!==null&&se<A.length;se++){te.index>se?(at=te,te=null):at=te.sibling;var we=B(_,te,A[se],X);if(we===null){te===null&&(te=at);break}e&&te&&we.alternate===null&&t(_,te),T=o(we,T,se),pe===null?I=we:pe.sibling=we,pe=we,te=at}if(se===A.length)return a(_,te),Oe&&an(_,se),I;if(te===null){for(;se<A.length;se++)te=V(_,A[se],X),te!==null&&(T=o(te,T,se),pe===null?I=te:pe.sibling=te,pe=te);return Oe&&an(_,se),I}for(te=n(te);se<A.length;se++)at=L(te,_,se,A[se],X),at!==null&&(e&&at.alternate!==null&&te.delete(at.key===null?se:at.key),T=o(at,T,se),pe===null?I=at:pe.sibling=at,pe=at);return e&&te.forEach(function(Qa){return t(_,Qa)}),Oe&&an(_,se),I}function le(_,T,A,X){if(A==null)throw Error(c(151));for(var I=null,pe=null,te=T,se=T=0,at=null,we=A.next();te!==null&&!we.done;se++,we=A.next()){te.index>se?(at=te,te=null):at=te.sibling;var Qa=B(_,te,we.value,X);if(Qa===null){te===null&&(te=at);break}e&&te&&Qa.alternate===null&&t(_,te),T=o(Qa,T,se),pe===null?I=Qa:pe.sibling=Qa,pe=Qa,te=at}if(we.done)return a(_,te),Oe&&an(_,se),I;if(te===null){for(;!we.done;se++,we=A.next())we=V(_,we.value,X),we!==null&&(T=o(we,T,se),pe===null?I=we:pe.sibling=we,pe=we);return Oe&&an(_,se),I}for(te=n(te);!we.done;se++,we=A.next())we=L(te,_,se,we.value,X),we!==null&&(e&&we.alternate!==null&&te.delete(we.key===null?se:we.key),T=o(we,T,se),pe===null?I=we:pe.sibling=we,pe=we);return e&&te.forEach(function(Yp){return t(_,Yp)}),Oe&&an(_,se),I}function De(_,T,A,X){if(typeof A=="object"&&A!==null&&A.type===U&&A.key===null&&(A=A.props.children),typeof A=="object"&&A!==null){switch(A.$$typeof){case O:e:{for(var I=A.key;T!==null;){if(T.key===I){if(I=A.type,I===U){if(T.tag===7){a(_,T.sibling),X=i(T,A.props.children),X.return=_,_=X;break e}}else if(T.elementType===I||typeof I=="object"&&I!==null&&I.$$typeof===K&&Zf(I)===T.type){a(_,T.sibling),X=i(T,A.props),Gl(X,A),X.return=_,_=X;break e}a(_,T);break}else t(_,T);T=T.sibling}A.type===U?(X=en(A.props.children,_.mode,X,A.key),X.return=_,_=X):(X=Ls(A.type,A.key,A.props,null,_.mode,X),Gl(X,A),X.return=_,_=X)}return h(_);case H:e:{for(I=A.key;T!==null;){if(T.key===I)if(T.tag===4&&T.stateNode.containerInfo===A.containerInfo&&T.stateNode.implementation===A.implementation){a(_,T.sibling),X=i(T,A.children||[]),X.return=_,_=X;break e}else{a(_,T);break}else t(_,T);T=T.sibling}X=Cr(A,_.mode,X),X.return=_,_=X}return h(_);case K:return I=A._init,A=I(A._payload),De(_,T,A,X)}if(Ue(A))return ce(_,T,A,X);if(ie(A)){if(I=ie(A),typeof I!="function")throw Error(c(150));return A=I.call(A),le(_,T,A,X)}if(typeof A.then=="function")return De(_,T,ei(A),X);if(A.$$typeof===w)return De(_,T,Gs(_,A),X);ti(_,A)}return typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint"?(A=""+A,T!==null&&T.tag===6?(a(_,T.sibling),X=i(T,A),X.return=_,_=X):(a(_,T),X=Or(A,_.mode,X),X.return=_,_=X),h(_)):a(_,T)}return function(_,T,A,X){try{ql=0;var I=De(_,T,A,X);return Xn=null,I}catch(te){if(te===Rl||te===Xs)throw te;var pe=_t(29,te,null,_.mode);return pe.lanes=X,pe.return=_,pe}finally{}}}var Vn=Kf(!0),Jf=Kf(!1),Gt=Q(null),Wt=null;function Aa(e){var t=e.alternate;Z(Fe,Fe.current&1),Z(Gt,e),Wt===null&&(t===null||kn.current!==null||t.memoizedState!==null)&&(Wt=e)}function $f(e){if(e.tag===22){if(Z(Fe,Fe.current),Z(Gt,e),Wt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Wt=e)}}else Ra()}function Ra(){Z(Fe,Fe.current),Z(Gt,Gt.current)}function ha(e){F(Gt),Wt===e&&(Wt=null),F(Fe)}var Fe=Q(0);function ai(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Fc(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function cc(e,t,a,n){t=e.memoizedState,a=a(n,t),a=a==null?t:v({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var oc={enqueueSetState:function(e,t,a){e=e._reactInternals;var n=Rt(),i=_a(n);i.payload=t,a!=null&&(i.callback=a),t=Oa(e,i,n),t!==null&&(Mt(t,e,n),zl(t,e,n))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var n=Rt(),i=_a(n);i.tag=1,i.payload=t,a!=null&&(i.callback=a),t=Oa(e,i,n),t!==null&&(Mt(t,e,n),zl(t,e,n))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=Rt(),n=_a(a);n.tag=2,t!=null&&(n.callback=t),t=Oa(e,n,a),t!==null&&(Mt(t,e,a),zl(t,e,a))}};function Pf(e,t,a,n,i,o,h){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(n,o,h):t.prototype&&t.prototype.isPureReactComponent?!Sl(a,n)||!Sl(i,o):!0}function Ff(e,t,a,n){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,n),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,n),t.state!==e&&oc.enqueueReplaceState(t,t.state,null)}function un(e,t){var a=t;if("ref"in t){a={};for(var n in t)n!=="ref"&&(a[n]=t[n])}if(e=e.defaultProps){a===t&&(a=v({},a));for(var i in e)a[i]===void 0&&(a[i]=e[i])}return a}var ni=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Wf(e){ni(e)}function If(e){console.error(e)}function ed(e){ni(e)}function li(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(n){setTimeout(function(){throw n})}}function td(e,t,a){try{var n=e.onCaughtError;n(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function uc(e,t,a){return a=_a(a),a.tag=3,a.payload={element:null},a.callback=function(){li(e,t)},a}function ad(e){return e=_a(e),e.tag=3,e}function nd(e,t,a,n){var i=a.type.getDerivedStateFromError;if(typeof i=="function"){var o=n.value;e.payload=function(){return i(o)},e.callback=function(){td(t,a,n)}}var h=a.stateNode;h!==null&&typeof h.componentDidCatch=="function"&&(e.callback=function(){td(t,a,n),typeof i!="function"&&(La===null?La=new Set([this]):La.add(this));var g=n.stack;this.componentDidCatch(n.value,{componentStack:g!==null?g:""})})}function Yg(e,t,a,n,i){if(a.flags|=32768,n!==null&&typeof n=="object"&&typeof n.then=="function"){if(t=a.alternate,t!==null&&Ol(t,a,i,!0),a=Gt.current,a!==null){switch(a.tag){case 13:return Wt===null?Dc():a.alternate===null&&Ye===0&&(Ye=3),a.flags&=-257,a.flags|=65536,a.lanes=i,n===kr?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([n]):t.add(n),Bc(e,n,i)),!1;case 22:return a.flags|=65536,n===kr?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([n])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([n]):a.add(n)),Bc(e,n,i)),!1}throw Error(c(435,a.tag))}return Bc(e,n,i),Dc(),!1}if(Oe)return t=Gt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=i,n!==Mr&&(e=Error(c(422),{cause:n}),_l(Lt(e,a)))):(n!==Mr&&(t=Error(c(423),{cause:n}),_l(Lt(t,a))),e=e.current.alternate,e.flags|=65536,i&=-i,e.lanes|=i,n=Lt(n,a),i=uc(e.stateNode,n,i),Yr(e,i),Ye!==4&&(Ye=2)),!1;var o=Error(c(520),{cause:n});if(o=Lt(o,a),Jl===null?Jl=[o]:Jl.push(o),Ye!==4&&(Ye=2),t===null)return!0;n=Lt(n,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=i&-i,a.lanes|=e,e=uc(a.stateNode,n,e),Yr(a,e),!1;case 1:if(t=a.type,o=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||o!==null&&typeof o.componentDidCatch=="function"&&(La===null||!La.has(o))))return a.flags|=65536,i&=-i,a.lanes|=i,i=ad(i),nd(i,e,a,n),Yr(a,i),!1}a=a.return}while(a!==null);return!1}var ld=Error(c(461)),et=!1;function lt(e,t,a,n){t.child=e===null?Jf(t,null,a,n):Vn(t,e.child,a,n)}function sd(e,t,a,n,i){a=a.render;var o=t.ref;if("ref"in n){var h={};for(var g in n)g!=="ref"&&(h[g]=n[g])}else h=n;return rn(t),n=Kr(e,t,a,h,o,i),g=Jr(),e!==null&&!et?($r(e,t,i),ma(e,t,i)):(Oe&&g&&Ar(t),t.flags|=1,lt(e,t,n,i),t.child)}function id(e,t,a,n,i){if(e===null){var o=a.type;return typeof o=="function"&&!_r(o)&&o.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=o,rd(e,t,o,n,i)):(e=Ls(a.type,null,n,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!yc(e,i)){var h=o.memoizedProps;if(a=a.compare,a=a!==null?a:Sl,a(h,n)&&e.ref===t.ref)return ma(e,t,i)}return t.flags|=1,e=ra(o,n),e.ref=t.ref,e.return=t,t.child=e}function rd(e,t,a,n,i){if(e!==null){var o=e.memoizedProps;if(Sl(o,n)&&e.ref===t.ref)if(et=!1,t.pendingProps=n=o,yc(e,i))(e.flags&131072)!==0&&(et=!0);else return t.lanes=e.lanes,ma(e,t,i)}return fc(e,t,a,n,i)}function cd(e,t,a){var n=t.pendingProps,i=n.children,o=e!==null?e.memoizedState:null;if(n.mode==="hidden"){if((t.flags&128)!==0){if(n=o!==null?o.baseLanes|a:a,e!==null){for(i=t.child=e.child,o=0;i!==null;)o=o|i.lanes|i.childLanes,i=i.sibling;t.childLanes=o&~n}else t.childLanes=0,t.child=null;return od(e,t,n,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Ys(t,o!==null?o.cachePool:null),o!==null?rf(t,o):Vr(),$f(t);else return t.lanes=t.childLanes=536870912,od(e,t,o!==null?o.baseLanes|a:a,a)}else o!==null?(Ys(t,o.cachePool),rf(t,o),Ra(),t.memoizedState=null):(e!==null&&Ys(t,null),Vr(),Ra());return lt(e,t,i,a),t.child}function od(e,t,a,n){var i=Hr();return i=i===null?null:{parent:Pe._currentValue,pool:i},t.memoizedState={baseLanes:a,cachePool:i},e!==null&&Ys(t,null),Vr(),$f(t),e!==null&&Ol(e,t,n,!0),null}function si(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(c(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function fc(e,t,a,n,i){return rn(t),a=Kr(e,t,a,n,void 0,i),n=Jr(),e!==null&&!et?($r(e,t,i),ma(e,t,i)):(Oe&&n&&Ar(t),t.flags|=1,lt(e,t,a,i),t.child)}function ud(e,t,a,n,i,o){return rn(t),t.updateQueue=null,a=of(t,n,a,i),cf(e),n=Jr(),e!==null&&!et?($r(e,t,o),ma(e,t,o)):(Oe&&n&&Ar(t),t.flags|=1,lt(e,t,a,o),t.child)}function fd(e,t,a,n,i){if(rn(t),t.stateNode===null){var o=Dn,h=a.contextType;typeof h=="object"&&h!==null&&(o=ot(h)),o=new a(n,o),t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,o.updater=oc,t.stateNode=o,o._reactInternals=t,o=t.stateNode,o.props=n,o.state=t.memoizedState,o.refs={},qr(t),h=a.contextType,o.context=typeof h=="object"&&h!==null?ot(h):Dn,o.state=t.memoizedState,h=a.getDerivedStateFromProps,typeof h=="function"&&(cc(t,a,h,n),o.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(h=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),h!==o.state&&oc.enqueueReplaceState(o,o.state,null),Ul(t,n,o,i),Dl(),o.state=t.memoizedState),typeof o.componentDidMount=="function"&&(t.flags|=4194308),n=!0}else if(e===null){o=t.stateNode;var g=t.memoizedProps,j=un(a,g);o.props=j;var z=o.context,Y=a.contextType;h=Dn,typeof Y=="object"&&Y!==null&&(h=ot(Y));var V=a.getDerivedStateFromProps;Y=typeof V=="function"||typeof o.getSnapshotBeforeUpdate=="function",g=t.pendingProps!==g,Y||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(g||z!==h)&&Ff(t,o,n,h),Ea=!1;var B=t.memoizedState;o.state=B,Ul(t,n,o,i),Dl(),z=t.memoizedState,g||B!==z||Ea?(typeof V=="function"&&(cc(t,a,V,n),z=t.memoizedState),(j=Ea||Pf(t,a,j,n,B,z,h))?(Y||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=z),o.props=n,o.state=z,o.context=h,n=j):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),n=!1)}else{o=t.stateNode,Gr(e,t),h=t.memoizedProps,Y=un(a,h),o.props=Y,V=t.pendingProps,B=o.context,z=a.contextType,j=Dn,typeof z=="object"&&z!==null&&(j=ot(z)),g=a.getDerivedStateFromProps,(z=typeof g=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(h!==V||B!==j)&&Ff(t,o,n,j),Ea=!1,B=t.memoizedState,o.state=B,Ul(t,n,o,i),Dl();var L=t.memoizedState;h!==V||B!==L||Ea||e!==null&&e.dependencies!==null&&qs(e.dependencies)?(typeof g=="function"&&(cc(t,a,g,n),L=t.memoizedState),(Y=Ea||Pf(t,a,Y,n,B,L,j)||e!==null&&e.dependencies!==null&&qs(e.dependencies))?(z||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(n,L,j),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(n,L,j)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||h===e.memoizedProps&&B===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&B===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=L),o.props=n,o.state=L,o.context=j,n=Y):(typeof o.componentDidUpdate!="function"||h===e.memoizedProps&&B===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&B===e.memoizedState||(t.flags|=1024),n=!1)}return o=n,si(e,t),n=(t.flags&128)!==0,o||n?(o=t.stateNode,a=n&&typeof a.getDerivedStateFromError!="function"?null:o.render(),t.flags|=1,e!==null&&n?(t.child=Vn(t,e.child,null,i),t.child=Vn(t,null,a,i)):lt(e,t,a,i),t.memoizedState=o.state,e=t.child):e=ma(e,t,i),e}function dd(e,t,a,n){return El(),t.flags|=256,lt(e,t,a,n),t.child}var dc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function hc(e){return{baseLanes:e,cachePool:Wu()}}function mc(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=Yt),e}function hd(e,t,a){var n=t.pendingProps,i=!1,o=(t.flags&128)!==0,h;if((h=o)||(h=e!==null&&e.memoizedState===null?!1:(Fe.current&2)!==0),h&&(i=!0,t.flags&=-129),h=(t.flags&32)!==0,t.flags&=-33,e===null){if(Oe){if(i?Aa(t):Ra(),Oe){var g=Ge,j;if(j=g){e:{for(j=g,g=Ft;j.nodeType!==8;){if(!g){g=null;break e}if(j=Zt(j.nextSibling),j===null){g=null;break e}}g=j}g!==null?(t.memoizedState={dehydrated:g,treeContext:tn!==null?{id:ca,overflow:oa}:null,retryLane:536870912,hydrationErrors:null},j=_t(18,null,null,0),j.stateNode=g,j.return=t,t.child=j,mt=t,Ge=null,j=!0):j=!1}j||ln(t)}if(g=t.memoizedState,g!==null&&(g=g.dehydrated,g!==null))return Fc(g)?t.lanes=32:t.lanes=536870912,null;ha(t)}return g=n.children,n=n.fallback,i?(Ra(),i=t.mode,g=ii({mode:"hidden",children:g},i),n=en(n,i,a,null),g.return=t,n.return=t,g.sibling=n,t.child=g,i=t.child,i.memoizedState=hc(a),i.childLanes=mc(e,h,a),t.memoizedState=dc,n):(Aa(t),gc(t,g))}if(j=e.memoizedState,j!==null&&(g=j.dehydrated,g!==null)){if(o)t.flags&256?(Aa(t),t.flags&=-257,t=pc(e,t,a)):t.memoizedState!==null?(Ra(),t.child=e.child,t.flags|=128,t=null):(Ra(),i=n.fallback,g=t.mode,n=ii({mode:"visible",children:n.children},g),i=en(i,g,a,null),i.flags|=2,n.return=t,i.return=t,n.sibling=i,t.child=n,Vn(t,e.child,null,a),n=t.child,n.memoizedState=hc(a),n.childLanes=mc(e,h,a),t.memoizedState=dc,t=i);else if(Aa(t),Fc(g)){if(h=g.nextSibling&&g.nextSibling.dataset,h)var z=h.dgst;h=z,n=Error(c(419)),n.stack="",n.digest=h,_l({value:n,source:null,stack:null}),t=pc(e,t,a)}else if(et||Ol(e,t,a,!1),h=(a&e.childLanes)!==0,et||h){if(h=Le,h!==null&&(n=a&-a,n=(n&42)!==0?1:Wi(n),n=(n&(h.suspendedLanes|a))!==0?0:n,n!==0&&n!==j.retryLane))throw j.retryLane=n,zn(e,n),Mt(h,e,n),ld;g.data==="$?"||Dc(),t=pc(e,t,a)}else g.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=j.treeContext,Ge=Zt(g.nextSibling),mt=t,Oe=!0,nn=null,Ft=!1,e!==null&&(kt[qt++]=ca,kt[qt++]=oa,kt[qt++]=tn,ca=e.id,oa=e.overflow,tn=t),t=gc(t,n.children),t.flags|=4096);return t}return i?(Ra(),i=n.fallback,g=t.mode,j=e.child,z=j.sibling,n=ra(j,{mode:"hidden",children:n.children}),n.subtreeFlags=j.subtreeFlags&65011712,z!==null?i=ra(z,i):(i=en(i,g,a,null),i.flags|=2),i.return=t,n.return=t,n.sibling=i,t.child=n,n=i,i=t.child,g=e.child.memoizedState,g===null?g=hc(a):(j=g.cachePool,j!==null?(z=Pe._currentValue,j=j.parent!==z?{parent:z,pool:z}:j):j=Wu(),g={baseLanes:g.baseLanes|a,cachePool:j}),i.memoizedState=g,i.childLanes=mc(e,h,a),t.memoizedState=dc,n):(Aa(t),a=e.child,e=a.sibling,a=ra(a,{mode:"visible",children:n.children}),a.return=t,a.sibling=null,e!==null&&(h=t.deletions,h===null?(t.deletions=[e],t.flags|=16):h.push(e)),t.child=a,t.memoizedState=null,a)}function gc(e,t){return t=ii({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function ii(e,t){return e=_t(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function pc(e,t,a){return Vn(t,e.child,null,a),e=gc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function md(e,t,a){e.lanes|=t;var n=e.alternate;n!==null&&(n.lanes|=t),Dr(e.return,t,a)}function xc(e,t,a,n,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:a,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=n,o.tail=a,o.tailMode=i)}function gd(e,t,a){var n=t.pendingProps,i=n.revealOrder,o=n.tail;if(lt(e,t,n.children,a),n=Fe.current,(n&2)!==0)n=n&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&md(e,a,t);else if(e.tag===19)md(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}switch(Z(Fe,n),i){case"forwards":for(a=t.child,i=null;a!==null;)e=a.alternate,e!==null&&ai(e)===null&&(i=a),a=a.sibling;a=i,a===null?(i=t.child,t.child=null):(i=a.sibling,a.sibling=null),xc(t,!1,i,a,o);break;case"backwards":for(a=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&ai(e)===null){t.child=i;break}e=i.sibling,i.sibling=a,a=i,i=e}xc(t,!0,a,null,o);break;case"together":xc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ma(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),Ba|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(Ol(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(c(153));if(t.child!==null){for(e=t.child,a=ra(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=ra(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function yc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&qs(e)))}function Xg(e,t,a){switch(t.tag){case 3:ge(t,t.stateNode.containerInfo),Ta(t,Pe,e.memoizedState.cache),El();break;case 27:case 5:na(t);break;case 4:ge(t,t.stateNode.containerInfo);break;case 10:Ta(t,t.type,t.memoizedProps.value);break;case 13:var n=t.memoizedState;if(n!==null)return n.dehydrated!==null?(Aa(t),t.flags|=128,null):(a&t.child.childLanes)!==0?hd(e,t,a):(Aa(t),e=ma(e,t,a),e!==null?e.sibling:null);Aa(t);break;case 19:var i=(e.flags&128)!==0;if(n=(a&t.childLanes)!==0,n||(Ol(e,t,a,!1),n=(a&t.childLanes)!==0),i){if(n)return gd(e,t,a);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),Z(Fe,Fe.current),n)break;return null;case 22:case 23:return t.lanes=0,cd(e,t,a);case 24:Ta(t,Pe,e.memoizedState.cache)}return ma(e,t,a)}function pd(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)et=!0;else{if(!yc(e,a)&&(t.flags&128)===0)return et=!1,Xg(e,t,a);et=(e.flags&131072)!==0}else et=!1,Oe&&(t.flags&1048576)!==0&&Qu(t,ks,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var n=t.elementType,i=n._init;if(n=i(n._payload),t.type=n,typeof n=="function")_r(n)?(e=un(n,e),t.tag=1,t=fd(null,t,n,e,a)):(t.tag=0,t=fc(null,t,n,e,a));else{if(n!=null){if(i=n.$$typeof,i===q){t.tag=11,t=sd(null,t,n,e,a);break e}else if(i===$){t.tag=14,t=id(null,t,n,e,a);break e}}throw t=Ze(n)||n,Error(c(306,t,""))}}return t;case 0:return fc(e,t,t.type,t.pendingProps,a);case 1:return n=t.type,i=un(n,t.pendingProps),fd(e,t,n,i,a);case 3:e:{if(ge(t,t.stateNode.containerInfo),e===null)throw Error(c(387));n=t.pendingProps;var o=t.memoizedState;i=o.element,Gr(e,t),Ul(t,n,null,a);var h=t.memoizedState;if(n=h.cache,Ta(t,Pe,n),n!==o.cache&&Ur(t,[Pe],a,!0),Dl(),n=h.element,o.isDehydrated)if(o={element:n,isDehydrated:!1,cache:h.cache},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){t=dd(e,t,n,a);break e}else if(n!==i){i=Lt(Error(c(424)),t),_l(i),t=dd(e,t,n,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ge=Zt(e.firstChild),mt=t,Oe=!0,nn=null,Ft=!0,a=Jf(t,null,n,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(El(),n===i){t=ma(e,t,a);break e}lt(e,t,n,a)}t=t.child}return t;case 26:return si(e,t),e===null?(a=bh(t.type,null,t.pendingProps,null))?t.memoizedState=a:Oe||(a=t.type,e=t.pendingProps,n=bi(re.current).createElement(a),n[ct]=t,n[pt]=e,it(n,a,e),Ie(n),t.stateNode=n):t.memoizedState=bh(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return na(t),e===null&&Oe&&(n=t.stateNode=xh(t.type,t.pendingProps,re.current),mt=t,Ft=!0,i=Ge,qa(t.type)?(Wc=i,Ge=Zt(n.firstChild)):Ge=i),lt(e,t,t.pendingProps.children,a),si(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Oe&&((i=n=Ge)&&(n=xp(n,t.type,t.pendingProps,Ft),n!==null?(t.stateNode=n,mt=t,Ge=Zt(n.firstChild),Ft=!1,i=!0):i=!1),i||ln(t)),na(t),i=t.type,o=t.pendingProps,h=e!==null?e.memoizedProps:null,n=o.children,Jc(i,o)?n=null:h!==null&&Jc(i,h)&&(t.flags|=32),t.memoizedState!==null&&(i=Kr(e,t,Ug,null,null,a),ns._currentValue=i),si(e,t),lt(e,t,n,a),t.child;case 6:return e===null&&Oe&&((e=a=Ge)&&(a=yp(a,t.pendingProps,Ft),a!==null?(t.stateNode=a,mt=t,Ge=null,e=!0):e=!1),e||ln(t)),null;case 13:return hd(e,t,a);case 4:return ge(t,t.stateNode.containerInfo),n=t.pendingProps,e===null?t.child=Vn(t,null,n,a):lt(e,t,n,a),t.child;case 11:return sd(e,t,t.type,t.pendingProps,a);case 7:return lt(e,t,t.pendingProps,a),t.child;case 8:return lt(e,t,t.pendingProps.children,a),t.child;case 12:return lt(e,t,t.pendingProps.children,a),t.child;case 10:return n=t.pendingProps,Ta(t,t.type,n.value),lt(e,t,n.children,a),t.child;case 9:return i=t.type._context,n=t.pendingProps.children,rn(t),i=ot(i),n=n(i),t.flags|=1,lt(e,t,n,a),t.child;case 14:return id(e,t,t.type,t.pendingProps,a);case 15:return rd(e,t,t.type,t.pendingProps,a);case 19:return gd(e,t,a);case 31:return n=t.pendingProps,a=t.mode,n={mode:n.mode,children:n.children},e===null?(a=ii(n,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=ra(e.child,n),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return cd(e,t,a);case 24:return rn(t),n=ot(Pe),e===null?(i=Hr(),i===null&&(i=Le,o=Br(),i.pooledCache=o,o.refCount++,o!==null&&(i.pooledCacheLanes|=a),i=o),t.memoizedState={parent:n,cache:i},qr(t),Ta(t,Pe,i)):((e.lanes&a)!==0&&(Gr(e,t),Ul(t,null,null,a),Dl()),i=e.memoizedState,o=t.memoizedState,i.parent!==n?(i={parent:n,cache:n},t.memoizedState=i,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=i),Ta(t,Pe,n)):(n=o.cache,Ta(t,Pe,n),n!==i.cache&&Ur(t,[Pe],a,!0))),lt(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(c(156,t.tag))}function ga(e){e.flags|=4}function xd(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Th(t)){if(t=Gt.current,t!==null&&((Se&4194048)===Se?Wt!==null:(Se&62914560)!==Se&&(Se&536870912)===0||t!==Wt))throw Ml=kr,Iu;e.flags|=8192}}function ri(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Jo():536870912,e.lanes|=t,Jn|=t)}function Yl(e,t){if(!Oe)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var n=null;a!==null;)a.alternate!==null&&(n=a),a=a.sibling;n===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:n.sibling=null}}function qe(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,n=0;if(t)for(var i=e.child;i!==null;)a|=i.lanes|i.childLanes,n|=i.subtreeFlags&65011712,n|=i.flags&65011712,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)a|=i.lanes|i.childLanes,n|=i.subtreeFlags,n|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=n,e.childLanes=a,t}function Vg(e,t,a){var n=t.pendingProps;switch(Rr(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qe(t),null;case 1:return qe(t),null;case 3:return a=t.stateNode,n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),fa(Pe),St(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(Tl(t)?ga(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Ju())),qe(t),null;case 26:return a=t.memoizedState,e===null?(ga(t),a!==null?(qe(t),xd(t,a)):(qe(t),t.flags&=-16777217)):a?a!==e.memoizedState?(ga(t),qe(t),xd(t,a)):(qe(t),t.flags&=-16777217):(e.memoizedProps!==n&&ga(t),qe(t),t.flags&=-16777217),null;case 27:P(t),a=re.current;var i=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==n&&ga(t);else{if(!n){if(t.stateNode===null)throw Error(c(166));return qe(t),null}e=ae.current,Tl(t)?Zu(t):(e=xh(i,n,a),t.stateNode=e,ga(t))}return qe(t),null;case 5:if(P(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==n&&ga(t);else{if(!n){if(t.stateNode===null)throw Error(c(166));return qe(t),null}if(e=ae.current,Tl(t))Zu(t);else{switch(i=bi(re.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof n.is=="string"?i.createElement("select",{is:n.is}):i.createElement("select"),n.multiple?e.multiple=!0:n.size&&(e.size=n.size);break;default:e=typeof n.is=="string"?i.createElement(a,{is:n.is}):i.createElement(a)}}e[ct]=t,e[pt]=n;e:for(i=t.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;i.sibling===null;){if(i.return===null||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(it(e,a,n),a){case"button":case"input":case"select":case"textarea":e=!!n.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&ga(t)}}return qe(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==n&&ga(t);else{if(typeof n!="string"&&t.stateNode===null)throw Error(c(166));if(e=re.current,Tl(t)){if(e=t.stateNode,a=t.memoizedProps,n=null,i=mt,i!==null)switch(i.tag){case 27:case 5:n=i.memoizedProps}e[ct]=t,e=!!(e.nodeValue===a||n!==null&&n.suppressHydrationWarning===!0||uh(e.nodeValue,a)),e||ln(t)}else e=bi(e).createTextNode(n),e[ct]=t,t.stateNode=e}return qe(t),null;case 13:if(n=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(i=Tl(t),n!==null&&n.dehydrated!==null){if(e===null){if(!i)throw Error(c(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(c(317));i[ct]=t}else El(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;qe(t),i=!1}else i=Ju(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return t.flags&256?(ha(t),t):(ha(t),null)}if(ha(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=n!==null,e=e!==null&&e.memoizedState!==null,a){n=t.child,i=null,n.alternate!==null&&n.alternate.memoizedState!==null&&n.alternate.memoizedState.cachePool!==null&&(i=n.alternate.memoizedState.cachePool.pool);var o=null;n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(o=n.memoizedState.cachePool.pool),o!==i&&(n.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),ri(t,t.updateQueue),qe(t),null;case 4:return St(),e===null&&Xc(t.stateNode.containerInfo),qe(t),null;case 10:return fa(t.type),qe(t),null;case 19:if(F(Fe),i=t.memoizedState,i===null)return qe(t),null;if(n=(t.flags&128)!==0,o=i.rendering,o===null)if(n)Yl(i,!1);else{if(Ye!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(o=ai(e),o!==null){for(t.flags|=128,Yl(i,!1),e=o.updateQueue,t.updateQueue=e,ri(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)Vu(a,e),a=a.sibling;return Z(Fe,Fe.current&1|2),t.child}e=e.sibling}i.tail!==null&&_e()>ui&&(t.flags|=128,n=!0,Yl(i,!1),t.lanes=4194304)}else{if(!n)if(e=ai(o),e!==null){if(t.flags|=128,n=!0,e=e.updateQueue,t.updateQueue=e,ri(t,e),Yl(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!Oe)return qe(t),null}else 2*_e()-i.renderingStartTime>ui&&a!==536870912&&(t.flags|=128,n=!0,Yl(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(e=i.last,e!==null?e.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=_e(),t.sibling=null,e=Fe.current,Z(Fe,n?e&1|2:e&1),t):(qe(t),null);case 22:case 23:return ha(t),Qr(),n=t.memoizedState!==null,e!==null?e.memoizedState!==null!==n&&(t.flags|=8192):n&&(t.flags|=8192),n?(a&536870912)!==0&&(t.flags&128)===0&&(qe(t),t.subtreeFlags&6&&(t.flags|=8192)):qe(t),a=t.updateQueue,a!==null&&ri(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),n=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),n!==a&&(t.flags|=2048),e!==null&&F(cn),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),fa(Pe),qe(t),null;case 25:return null;case 30:return null}throw Error(c(156,t.tag))}function Qg(e,t){switch(Rr(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return fa(Pe),St(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return P(t),null;case 13:if(ha(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(c(340));El()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return F(Fe),null;case 4:return St(),null;case 10:return fa(t.type),null;case 22:case 23:return ha(t),Qr(),e!==null&&F(cn),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return fa(Pe),null;case 25:return null;default:return null}}function yd(e,t){switch(Rr(t),t.tag){case 3:fa(Pe),St();break;case 26:case 27:case 5:P(t);break;case 4:St();break;case 13:ha(t);break;case 19:F(Fe);break;case 10:fa(t.type);break;case 22:case 23:ha(t),Qr(),e!==null&&F(cn);break;case 24:fa(Pe)}}function Xl(e,t){try{var a=t.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var i=n.next;a=i;do{if((a.tag&e)===e){n=void 0;var o=a.create,h=a.inst;n=o(),h.destroy=n}a=a.next}while(a!==i)}}catch(g){Be(t,t.return,g)}}function Ma(e,t,a){try{var n=t.updateQueue,i=n!==null?n.lastEffect:null;if(i!==null){var o=i.next;n=o;do{if((n.tag&e)===e){var h=n.inst,g=h.destroy;if(g!==void 0){h.destroy=void 0,i=t;var j=a,z=g;try{z()}catch(Y){Be(i,j,Y)}}}n=n.next}while(n!==o)}}catch(Y){Be(t,t.return,Y)}}function vd(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{sf(t,a)}catch(n){Be(e,e.return,n)}}}function bd(e,t,a){a.props=un(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(n){Be(e,t,n)}}function Vl(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var n=e.stateNode;break;case 30:n=e.stateNode;break;default:n=e.stateNode}typeof a=="function"?e.refCleanup=a(n):a.current=n}}catch(i){Be(e,t,i)}}function It(e,t){var a=e.ref,n=e.refCleanup;if(a!==null)if(typeof n=="function")try{n()}catch(i){Be(e,t,i)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(i){Be(e,t,i)}else a.current=null}function jd(e){var t=e.type,a=e.memoizedProps,n=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break e;case"img":a.src?n.src=a.src:a.srcSet&&(n.srcset=a.srcSet)}}catch(i){Be(e,e.return,i)}}function vc(e,t,a){try{var n=e.stateNode;dp(n,e.type,a,t),n[pt]=t}catch(i){Be(e,e.return,i)}}function Nd(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&qa(e.type)||e.tag===4}function bc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Nd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&qa(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function jc(e,t,a){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=vi));else if(n!==4&&(n===27&&qa(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(jc(e,t,a),e=e.sibling;e!==null;)jc(e,t,a),e=e.sibling}function ci(e,t,a){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(n!==4&&(n===27&&qa(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(ci(e,t,a),e=e.sibling;e!==null;)ci(e,t,a),e=e.sibling}function Sd(e){var t=e.stateNode,a=e.memoizedProps;try{for(var n=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);it(t,n,a),t[ct]=e,t[pt]=a}catch(o){Be(e,e.return,o)}}var pa=!1,Ve=!1,Nc=!1,wd=typeof WeakSet=="function"?WeakSet:Set,tt=null;function Zg(e,t){if(e=e.containerInfo,Zc=Ei,e=Du(e),br(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var n=a.getSelection&&a.getSelection();if(n&&n.rangeCount!==0){a=n.anchorNode;var i=n.anchorOffset,o=n.focusNode;n=n.focusOffset;try{a.nodeType,o.nodeType}catch{a=null;break e}var h=0,g=-1,j=-1,z=0,Y=0,V=e,B=null;t:for(;;){for(var L;V!==a||i!==0&&V.nodeType!==3||(g=h+i),V!==o||n!==0&&V.nodeType!==3||(j=h+n),V.nodeType===3&&(h+=V.nodeValue.length),(L=V.firstChild)!==null;)B=V,V=L;for(;;){if(V===e)break t;if(B===a&&++z===i&&(g=h),B===o&&++Y===n&&(j=h),(L=V.nextSibling)!==null)break;V=B,B=V.parentNode}V=L}a=g===-1||j===-1?null:{start:g,end:j}}else a=null}a=a||{start:0,end:0}}else a=null;for(Kc={focusedElem:e,selectionRange:a},Ei=!1,tt=t;tt!==null;)if(t=tt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,tt=e;else for(;tt!==null;){switch(t=tt,o=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&o!==null){e=void 0,a=t,i=o.memoizedProps,o=o.memoizedState,n=a.stateNode;try{var ce=un(a.type,i,a.elementType===a.type);e=n.getSnapshotBeforeUpdate(ce,o),n.__reactInternalSnapshotBeforeUpdate=e}catch(le){Be(a,a.return,le)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)Pc(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Pc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(c(163))}if(e=t.sibling,e!==null){e.return=t.return,tt=e;break}tt=t.return}}function Td(e,t,a){var n=a.flags;switch(a.tag){case 0:case 11:case 15:za(e,a),n&4&&Xl(5,a);break;case 1:if(za(e,a),n&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(h){Be(a,a.return,h)}else{var i=un(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(h){Be(a,a.return,h)}}n&64&&vd(a),n&512&&Vl(a,a.return);break;case 3:if(za(e,a),n&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{sf(e,t)}catch(h){Be(a,a.return,h)}}break;case 27:t===null&&n&4&&Sd(a);case 26:case 5:za(e,a),t===null&&n&4&&jd(a),n&512&&Vl(a,a.return);break;case 12:za(e,a);break;case 13:za(e,a),n&4&&Od(e,a),n&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=tp.bind(null,a),vp(e,a))));break;case 22:if(n=a.memoizedState!==null||pa,!n){t=t!==null&&t.memoizedState!==null||Ve,i=pa;var o=Ve;pa=n,(Ve=t)&&!o?Da(e,a,(a.subtreeFlags&8772)!==0):za(e,a),pa=i,Ve=o}break;case 30:break;default:za(e,a)}}function Ed(e){var t=e.alternate;t!==null&&(e.alternate=null,Ed(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&tr(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var ke=null,vt=!1;function xa(e,t,a){for(a=a.child;a!==null;)_d(e,t,a),a=a.sibling}function _d(e,t,a){if(wt&&typeof wt.onCommitFiberUnmount=="function")try{wt.onCommitFiberUnmount(fl,a)}catch{}switch(a.tag){case 26:Ve||It(a,t),xa(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Ve||It(a,t);var n=ke,i=vt;qa(a.type)&&(ke=a.stateNode,vt=!1),xa(e,t,a),Il(a.stateNode),ke=n,vt=i;break;case 5:Ve||It(a,t);case 6:if(n=ke,i=vt,ke=null,xa(e,t,a),ke=n,vt=i,ke!==null)if(vt)try{(ke.nodeType===9?ke.body:ke.nodeName==="HTML"?ke.ownerDocument.body:ke).removeChild(a.stateNode)}catch(o){Be(a,t,o)}else try{ke.removeChild(a.stateNode)}catch(o){Be(a,t,o)}break;case 18:ke!==null&&(vt?(e=ke,gh(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),rs(e)):gh(ke,a.stateNode));break;case 4:n=ke,i=vt,ke=a.stateNode.containerInfo,vt=!0,xa(e,t,a),ke=n,vt=i;break;case 0:case 11:case 14:case 15:Ve||Ma(2,a,t),Ve||Ma(4,a,t),xa(e,t,a);break;case 1:Ve||(It(a,t),n=a.stateNode,typeof n.componentWillUnmount=="function"&&bd(a,t,n)),xa(e,t,a);break;case 21:xa(e,t,a);break;case 22:Ve=(n=Ve)||a.memoizedState!==null,xa(e,t,a),Ve=n;break;default:xa(e,t,a)}}function Od(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{rs(e)}catch(a){Be(t,t.return,a)}}function Kg(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new wd),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new wd),t;default:throw Error(c(435,e.tag))}}function Sc(e,t){var a=Kg(e);t.forEach(function(n){var i=ap.bind(null,e,n);a.has(n)||(a.add(n),n.then(i,i))})}function Ot(e,t){var a=t.deletions;if(a!==null)for(var n=0;n<a.length;n++){var i=a[n],o=e,h=t,g=h;e:for(;g!==null;){switch(g.tag){case 27:if(qa(g.type)){ke=g.stateNode,vt=!1;break e}break;case 5:ke=g.stateNode,vt=!1;break e;case 3:case 4:ke=g.stateNode.containerInfo,vt=!0;break e}g=g.return}if(ke===null)throw Error(c(160));_d(o,h,i),ke=null,vt=!1,o=i.alternate,o!==null&&(o.return=null),i.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Cd(t,e),t=t.sibling}var Qt=null;function Cd(e,t){var a=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Ot(t,e),Ct(e),n&4&&(Ma(3,e,e.return),Xl(3,e),Ma(5,e,e.return));break;case 1:Ot(t,e),Ct(e),n&512&&(Ve||a===null||It(a,a.return)),n&64&&pa&&(e=e.updateQueue,e!==null&&(n=e.callbacks,n!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?n:a.concat(n))));break;case 26:var i=Qt;if(Ot(t,e),Ct(e),n&512&&(Ve||a===null||It(a,a.return)),n&4){var o=a!==null?a.memoizedState:null;if(n=e.memoizedState,a===null)if(n===null)if(e.stateNode===null){e:{n=e.type,a=e.memoizedProps,i=i.ownerDocument||i;t:switch(n){case"title":o=i.getElementsByTagName("title")[0],(!o||o[ml]||o[ct]||o.namespaceURI==="http://www.w3.org/2000/svg"||o.hasAttribute("itemprop"))&&(o=i.createElement(n),i.head.insertBefore(o,i.querySelector("head > title"))),it(o,n,a),o[ct]=e,Ie(o),n=o;break e;case"link":var h=Sh("link","href",i).get(n+(a.href||""));if(h){for(var g=0;g<h.length;g++)if(o=h[g],o.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&o.getAttribute("rel")===(a.rel==null?null:a.rel)&&o.getAttribute("title")===(a.title==null?null:a.title)&&o.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){h.splice(g,1);break t}}o=i.createElement(n),it(o,n,a),i.head.appendChild(o);break;case"meta":if(h=Sh("meta","content",i).get(n+(a.content||""))){for(g=0;g<h.length;g++)if(o=h[g],o.getAttribute("content")===(a.content==null?null:""+a.content)&&o.getAttribute("name")===(a.name==null?null:a.name)&&o.getAttribute("property")===(a.property==null?null:a.property)&&o.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&o.getAttribute("charset")===(a.charSet==null?null:a.charSet)){h.splice(g,1);break t}}o=i.createElement(n),it(o,n,a),i.head.appendChild(o);break;default:throw Error(c(468,n))}o[ct]=e,Ie(o),n=o}e.stateNode=n}else wh(i,e.type,e.stateNode);else e.stateNode=Nh(i,n,e.memoizedProps);else o!==n?(o===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):o.count--,n===null?wh(i,e.type,e.stateNode):Nh(i,n,e.memoizedProps)):n===null&&e.stateNode!==null&&vc(e,e.memoizedProps,a.memoizedProps)}break;case 27:Ot(t,e),Ct(e),n&512&&(Ve||a===null||It(a,a.return)),a!==null&&n&4&&vc(e,e.memoizedProps,a.memoizedProps);break;case 5:if(Ot(t,e),Ct(e),n&512&&(Ve||a===null||It(a,a.return)),e.flags&32){i=e.stateNode;try{En(i,"")}catch(L){Be(e,e.return,L)}}n&4&&e.stateNode!=null&&(i=e.memoizedProps,vc(e,i,a!==null?a.memoizedProps:i)),n&1024&&(Nc=!0);break;case 6:if(Ot(t,e),Ct(e),n&4){if(e.stateNode===null)throw Error(c(162));n=e.memoizedProps,a=e.stateNode;try{a.nodeValue=n}catch(L){Be(e,e.return,L)}}break;case 3:if(Si=null,i=Qt,Qt=ji(t.containerInfo),Ot(t,e),Qt=i,Ct(e),n&4&&a!==null&&a.memoizedState.isDehydrated)try{rs(t.containerInfo)}catch(L){Be(e,e.return,L)}Nc&&(Nc=!1,Ad(e));break;case 4:n=Qt,Qt=ji(e.stateNode.containerInfo),Ot(t,e),Ct(e),Qt=n;break;case 12:Ot(t,e),Ct(e);break;case 13:Ot(t,e),Ct(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Cc=_e()),n&4&&(n=e.updateQueue,n!==null&&(e.updateQueue=null,Sc(e,n)));break;case 22:i=e.memoizedState!==null;var j=a!==null&&a.memoizedState!==null,z=pa,Y=Ve;if(pa=z||i,Ve=Y||j,Ot(t,e),Ve=Y,pa=z,Ct(e),n&8192)e:for(t=e.stateNode,t._visibility=i?t._visibility&-2:t._visibility|1,i&&(a===null||j||pa||Ve||fn(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){j=a=t;try{if(o=j.stateNode,i)h=o.style,typeof h.setProperty=="function"?h.setProperty("display","none","important"):h.display="none";else{g=j.stateNode;var V=j.memoizedProps.style,B=V!=null&&V.hasOwnProperty("display")?V.display:null;g.style.display=B==null||typeof B=="boolean"?"":(""+B).trim()}}catch(L){Be(j,j.return,L)}}}else if(t.tag===6){if(a===null){j=t;try{j.stateNode.nodeValue=i?"":j.memoizedProps}catch(L){Be(j,j.return,L)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}n&4&&(n=e.updateQueue,n!==null&&(a=n.retryQueue,a!==null&&(n.retryQueue=null,Sc(e,a))));break;case 19:Ot(t,e),Ct(e),n&4&&(n=e.updateQueue,n!==null&&(e.updateQueue=null,Sc(e,n)));break;case 30:break;case 21:break;default:Ot(t,e),Ct(e)}}function Ct(e){var t=e.flags;if(t&2){try{for(var a,n=e.return;n!==null;){if(Nd(n)){a=n;break}n=n.return}if(a==null)throw Error(c(160));switch(a.tag){case 27:var i=a.stateNode,o=bc(e);ci(e,o,i);break;case 5:var h=a.stateNode;a.flags&32&&(En(h,""),a.flags&=-33);var g=bc(e);ci(e,g,h);break;case 3:case 4:var j=a.stateNode.containerInfo,z=bc(e);jc(e,z,j);break;default:throw Error(c(161))}}catch(Y){Be(e,e.return,Y)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Ad(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Ad(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function za(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Td(e,t.alternate,t),t=t.sibling}function fn(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Ma(4,t,t.return),fn(t);break;case 1:It(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&bd(t,t.return,a),fn(t);break;case 27:Il(t.stateNode);case 26:case 5:It(t,t.return),fn(t);break;case 22:t.memoizedState===null&&fn(t);break;case 30:fn(t);break;default:fn(t)}e=e.sibling}}function Da(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var n=t.alternate,i=e,o=t,h=o.flags;switch(o.tag){case 0:case 11:case 15:Da(i,o,a),Xl(4,o);break;case 1:if(Da(i,o,a),n=o,i=n.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(z){Be(n,n.return,z)}if(n=o,i=n.updateQueue,i!==null){var g=n.stateNode;try{var j=i.shared.hiddenCallbacks;if(j!==null)for(i.shared.hiddenCallbacks=null,i=0;i<j.length;i++)lf(j[i],g)}catch(z){Be(n,n.return,z)}}a&&h&64&&vd(o),Vl(o,o.return);break;case 27:Sd(o);case 26:case 5:Da(i,o,a),a&&n===null&&h&4&&jd(o),Vl(o,o.return);break;case 12:Da(i,o,a);break;case 13:Da(i,o,a),a&&h&4&&Od(i,o);break;case 22:o.memoizedState===null&&Da(i,o,a),Vl(o,o.return);break;case 30:break;default:Da(i,o,a)}t=t.sibling}}function wc(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&Cl(a))}function Tc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Cl(e))}function ea(e,t,a,n){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Rd(e,t,a,n),t=t.sibling}function Rd(e,t,a,n){var i=t.flags;switch(t.tag){case 0:case 11:case 15:ea(e,t,a,n),i&2048&&Xl(9,t);break;case 1:ea(e,t,a,n);break;case 3:ea(e,t,a,n),i&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Cl(e)));break;case 12:if(i&2048){ea(e,t,a,n),e=t.stateNode;try{var o=t.memoizedProps,h=o.id,g=o.onPostCommit;typeof g=="function"&&g(h,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(j){Be(t,t.return,j)}}else ea(e,t,a,n);break;case 13:ea(e,t,a,n);break;case 23:break;case 22:o=t.stateNode,h=t.alternate,t.memoizedState!==null?o._visibility&2?ea(e,t,a,n):Ql(e,t):o._visibility&2?ea(e,t,a,n):(o._visibility|=2,Qn(e,t,a,n,(t.subtreeFlags&10256)!==0)),i&2048&&wc(h,t);break;case 24:ea(e,t,a,n),i&2048&&Tc(t.alternate,t);break;default:ea(e,t,a,n)}}function Qn(e,t,a,n,i){for(i=i&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var o=e,h=t,g=a,j=n,z=h.flags;switch(h.tag){case 0:case 11:case 15:Qn(o,h,g,j,i),Xl(8,h);break;case 23:break;case 22:var Y=h.stateNode;h.memoizedState!==null?Y._visibility&2?Qn(o,h,g,j,i):Ql(o,h):(Y._visibility|=2,Qn(o,h,g,j,i)),i&&z&2048&&wc(h.alternate,h);break;case 24:Qn(o,h,g,j,i),i&&z&2048&&Tc(h.alternate,h);break;default:Qn(o,h,g,j,i)}t=t.sibling}}function Ql(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,n=t,i=n.flags;switch(n.tag){case 22:Ql(a,n),i&2048&&wc(n.alternate,n);break;case 24:Ql(a,n),i&2048&&Tc(n.alternate,n);break;default:Ql(a,n)}t=t.sibling}}var Zl=8192;function Zn(e){if(e.subtreeFlags&Zl)for(e=e.child;e!==null;)Md(e),e=e.sibling}function Md(e){switch(e.tag){case 26:Zn(e),e.flags&Zl&&e.memoizedState!==null&&Mp(Qt,e.memoizedState,e.memoizedProps);break;case 5:Zn(e);break;case 3:case 4:var t=Qt;Qt=ji(e.stateNode.containerInfo),Zn(e),Qt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Zl,Zl=16777216,Zn(e),Zl=t):Zn(e));break;default:Zn(e)}}function zd(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Kl(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var n=t[a];tt=n,Ud(n,e)}zd(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Dd(e),e=e.sibling}function Dd(e){switch(e.tag){case 0:case 11:case 15:Kl(e),e.flags&2048&&Ma(9,e,e.return);break;case 3:Kl(e);break;case 12:Kl(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,oi(e)):Kl(e);break;default:Kl(e)}}function oi(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var n=t[a];tt=n,Ud(n,e)}zd(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Ma(8,t,t.return),oi(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,oi(t));break;default:oi(t)}e=e.sibling}}function Ud(e,t){for(;tt!==null;){var a=tt;switch(a.tag){case 0:case 11:case 15:Ma(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var n=a.memoizedState.cachePool.pool;n!=null&&n.refCount++}break;case 24:Cl(a.memoizedState.cache)}if(n=a.child,n!==null)n.return=a,tt=n;else e:for(a=e;tt!==null;){n=tt;var i=n.sibling,o=n.return;if(Ed(n),n===a){tt=null;break e}if(i!==null){i.return=o,tt=i;break e}tt=o}}}var Jg={getCacheForType:function(e){var t=ot(Pe),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},$g=typeof WeakMap=="function"?WeakMap:Map,Ae=0,Le=null,ye=null,Se=0,Re=0,At=null,Ua=!1,Kn=!1,Ec=!1,ya=0,Ye=0,Ba=0,dn=0,_c=0,Yt=0,Jn=0,Jl=null,bt=null,Oc=!1,Cc=0,ui=1/0,fi=null,La=null,st=0,Ha=null,$n=null,Pn=0,Ac=0,Rc=null,Bd=null,$l=0,Mc=null;function Rt(){if((Ae&2)!==0&&Se!==0)return Se&-Se;if(G.T!==null){var e=Ln;return e!==0?e:kc()}return Fo()}function Ld(){Yt===0&&(Yt=(Se&536870912)===0||Oe?Ko():536870912);var e=Gt.current;return e!==null&&(e.flags|=32),Yt}function Mt(e,t,a){(e===Le&&(Re===2||Re===9)||e.cancelPendingCommit!==null)&&(Fn(e,0),ka(e,Se,Yt,!1)),hl(e,a),((Ae&2)===0||e!==Le)&&(e===Le&&((Ae&2)===0&&(dn|=a),Ye===4&&ka(e,Se,Yt,!1)),ta(e))}function Hd(e,t,a){if((Ae&6)!==0)throw Error(c(327));var n=!a&&(t&124)===0&&(t&e.expiredLanes)===0||dl(e,t),i=n?Wg(e,t):Uc(e,t,!0),o=n;do{if(i===0){Kn&&!n&&ka(e,t,0,!1);break}else{if(a=e.current.alternate,o&&!Pg(a)){i=Uc(e,t,!1),o=!1;continue}if(i===2){if(o=t,e.errorRecoveryDisabledLanes&o)var h=0;else h=e.pendingLanes&-536870913,h=h!==0?h:h&536870912?536870912:0;if(h!==0){t=h;e:{var g=e;i=Jl;var j=g.current.memoizedState.isDehydrated;if(j&&(Fn(g,h).flags|=256),h=Uc(g,h,!1),h!==2){if(Ec&&!j){g.errorRecoveryDisabledLanes|=o,dn|=o,i=4;break e}o=bt,bt=i,o!==null&&(bt===null?bt=o:bt.push.apply(bt,o))}i=h}if(o=!1,i!==2)continue}}if(i===1){Fn(e,0),ka(e,t,0,!0);break}e:{switch(n=e,o=i,o){case 0:case 1:throw Error(c(345));case 4:if((t&4194048)!==t)break;case 6:ka(n,t,Yt,!Ua);break e;case 2:bt=null;break;case 3:case 5:break;default:throw Error(c(329))}if((t&62914560)===t&&(i=Cc+300-_e(),10<i)){if(ka(n,t,Yt,!Ua),Ns(n,0,!0)!==0)break e;n.timeoutHandle=hh(kd.bind(null,n,a,bt,fi,Oc,t,Yt,dn,Jn,Ua,o,2,-0,0),i);break e}kd(n,a,bt,fi,Oc,t,Yt,dn,Jn,Ua,o,0,-0,0)}}break}while(!0);ta(e)}function kd(e,t,a,n,i,o,h,g,j,z,Y,V,B,L){if(e.timeoutHandle=-1,V=t.subtreeFlags,(V&8192||(V&16785408)===16785408)&&(as={stylesheets:null,count:0,unsuspend:Rp},Md(t),V=zp(),V!==null)){e.cancelPendingCommit=V(Zd.bind(null,e,t,o,a,n,i,h,g,j,Y,1,B,L)),ka(e,o,h,!z);return}Zd(e,t,o,a,n,i,h,g,j)}function Pg(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var n=0;n<a.length;n++){var i=a[n],o=i.getSnapshot;i=i.value;try{if(!Et(o(),i))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ka(e,t,a,n){t&=~_c,t&=~dn,e.suspendedLanes|=t,e.pingedLanes&=~t,n&&(e.warmLanes|=t),n=e.expirationTimes;for(var i=t;0<i;){var o=31-Tt(i),h=1<<o;n[o]=-1,i&=~h}a!==0&&$o(e,a,t)}function di(){return(Ae&6)===0?(Pl(0),!1):!0}function zc(){if(ye!==null){if(Re===0)var e=ye.return;else e=ye,ua=sn=null,Pr(e),Xn=null,ql=0,e=ye;for(;e!==null;)yd(e.alternate,e),e=e.return;ye=null}}function Fn(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,mp(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),zc(),Le=e,ye=a=ra(e.current,null),Se=t,Re=0,At=null,Ua=!1,Kn=dl(e,t),Ec=!1,Jn=Yt=_c=dn=Ba=Ye=0,bt=Jl=null,Oc=!1,(t&8)!==0&&(t|=t&32);var n=e.entangledLanes;if(n!==0)for(e=e.entanglements,n&=t;0<n;){var i=31-Tt(n),o=1<<i;t|=e[i],n&=~o}return ya=t,Ds(),a}function qd(e,t){he=null,G.H=Is,t===Rl||t===Xs?(t=af(),Re=3):t===Iu?(t=af(),Re=4):Re=t===ld?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,At=t,ye===null&&(Ye=1,li(e,Lt(t,e.current)))}function Gd(){var e=G.H;return G.H=Is,e===null?Is:e}function Yd(){var e=G.A;return G.A=Jg,e}function Dc(){Ye=4,Ua||(Se&4194048)!==Se&&Gt.current!==null||(Kn=!0),(Ba&134217727)===0&&(dn&134217727)===0||Le===null||ka(Le,Se,Yt,!1)}function Uc(e,t,a){var n=Ae;Ae|=2;var i=Gd(),o=Yd();(Le!==e||Se!==t)&&(fi=null,Fn(e,t)),t=!1;var h=Ye;e:do try{if(Re!==0&&ye!==null){var g=ye,j=At;switch(Re){case 8:zc(),h=6;break e;case 3:case 2:case 9:case 6:Gt.current===null&&(t=!0);var z=Re;if(Re=0,At=null,Wn(e,g,j,z),a&&Kn){h=0;break e}break;default:z=Re,Re=0,At=null,Wn(e,g,j,z)}}Fg(),h=Ye;break}catch(Y){qd(e,Y)}while(!0);return t&&e.shellSuspendCounter++,ua=sn=null,Ae=n,G.H=i,G.A=o,ye===null&&(Le=null,Se=0,Ds()),h}function Fg(){for(;ye!==null;)Xd(ye)}function Wg(e,t){var a=Ae;Ae|=2;var n=Gd(),i=Yd();Le!==e||Se!==t?(fi=null,ui=_e()+500,Fn(e,t)):Kn=dl(e,t);e:do try{if(Re!==0&&ye!==null){t=ye;var o=At;t:switch(Re){case 1:Re=0,At=null,Wn(e,t,o,1);break;case 2:case 9:if(ef(o)){Re=0,At=null,Vd(t);break}t=function(){Re!==2&&Re!==9||Le!==e||(Re=7),ta(e)},o.then(t,t);break e;case 3:Re=7;break e;case 4:Re=5;break e;case 7:ef(o)?(Re=0,At=null,Vd(t)):(Re=0,At=null,Wn(e,t,o,7));break;case 5:var h=null;switch(ye.tag){case 26:h=ye.memoizedState;case 5:case 27:var g=ye;if(!h||Th(h)){Re=0,At=null;var j=g.sibling;if(j!==null)ye=j;else{var z=g.return;z!==null?(ye=z,hi(z)):ye=null}break t}}Re=0,At=null,Wn(e,t,o,5);break;case 6:Re=0,At=null,Wn(e,t,o,6);break;case 8:zc(),Ye=6;break e;default:throw Error(c(462))}}Ig();break}catch(Y){qd(e,Y)}while(!0);return ua=sn=null,G.H=n,G.A=i,Ae=a,ye!==null?0:(Le=null,Se=0,Ds(),Ye)}function Ig(){for(;ye!==null&&!ne();)Xd(ye)}function Xd(e){var t=pd(e.alternate,e,ya);e.memoizedProps=e.pendingProps,t===null?hi(e):ye=t}function Vd(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=ud(a,t,t.pendingProps,t.type,void 0,Se);break;case 11:t=ud(a,t,t.pendingProps,t.type.render,t.ref,Se);break;case 5:Pr(t);default:yd(a,t),t=ye=Vu(t,ya),t=pd(a,t,ya)}e.memoizedProps=e.pendingProps,t===null?hi(e):ye=t}function Wn(e,t,a,n){ua=sn=null,Pr(t),Xn=null,ql=0;var i=t.return;try{if(Yg(e,i,t,a,Se)){Ye=1,li(e,Lt(a,e.current)),ye=null;return}}catch(o){if(i!==null)throw ye=i,o;Ye=1,li(e,Lt(a,e.current)),ye=null;return}t.flags&32768?(Oe||n===1?e=!0:Kn||(Se&536870912)!==0?e=!1:(Ua=e=!0,(n===2||n===9||n===3||n===6)&&(n=Gt.current,n!==null&&n.tag===13&&(n.flags|=16384))),Qd(t,e)):hi(t)}function hi(e){var t=e;do{if((t.flags&32768)!==0){Qd(t,Ua);return}e=t.return;var a=Vg(t.alternate,t,ya);if(a!==null){ye=a;return}if(t=t.sibling,t!==null){ye=t;return}ye=t=e}while(t!==null);Ye===0&&(Ye=5)}function Qd(e,t){do{var a=Qg(e.alternate,e);if(a!==null){a.flags&=32767,ye=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){ye=e;return}ye=e=a}while(e!==null);Ye=6,ye=null}function Zd(e,t,a,n,i,o,h,g,j){e.cancelPendingCommit=null;do mi();while(st!==0);if((Ae&6)!==0)throw Error(c(327));if(t!==null){if(t===e.current)throw Error(c(177));if(o=t.lanes|t.childLanes,o|=Tr,R0(e,a,o,h,g,j),e===Le&&(ye=Le=null,Se=0),$n=t,Ha=e,Pn=a,Ac=o,Rc=i,Bd=n,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,np(la,function(){return Fd(),null})):(e.callbackNode=null,e.callbackPriority=0),n=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||n){n=G.T,G.T=null,i=J.p,J.p=2,h=Ae,Ae|=4;try{Zg(e,t,a)}finally{Ae=h,J.p=i,G.T=n}}st=1,Kd(),Jd(),$d()}}function Kd(){if(st===1){st=0;var e=Ha,t=$n,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=G.T,G.T=null;var n=J.p;J.p=2;var i=Ae;Ae|=4;try{Cd(t,e);var o=Kc,h=Du(e.containerInfo),g=o.focusedElem,j=o.selectionRange;if(h!==g&&g&&g.ownerDocument&&zu(g.ownerDocument.documentElement,g)){if(j!==null&&br(g)){var z=j.start,Y=j.end;if(Y===void 0&&(Y=z),"selectionStart"in g)g.selectionStart=z,g.selectionEnd=Math.min(Y,g.value.length);else{var V=g.ownerDocument||document,B=V&&V.defaultView||window;if(B.getSelection){var L=B.getSelection(),ce=g.textContent.length,le=Math.min(j.start,ce),De=j.end===void 0?le:Math.min(j.end,ce);!L.extend&&le>De&&(h=De,De=le,le=h);var _=Mu(g,le),T=Mu(g,De);if(_&&T&&(L.rangeCount!==1||L.anchorNode!==_.node||L.anchorOffset!==_.offset||L.focusNode!==T.node||L.focusOffset!==T.offset)){var A=V.createRange();A.setStart(_.node,_.offset),L.removeAllRanges(),le>De?(L.addRange(A),L.extend(T.node,T.offset)):(A.setEnd(T.node,T.offset),L.addRange(A))}}}}for(V=[],L=g;L=L.parentNode;)L.nodeType===1&&V.push({element:L,left:L.scrollLeft,top:L.scrollTop});for(typeof g.focus=="function"&&g.focus(),g=0;g<V.length;g++){var X=V[g];X.element.scrollLeft=X.left,X.element.scrollTop=X.top}}Ei=!!Zc,Kc=Zc=null}finally{Ae=i,J.p=n,G.T=a}}e.current=t,st=2}}function Jd(){if(st===2){st=0;var e=Ha,t=$n,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=G.T,G.T=null;var n=J.p;J.p=2;var i=Ae;Ae|=4;try{Td(e,t.alternate,t)}finally{Ae=i,J.p=n,G.T=a}}st=3}}function $d(){if(st===4||st===3){st=0,Ee();var e=Ha,t=$n,a=Pn,n=Bd;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?st=5:(st=0,$n=Ha=null,Pd(e,e.pendingLanes));var i=e.pendingLanes;if(i===0&&(La=null),Ii(a),t=t.stateNode,wt&&typeof wt.onCommitFiberRoot=="function")try{wt.onCommitFiberRoot(fl,t,void 0,(t.current.flags&128)===128)}catch{}if(n!==null){t=G.T,i=J.p,J.p=2,G.T=null;try{for(var o=e.onRecoverableError,h=0;h<n.length;h++){var g=n[h];o(g.value,{componentStack:g.stack})}}finally{G.T=t,J.p=i}}(Pn&3)!==0&&mi(),ta(e),i=e.pendingLanes,(a&4194090)!==0&&(i&42)!==0?e===Mc?$l++:($l=0,Mc=e):$l=0,Pl(0)}}function Pd(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Cl(t)))}function mi(e){return Kd(),Jd(),$d(),Fd()}function Fd(){if(st!==5)return!1;var e=Ha,t=Ac;Ac=0;var a=Ii(Pn),n=G.T,i=J.p;try{J.p=32>a?32:a,G.T=null,a=Rc,Rc=null;var o=Ha,h=Pn;if(st=0,$n=Ha=null,Pn=0,(Ae&6)!==0)throw Error(c(331));var g=Ae;if(Ae|=4,Dd(o.current),Rd(o,o.current,h,a),Ae=g,Pl(0,!1),wt&&typeof wt.onPostCommitFiberRoot=="function")try{wt.onPostCommitFiberRoot(fl,o)}catch{}return!0}finally{J.p=i,G.T=n,Pd(e,t)}}function Wd(e,t,a){t=Lt(a,t),t=uc(e.stateNode,t,2),e=Oa(e,t,2),e!==null&&(hl(e,2),ta(e))}function Be(e,t,a){if(e.tag===3)Wd(e,e,a);else for(;t!==null;){if(t.tag===3){Wd(t,e,a);break}else if(t.tag===1){var n=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(La===null||!La.has(n))){e=Lt(a,e),a=ad(2),n=Oa(t,a,2),n!==null&&(nd(a,n,t,e),hl(n,2),ta(n));break}}t=t.return}}function Bc(e,t,a){var n=e.pingCache;if(n===null){n=e.pingCache=new $g;var i=new Set;n.set(t,i)}else i=n.get(t),i===void 0&&(i=new Set,n.set(t,i));i.has(a)||(Ec=!0,i.add(a),e=ep.bind(null,e,t,a),t.then(e,e))}function ep(e,t,a){var n=e.pingCache;n!==null&&n.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,Le===e&&(Se&a)===a&&(Ye===4||Ye===3&&(Se&62914560)===Se&&300>_e()-Cc?(Ae&2)===0&&Fn(e,0):_c|=a,Jn===Se&&(Jn=0)),ta(e)}function Id(e,t){t===0&&(t=Jo()),e=zn(e,t),e!==null&&(hl(e,t),ta(e))}function tp(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),Id(e,a)}function ap(e,t){var a=0;switch(e.tag){case 13:var n=e.stateNode,i=e.memoizedState;i!==null&&(a=i.retryLane);break;case 19:n=e.stateNode;break;case 22:n=e.stateNode._retryCache;break;default:throw Error(c(314))}n!==null&&n.delete(t),Id(e,a)}function np(e,t){return He(e,t)}var gi=null,In=null,Lc=!1,pi=!1,Hc=!1,hn=0;function ta(e){e!==In&&e.next===null&&(In===null?gi=In=e:In=In.next=e),pi=!0,Lc||(Lc=!0,sp())}function Pl(e,t){if(!Hc&&pi){Hc=!0;do for(var a=!1,n=gi;n!==null;){if(e!==0){var i=n.pendingLanes;if(i===0)var o=0;else{var h=n.suspendedLanes,g=n.pingedLanes;o=(1<<31-Tt(42|e)+1)-1,o&=i&~(h&~g),o=o&201326741?o&201326741|1:o?o|2:0}o!==0&&(a=!0,nh(n,o))}else o=Se,o=Ns(n,n===Le?o:0,n.cancelPendingCommit!==null||n.timeoutHandle!==-1),(o&3)===0||dl(n,o)||(a=!0,nh(n,o));n=n.next}while(a);Hc=!1}}function lp(){eh()}function eh(){pi=Lc=!1;var e=0;hn!==0&&(hp()&&(e=hn),hn=0);for(var t=_e(),a=null,n=gi;n!==null;){var i=n.next,o=th(n,t);o===0?(n.next=null,a===null?gi=i:a.next=i,i===null&&(In=a)):(a=n,(e!==0||(o&3)!==0)&&(pi=!0)),n=i}Pl(e)}function th(e,t){for(var a=e.suspendedLanes,n=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes&-62914561;0<o;){var h=31-Tt(o),g=1<<h,j=i[h];j===-1?((g&a)===0||(g&n)!==0)&&(i[h]=A0(g,t)):j<=t&&(e.expiredLanes|=g),o&=~g}if(t=Le,a=Se,a=Ns(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),n=e.callbackNode,a===0||e===t&&(Re===2||Re===9)||e.cancelPendingCommit!==null)return n!==null&&n!==null&&dt(n),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||dl(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(n!==null&&dt(n),Ii(a)){case 2:case 8:a=ht;break;case 32:a=la;break;case 268435456:a=Zo;break;default:a=la}return n=ah.bind(null,e),a=He(a,n),e.callbackPriority=t,e.callbackNode=a,t}return n!==null&&n!==null&&dt(n),e.callbackPriority=2,e.callbackNode=null,2}function ah(e,t){if(st!==0&&st!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(mi()&&e.callbackNode!==a)return null;var n=Se;return n=Ns(e,e===Le?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),n===0?null:(Hd(e,n,t),th(e,_e()),e.callbackNode!=null&&e.callbackNode===a?ah.bind(null,e):null)}function nh(e,t){if(mi())return null;Hd(e,t,!0)}function sp(){gp(function(){(Ae&6)!==0?He(Pt,lp):eh()})}function kc(){return hn===0&&(hn=Ko()),hn}function lh(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:_s(""+e)}function sh(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function ip(e,t,a,n,i){if(t==="submit"&&a&&a.stateNode===i){var o=lh((i[pt]||null).action),h=n.submitter;h&&(t=(t=h[pt]||null)?lh(t.formAction):h.getAttribute("formAction"),t!==null&&(o=t,h=null));var g=new Rs("action","action",null,n,i);e.push({event:g,listeners:[{instance:null,listener:function(){if(n.defaultPrevented){if(hn!==0){var j=h?sh(i,h):new FormData(i);sc(a,{pending:!0,data:j,method:i.method,action:o},null,j)}}else typeof o=="function"&&(g.preventDefault(),j=h?sh(i,h):new FormData(i),sc(a,{pending:!0,data:j,method:i.method,action:o},o,j))},currentTarget:i}]})}}for(var qc=0;qc<wr.length;qc++){var Gc=wr[qc],rp=Gc.toLowerCase(),cp=Gc[0].toUpperCase()+Gc.slice(1);Vt(rp,"on"+cp)}Vt(Lu,"onAnimationEnd"),Vt(Hu,"onAnimationIteration"),Vt(ku,"onAnimationStart"),Vt("dblclick","onDoubleClick"),Vt("focusin","onFocus"),Vt("focusout","onBlur"),Vt(Tg,"onTransitionRun"),Vt(Eg,"onTransitionStart"),Vt(_g,"onTransitionCancel"),Vt(qu,"onTransitionEnd"),Sn("onMouseEnter",["mouseout","mouseover"]),Sn("onMouseLeave",["mouseout","mouseover"]),Sn("onPointerEnter",["pointerout","pointerover"]),Sn("onPointerLeave",["pointerout","pointerover"]),Pa("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Pa("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Pa("onBeforeInput",["compositionend","keypress","textInput","paste"]),Pa("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Pa("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Pa("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Fl="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),op=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Fl));function ih(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var n=e[a],i=n.event;n=n.listeners;e:{var o=void 0;if(t)for(var h=n.length-1;0<=h;h--){var g=n[h],j=g.instance,z=g.currentTarget;if(g=g.listener,j!==o&&i.isPropagationStopped())break e;o=g,i.currentTarget=z;try{o(i)}catch(Y){ni(Y)}i.currentTarget=null,o=j}else for(h=0;h<n.length;h++){if(g=n[h],j=g.instance,z=g.currentTarget,g=g.listener,j!==o&&i.isPropagationStopped())break e;o=g,i.currentTarget=z;try{o(i)}catch(Y){ni(Y)}i.currentTarget=null,o=j}}}}function ve(e,t){var a=t[er];a===void 0&&(a=t[er]=new Set);var n=e+"__bubble";a.has(n)||(rh(t,e,2,!1),a.add(n))}function Yc(e,t,a){var n=0;t&&(n|=4),rh(a,e,n,t)}var xi="_reactListening"+Math.random().toString(36).slice(2);function Xc(e){if(!e[xi]){e[xi]=!0,Io.forEach(function(a){a!=="selectionchange"&&(op.has(a)||Yc(a,!1,e),Yc(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[xi]||(t[xi]=!0,Yc("selectionchange",!1,t))}}function rh(e,t,a,n){switch(Rh(t)){case 2:var i=Bp;break;case 8:i=Lp;break;default:i=no}a=i.bind(null,t,a,e),i=void 0,!fr||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),n?i!==void 0?e.addEventListener(t,a,{capture:!0,passive:i}):e.addEventListener(t,a,!0):i!==void 0?e.addEventListener(t,a,{passive:i}):e.addEventListener(t,a,!1)}function Vc(e,t,a,n,i){var o=n;if((t&1)===0&&(t&2)===0&&n!==null)e:for(;;){if(n===null)return;var h=n.tag;if(h===3||h===4){var g=n.stateNode.containerInfo;if(g===i)break;if(h===4)for(h=n.return;h!==null;){var j=h.tag;if((j===3||j===4)&&h.stateNode.containerInfo===i)return;h=h.return}for(;g!==null;){if(h=bn(g),h===null)return;if(j=h.tag,j===5||j===6||j===26||j===27){n=o=h;continue e}g=g.parentNode}}n=n.return}hu(function(){var z=o,Y=or(a),V=[];e:{var B=Gu.get(e);if(B!==void 0){var L=Rs,ce=e;switch(e){case"keypress":if(Cs(a)===0)break e;case"keydown":case"keyup":L=ng;break;case"focusin":ce="focus",L=gr;break;case"focusout":ce="blur",L=gr;break;case"beforeblur":case"afterblur":L=gr;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":L=pu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":L=Q0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":L=ig;break;case Lu:case Hu:case ku:L=J0;break;case qu:L=cg;break;case"scroll":case"scrollend":L=X0;break;case"wheel":L=ug;break;case"copy":case"cut":case"paste":L=P0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":L=yu;break;case"toggle":case"beforetoggle":L=dg}var le=(t&4)!==0,De=!le&&(e==="scroll"||e==="scrollend"),_=le?B!==null?B+"Capture":null:B;le=[];for(var T=z,A;T!==null;){var X=T;if(A=X.stateNode,X=X.tag,X!==5&&X!==26&&X!==27||A===null||_===null||(X=pl(T,_),X!=null&&le.push(Wl(T,X,A))),De)break;T=T.return}0<le.length&&(B=new L(B,ce,null,a,Y),V.push({event:B,listeners:le}))}}if((t&7)===0){e:{if(B=e==="mouseover"||e==="pointerover",L=e==="mouseout"||e==="pointerout",B&&a!==cr&&(ce=a.relatedTarget||a.fromElement)&&(bn(ce)||ce[vn]))break e;if((L||B)&&(B=Y.window===Y?Y:(B=Y.ownerDocument)?B.defaultView||B.parentWindow:window,L?(ce=a.relatedTarget||a.toElement,L=z,ce=ce?bn(ce):null,ce!==null&&(De=d(ce),le=ce.tag,ce!==De||le!==5&&le!==27&&le!==6)&&(ce=null)):(L=null,ce=z),L!==ce)){if(le=pu,X="onMouseLeave",_="onMouseEnter",T="mouse",(e==="pointerout"||e==="pointerover")&&(le=yu,X="onPointerLeave",_="onPointerEnter",T="pointer"),De=L==null?B:gl(L),A=ce==null?B:gl(ce),B=new le(X,T+"leave",L,a,Y),B.target=De,B.relatedTarget=A,X=null,bn(Y)===z&&(le=new le(_,T+"enter",ce,a,Y),le.target=A,le.relatedTarget=De,X=le),De=X,L&&ce)t:{for(le=L,_=ce,T=0,A=le;A;A=el(A))T++;for(A=0,X=_;X;X=el(X))A++;for(;0<T-A;)le=el(le),T--;for(;0<A-T;)_=el(_),A--;for(;T--;){if(le===_||_!==null&&le===_.alternate)break t;le=el(le),_=el(_)}le=null}else le=null;L!==null&&ch(V,B,L,le,!1),ce!==null&&De!==null&&ch(V,De,ce,le,!0)}}e:{if(B=z?gl(z):window,L=B.nodeName&&B.nodeName.toLowerCase(),L==="select"||L==="input"&&B.type==="file")var I=Eu;else if(wu(B))if(_u)I=Ng;else{I=bg;var pe=vg}else L=B.nodeName,!L||L.toLowerCase()!=="input"||B.type!=="checkbox"&&B.type!=="radio"?z&&rr(z.elementType)&&(I=Eu):I=jg;if(I&&(I=I(e,z))){Tu(V,I,a,Y);break e}pe&&pe(e,B,z),e==="focusout"&&z&&B.type==="number"&&z.memoizedProps.value!=null&&ir(B,"number",B.value)}switch(pe=z?gl(z):window,e){case"focusin":(wu(pe)||pe.contentEditable==="true")&&(An=pe,jr=z,wl=null);break;case"focusout":wl=jr=An=null;break;case"mousedown":Nr=!0;break;case"contextmenu":case"mouseup":case"dragend":Nr=!1,Uu(V,a,Y);break;case"selectionchange":if(wg)break;case"keydown":case"keyup":Uu(V,a,Y)}var te;if(xr)e:{switch(e){case"compositionstart":var se="onCompositionStart";break e;case"compositionend":se="onCompositionEnd";break e;case"compositionupdate":se="onCompositionUpdate";break e}se=void 0}else Cn?Nu(e,a)&&(se="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(se="onCompositionStart");se&&(vu&&a.locale!=="ko"&&(Cn||se!=="onCompositionStart"?se==="onCompositionEnd"&&Cn&&(te=mu()):(wa=Y,dr="value"in wa?wa.value:wa.textContent,Cn=!0)),pe=yi(z,se),0<pe.length&&(se=new xu(se,e,null,a,Y),V.push({event:se,listeners:pe}),te?se.data=te:(te=Su(a),te!==null&&(se.data=te)))),(te=mg?gg(e,a):pg(e,a))&&(se=yi(z,"onBeforeInput"),0<se.length&&(pe=new xu("onBeforeInput","beforeinput",null,a,Y),V.push({event:pe,listeners:se}),pe.data=te)),ip(V,e,z,a,Y)}ih(V,t)})}function Wl(e,t,a){return{instance:e,listener:t,currentTarget:a}}function yi(e,t){for(var a=t+"Capture",n=[];e!==null;){var i=e,o=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||o===null||(i=pl(e,a),i!=null&&n.unshift(Wl(e,i,o)),i=pl(e,t),i!=null&&n.push(Wl(e,i,o))),e.tag===3)return n;e=e.return}return[]}function el(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function ch(e,t,a,n,i){for(var o=t._reactName,h=[];a!==null&&a!==n;){var g=a,j=g.alternate,z=g.stateNode;if(g=g.tag,j!==null&&j===n)break;g!==5&&g!==26&&g!==27||z===null||(j=z,i?(z=pl(a,o),z!=null&&h.unshift(Wl(a,z,j))):i||(z=pl(a,o),z!=null&&h.push(Wl(a,z,j)))),a=a.return}h.length!==0&&e.push({event:t,listeners:h})}var up=/\r\n?/g,fp=/\u0000|\uFFFD/g;function oh(e){return(typeof e=="string"?e:""+e).replace(up,`
`).replace(fp,"")}function uh(e,t){return t=oh(t),oh(e)===t}function vi(){}function ze(e,t,a,n,i,o){switch(a){case"children":typeof n=="string"?t==="body"||t==="textarea"&&n===""||En(e,n):(typeof n=="number"||typeof n=="bigint")&&t!=="body"&&En(e,""+n);break;case"className":ws(e,"class",n);break;case"tabIndex":ws(e,"tabindex",n);break;case"dir":case"role":case"viewBox":case"width":case"height":ws(e,a,n);break;case"style":fu(e,n,o);break;case"data":if(t!=="object"){ws(e,"data",n);break}case"src":case"href":if(n===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(n==null||typeof n=="function"||typeof n=="symbol"||typeof n=="boolean"){e.removeAttribute(a);break}n=_s(""+n),e.setAttribute(a,n);break;case"action":case"formAction":if(typeof n=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof o=="function"&&(a==="formAction"?(t!=="input"&&ze(e,t,"name",i.name,i,null),ze(e,t,"formEncType",i.formEncType,i,null),ze(e,t,"formMethod",i.formMethod,i,null),ze(e,t,"formTarget",i.formTarget,i,null)):(ze(e,t,"encType",i.encType,i,null),ze(e,t,"method",i.method,i,null),ze(e,t,"target",i.target,i,null)));if(n==null||typeof n=="symbol"||typeof n=="boolean"){e.removeAttribute(a);break}n=_s(""+n),e.setAttribute(a,n);break;case"onClick":n!=null&&(e.onclick=vi);break;case"onScroll":n!=null&&ve("scroll",e);break;case"onScrollEnd":n!=null&&ve("scrollend",e);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(c(61));if(a=n.__html,a!=null){if(i.children!=null)throw Error(c(60));e.innerHTML=a}}break;case"multiple":e.multiple=n&&typeof n!="function"&&typeof n!="symbol";break;case"muted":e.muted=n&&typeof n!="function"&&typeof n!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(n==null||typeof n=="function"||typeof n=="boolean"||typeof n=="symbol"){e.removeAttribute("xlink:href");break}a=_s(""+n),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":n!=null&&typeof n!="function"&&typeof n!="symbol"?e.setAttribute(a,""+n):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":n&&typeof n!="function"&&typeof n!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":n===!0?e.setAttribute(a,""):n!==!1&&n!=null&&typeof n!="function"&&typeof n!="symbol"?e.setAttribute(a,n):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":n!=null&&typeof n!="function"&&typeof n!="symbol"&&!isNaN(n)&&1<=n?e.setAttribute(a,n):e.removeAttribute(a);break;case"rowSpan":case"start":n==null||typeof n=="function"||typeof n=="symbol"||isNaN(n)?e.removeAttribute(a):e.setAttribute(a,n);break;case"popover":ve("beforetoggle",e),ve("toggle",e),Ss(e,"popover",n);break;case"xlinkActuate":sa(e,"http://www.w3.org/1999/xlink","xlink:actuate",n);break;case"xlinkArcrole":sa(e,"http://www.w3.org/1999/xlink","xlink:arcrole",n);break;case"xlinkRole":sa(e,"http://www.w3.org/1999/xlink","xlink:role",n);break;case"xlinkShow":sa(e,"http://www.w3.org/1999/xlink","xlink:show",n);break;case"xlinkTitle":sa(e,"http://www.w3.org/1999/xlink","xlink:title",n);break;case"xlinkType":sa(e,"http://www.w3.org/1999/xlink","xlink:type",n);break;case"xmlBase":sa(e,"http://www.w3.org/XML/1998/namespace","xml:base",n);break;case"xmlLang":sa(e,"http://www.w3.org/XML/1998/namespace","xml:lang",n);break;case"xmlSpace":sa(e,"http://www.w3.org/XML/1998/namespace","xml:space",n);break;case"is":Ss(e,"is",n);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=G0.get(a)||a,Ss(e,a,n))}}function Qc(e,t,a,n,i,o){switch(a){case"style":fu(e,n,o);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(c(61));if(a=n.__html,a!=null){if(i.children!=null)throw Error(c(60));e.innerHTML=a}}break;case"children":typeof n=="string"?En(e,n):(typeof n=="number"||typeof n=="bigint")&&En(e,""+n);break;case"onScroll":n!=null&&ve("scroll",e);break;case"onScrollEnd":n!=null&&ve("scrollend",e);break;case"onClick":n!=null&&(e.onclick=vi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!eu.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(i=a.endsWith("Capture"),t=a.slice(2,i?a.length-7:void 0),o=e[pt]||null,o=o!=null?o[a]:null,typeof o=="function"&&e.removeEventListener(t,o,i),typeof n=="function")){typeof o!="function"&&o!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,n,i);break e}a in e?e[a]=n:n===!0?e.setAttribute(a,""):Ss(e,a,n)}}}function it(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ve("error",e),ve("load",e);var n=!1,i=!1,o;for(o in a)if(a.hasOwnProperty(o)){var h=a[o];if(h!=null)switch(o){case"src":n=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:ze(e,t,o,h,a,null)}}i&&ze(e,t,"srcSet",a.srcSet,a,null),n&&ze(e,t,"src",a.src,a,null);return;case"input":ve("invalid",e);var g=o=h=i=null,j=null,z=null;for(n in a)if(a.hasOwnProperty(n)){var Y=a[n];if(Y!=null)switch(n){case"name":i=Y;break;case"type":h=Y;break;case"checked":j=Y;break;case"defaultChecked":z=Y;break;case"value":o=Y;break;case"defaultValue":g=Y;break;case"children":case"dangerouslySetInnerHTML":if(Y!=null)throw Error(c(137,t));break;default:ze(e,t,n,Y,a,null)}}ru(e,o,g,j,z,h,i,!1),Ts(e);return;case"select":ve("invalid",e),n=h=o=null;for(i in a)if(a.hasOwnProperty(i)&&(g=a[i],g!=null))switch(i){case"value":o=g;break;case"defaultValue":h=g;break;case"multiple":n=g;default:ze(e,t,i,g,a,null)}t=o,a=h,e.multiple=!!n,t!=null?Tn(e,!!n,t,!1):a!=null&&Tn(e,!!n,a,!0);return;case"textarea":ve("invalid",e),o=i=n=null;for(h in a)if(a.hasOwnProperty(h)&&(g=a[h],g!=null))switch(h){case"value":n=g;break;case"defaultValue":i=g;break;case"children":o=g;break;case"dangerouslySetInnerHTML":if(g!=null)throw Error(c(91));break;default:ze(e,t,h,g,a,null)}ou(e,n,i,o),Ts(e);return;case"option":for(j in a)if(a.hasOwnProperty(j)&&(n=a[j],n!=null))switch(j){case"selected":e.selected=n&&typeof n!="function"&&typeof n!="symbol";break;default:ze(e,t,j,n,a,null)}return;case"dialog":ve("beforetoggle",e),ve("toggle",e),ve("cancel",e),ve("close",e);break;case"iframe":case"object":ve("load",e);break;case"video":case"audio":for(n=0;n<Fl.length;n++)ve(Fl[n],e);break;case"image":ve("error",e),ve("load",e);break;case"details":ve("toggle",e);break;case"embed":case"source":case"link":ve("error",e),ve("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(z in a)if(a.hasOwnProperty(z)&&(n=a[z],n!=null))switch(z){case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:ze(e,t,z,n,a,null)}return;default:if(rr(t)){for(Y in a)a.hasOwnProperty(Y)&&(n=a[Y],n!==void 0&&Qc(e,t,Y,n,a,void 0));return}}for(g in a)a.hasOwnProperty(g)&&(n=a[g],n!=null&&ze(e,t,g,n,a,null))}function dp(e,t,a,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,o=null,h=null,g=null,j=null,z=null,Y=null;for(L in a){var V=a[L];if(a.hasOwnProperty(L)&&V!=null)switch(L){case"checked":break;case"value":break;case"defaultValue":j=V;default:n.hasOwnProperty(L)||ze(e,t,L,null,n,V)}}for(var B in n){var L=n[B];if(V=a[B],n.hasOwnProperty(B)&&(L!=null||V!=null))switch(B){case"type":o=L;break;case"name":i=L;break;case"checked":z=L;break;case"defaultChecked":Y=L;break;case"value":h=L;break;case"defaultValue":g=L;break;case"children":case"dangerouslySetInnerHTML":if(L!=null)throw Error(c(137,t));break;default:L!==V&&ze(e,t,B,L,n,V)}}sr(e,h,g,j,z,Y,o,i);return;case"select":L=h=g=B=null;for(o in a)if(j=a[o],a.hasOwnProperty(o)&&j!=null)switch(o){case"value":break;case"multiple":L=j;default:n.hasOwnProperty(o)||ze(e,t,o,null,n,j)}for(i in n)if(o=n[i],j=a[i],n.hasOwnProperty(i)&&(o!=null||j!=null))switch(i){case"value":B=o;break;case"defaultValue":g=o;break;case"multiple":h=o;default:o!==j&&ze(e,t,i,o,n,j)}t=g,a=h,n=L,B!=null?Tn(e,!!a,B,!1):!!n!=!!a&&(t!=null?Tn(e,!!a,t,!0):Tn(e,!!a,a?[]:"",!1));return;case"textarea":L=B=null;for(g in a)if(i=a[g],a.hasOwnProperty(g)&&i!=null&&!n.hasOwnProperty(g))switch(g){case"value":break;case"children":break;default:ze(e,t,g,null,n,i)}for(h in n)if(i=n[h],o=a[h],n.hasOwnProperty(h)&&(i!=null||o!=null))switch(h){case"value":B=i;break;case"defaultValue":L=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(c(91));break;default:i!==o&&ze(e,t,h,i,n,o)}cu(e,B,L);return;case"option":for(var ce in a)if(B=a[ce],a.hasOwnProperty(ce)&&B!=null&&!n.hasOwnProperty(ce))switch(ce){case"selected":e.selected=!1;break;default:ze(e,t,ce,null,n,B)}for(j in n)if(B=n[j],L=a[j],n.hasOwnProperty(j)&&B!==L&&(B!=null||L!=null))switch(j){case"selected":e.selected=B&&typeof B!="function"&&typeof B!="symbol";break;default:ze(e,t,j,B,n,L)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var le in a)B=a[le],a.hasOwnProperty(le)&&B!=null&&!n.hasOwnProperty(le)&&ze(e,t,le,null,n,B);for(z in n)if(B=n[z],L=a[z],n.hasOwnProperty(z)&&B!==L&&(B!=null||L!=null))switch(z){case"children":case"dangerouslySetInnerHTML":if(B!=null)throw Error(c(137,t));break;default:ze(e,t,z,B,n,L)}return;default:if(rr(t)){for(var De in a)B=a[De],a.hasOwnProperty(De)&&B!==void 0&&!n.hasOwnProperty(De)&&Qc(e,t,De,void 0,n,B);for(Y in n)B=n[Y],L=a[Y],!n.hasOwnProperty(Y)||B===L||B===void 0&&L===void 0||Qc(e,t,Y,B,n,L);return}}for(var _ in a)B=a[_],a.hasOwnProperty(_)&&B!=null&&!n.hasOwnProperty(_)&&ze(e,t,_,null,n,B);for(V in n)B=n[V],L=a[V],!n.hasOwnProperty(V)||B===L||B==null&&L==null||ze(e,t,V,B,n,L)}var Zc=null,Kc=null;function bi(e){return e.nodeType===9?e:e.ownerDocument}function fh(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function dh(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Jc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var $c=null;function hp(){var e=window.event;return e&&e.type==="popstate"?e===$c?!1:($c=e,!0):($c=null,!1)}var hh=typeof setTimeout=="function"?setTimeout:void 0,mp=typeof clearTimeout=="function"?clearTimeout:void 0,mh=typeof Promise=="function"?Promise:void 0,gp=typeof queueMicrotask=="function"?queueMicrotask:typeof mh<"u"?function(e){return mh.resolve(null).then(e).catch(pp)}:hh;function pp(e){setTimeout(function(){throw e})}function qa(e){return e==="head"}function gh(e,t){var a=t,n=0,i=0;do{var o=a.nextSibling;if(e.removeChild(a),o&&o.nodeType===8)if(a=o.data,a==="/$"){if(0<n&&8>n){a=n;var h=e.ownerDocument;if(a&1&&Il(h.documentElement),a&2&&Il(h.body),a&4)for(a=h.head,Il(a),h=a.firstChild;h;){var g=h.nextSibling,j=h.nodeName;h[ml]||j==="SCRIPT"||j==="STYLE"||j==="LINK"&&h.rel.toLowerCase()==="stylesheet"||a.removeChild(h),h=g}}if(i===0){e.removeChild(o),rs(t);return}i--}else a==="$"||a==="$?"||a==="$!"?i++:n=a.charCodeAt(0)-48;else n=0;a=o}while(a);rs(t)}function Pc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Pc(a),tr(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function xp(e,t,a,n){for(;e.nodeType===1;){var i=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!n&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(n){if(!e[ml])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(o=e.getAttribute("rel"),o==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(o!==i.rel||e.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||e.getAttribute("title")!==(i.title==null?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(o=e.getAttribute("src"),(o!==(i.src==null?null:i.src)||e.getAttribute("type")!==(i.type==null?null:i.type)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&o&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var o=i.name==null?null:""+i.name;if(i.type==="hidden"&&e.getAttribute("name")===o)return e}else return e;if(e=Zt(e.nextSibling),e===null)break}return null}function yp(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=Zt(e.nextSibling),e===null))return null;return e}function Fc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function vp(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var n=function(){t(),a.removeEventListener("DOMContentLoaded",n)};a.addEventListener("DOMContentLoaded",n),e._reactRetry=n}}function Zt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Wc=null;function ph(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function xh(e,t,a){switch(t=bi(a),e){case"html":if(e=t.documentElement,!e)throw Error(c(452));return e;case"head":if(e=t.head,!e)throw Error(c(453));return e;case"body":if(e=t.body,!e)throw Error(c(454));return e;default:throw Error(c(451))}}function Il(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);tr(e)}var Xt=new Map,yh=new Set;function ji(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var va=J.d;J.d={f:bp,r:jp,D:Np,C:Sp,L:wp,m:Tp,X:_p,S:Ep,M:Op};function bp(){var e=va.f(),t=di();return e||t}function jp(e){var t=jn(e);t!==null&&t.tag===5&&t.type==="form"?Hf(t):va.r(e)}var tl=typeof document>"u"?null:document;function vh(e,t,a){var n=tl;if(n&&typeof t=="string"&&t){var i=Bt(t);i='link[rel="'+e+'"][href="'+i+'"]',typeof a=="string"&&(i+='[crossorigin="'+a+'"]'),yh.has(i)||(yh.add(i),e={rel:e,crossOrigin:a,href:t},n.querySelector(i)===null&&(t=n.createElement("link"),it(t,"link",e),Ie(t),n.head.appendChild(t)))}}function Np(e){va.D(e),vh("dns-prefetch",e,null)}function Sp(e,t){va.C(e,t),vh("preconnect",e,t)}function wp(e,t,a){va.L(e,t,a);var n=tl;if(n&&e&&t){var i='link[rel="preload"][as="'+Bt(t)+'"]';t==="image"&&a&&a.imageSrcSet?(i+='[imagesrcset="'+Bt(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(i+='[imagesizes="'+Bt(a.imageSizes)+'"]')):i+='[href="'+Bt(e)+'"]';var o=i;switch(t){case"style":o=al(e);break;case"script":o=nl(e)}Xt.has(o)||(e=v({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),Xt.set(o,e),n.querySelector(i)!==null||t==="style"&&n.querySelector(es(o))||t==="script"&&n.querySelector(ts(o))||(t=n.createElement("link"),it(t,"link",e),Ie(t),n.head.appendChild(t)))}}function Tp(e,t){va.m(e,t);var a=tl;if(a&&e){var n=t&&typeof t.as=="string"?t.as:"script",i='link[rel="modulepreload"][as="'+Bt(n)+'"][href="'+Bt(e)+'"]',o=i;switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":o=nl(e)}if(!Xt.has(o)&&(e=v({rel:"modulepreload",href:e},t),Xt.set(o,e),a.querySelector(i)===null)){switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(ts(o)))return}n=a.createElement("link"),it(n,"link",e),Ie(n),a.head.appendChild(n)}}}function Ep(e,t,a){va.S(e,t,a);var n=tl;if(n&&e){var i=Nn(n).hoistableStyles,o=al(e);t=t||"default";var h=i.get(o);if(!h){var g={loading:0,preload:null};if(h=n.querySelector(es(o)))g.loading=5;else{e=v({rel:"stylesheet",href:e,"data-precedence":t},a),(a=Xt.get(o))&&Ic(e,a);var j=h=n.createElement("link");Ie(j),it(j,"link",e),j._p=new Promise(function(z,Y){j.onload=z,j.onerror=Y}),j.addEventListener("load",function(){g.loading|=1}),j.addEventListener("error",function(){g.loading|=2}),g.loading|=4,Ni(h,t,n)}h={type:"stylesheet",instance:h,count:1,state:g},i.set(o,h)}}}function _p(e,t){va.X(e,t);var a=tl;if(a&&e){var n=Nn(a).hoistableScripts,i=nl(e),o=n.get(i);o||(o=a.querySelector(ts(i)),o||(e=v({src:e,async:!0},t),(t=Xt.get(i))&&eo(e,t),o=a.createElement("script"),Ie(o),it(o,"link",e),a.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},n.set(i,o))}}function Op(e,t){va.M(e,t);var a=tl;if(a&&e){var n=Nn(a).hoistableScripts,i=nl(e),o=n.get(i);o||(o=a.querySelector(ts(i)),o||(e=v({src:e,async:!0,type:"module"},t),(t=Xt.get(i))&&eo(e,t),o=a.createElement("script"),Ie(o),it(o,"link",e),a.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},n.set(i,o))}}function bh(e,t,a,n){var i=(i=re.current)?ji(i):null;if(!i)throw Error(c(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=al(a.href),a=Nn(i).hoistableStyles,n=a.get(t),n||(n={type:"style",instance:null,count:0,state:null},a.set(t,n)),n):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=al(a.href);var o=Nn(i).hoistableStyles,h=o.get(e);if(h||(i=i.ownerDocument||i,h={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},o.set(e,h),(o=i.querySelector(es(e)))&&!o._p&&(h.instance=o,h.state.loading=5),Xt.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Xt.set(e,a),o||Cp(i,e,a,h.state))),t&&n===null)throw Error(c(528,""));return h}if(t&&n!==null)throw Error(c(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=nl(a),a=Nn(i).hoistableScripts,n=a.get(t),n||(n={type:"script",instance:null,count:0,state:null},a.set(t,n)),n):{type:"void",instance:null,count:0,state:null};default:throw Error(c(444,e))}}function al(e){return'href="'+Bt(e)+'"'}function es(e){return'link[rel="stylesheet"]['+e+"]"}function jh(e){return v({},e,{"data-precedence":e.precedence,precedence:null})}function Cp(e,t,a,n){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?n.loading=1:(t=e.createElement("link"),n.preload=t,t.addEventListener("load",function(){return n.loading|=1}),t.addEventListener("error",function(){return n.loading|=2}),it(t,"link",a),Ie(t),e.head.appendChild(t))}function nl(e){return'[src="'+Bt(e)+'"]'}function ts(e){return"script[async]"+e}function Nh(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var n=e.querySelector('style[data-href~="'+Bt(a.href)+'"]');if(n)return t.instance=n,Ie(n),n;var i=v({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return n=(e.ownerDocument||e).createElement("style"),Ie(n),it(n,"style",i),Ni(n,a.precedence,e),t.instance=n;case"stylesheet":i=al(a.href);var o=e.querySelector(es(i));if(o)return t.state.loading|=4,t.instance=o,Ie(o),o;n=jh(a),(i=Xt.get(i))&&Ic(n,i),o=(e.ownerDocument||e).createElement("link"),Ie(o);var h=o;return h._p=new Promise(function(g,j){h.onload=g,h.onerror=j}),it(o,"link",n),t.state.loading|=4,Ni(o,a.precedence,e),t.instance=o;case"script":return o=nl(a.src),(i=e.querySelector(ts(o)))?(t.instance=i,Ie(i),i):(n=a,(i=Xt.get(o))&&(n=v({},a),eo(n,i)),e=e.ownerDocument||e,i=e.createElement("script"),Ie(i),it(i,"link",n),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(c(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(n=t.instance,t.state.loading|=4,Ni(n,a.precedence,e));return t.instance}function Ni(e,t,a){for(var n=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=n.length?n[n.length-1]:null,o=i,h=0;h<n.length;h++){var g=n[h];if(g.dataset.precedence===t)o=g;else if(o!==i)break}o?o.parentNode.insertBefore(e,o.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Ic(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function eo(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Si=null;function Sh(e,t,a){if(Si===null){var n=new Map,i=Si=new Map;i.set(a,n)}else i=Si,n=i.get(a),n||(n=new Map,i.set(a,n));if(n.has(e))return n;for(n.set(e,null),a=a.getElementsByTagName(e),i=0;i<a.length;i++){var o=a[i];if(!(o[ml]||o[ct]||e==="link"&&o.getAttribute("rel")==="stylesheet")&&o.namespaceURI!=="http://www.w3.org/2000/svg"){var h=o.getAttribute(t)||"";h=e+h;var g=n.get(h);g?g.push(o):n.set(h,[o])}}return n}function wh(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function Ap(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Th(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var as=null;function Rp(){}function Mp(e,t,a){if(as===null)throw Error(c(475));var n=as;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var i=al(a.href),o=e.querySelector(es(i));if(o){e=o._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(n.count++,n=wi.bind(n),e.then(n,n)),t.state.loading|=4,t.instance=o,Ie(o);return}o=e.ownerDocument||e,a=jh(a),(i=Xt.get(i))&&Ic(a,i),o=o.createElement("link"),Ie(o);var h=o;h._p=new Promise(function(g,j){h.onload=g,h.onerror=j}),it(o,"link",a),t.instance=o}n.stylesheets===null&&(n.stylesheets=new Map),n.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(n.count++,t=wi.bind(n),e.addEventListener("load",t),e.addEventListener("error",t))}}function zp(){if(as===null)throw Error(c(475));var e=as;return e.stylesheets&&e.count===0&&to(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&to(e,e.stylesheets),e.unsuspend){var n=e.unsuspend;e.unsuspend=null,n()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function wi(){if(this.count--,this.count===0){if(this.stylesheets)to(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Ti=null;function to(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Ti=new Map,t.forEach(Dp,e),Ti=null,wi.call(e))}function Dp(e,t){if(!(t.state.loading&4)){var a=Ti.get(e);if(a)var n=a.get(null);else{a=new Map,Ti.set(e,a);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),o=0;o<i.length;o++){var h=i[o];(h.nodeName==="LINK"||h.getAttribute("media")!=="not all")&&(a.set(h.dataset.precedence,h),n=h)}n&&a.set(null,n)}i=t.instance,h=i.getAttribute("data-precedence"),o=a.get(h)||n,o===n&&a.set(null,i),a.set(h,i),this.count++,n=wi.bind(this),i.addEventListener("load",n),i.addEventListener("error",n),o?o.parentNode.insertBefore(i,o.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(i,e.firstChild)),t.state.loading|=4}}var ns={$$typeof:w,Provider:null,Consumer:null,_currentValue:ee,_currentValue2:ee,_threadCount:0};function Up(e,t,a,n,i,o,h,g){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Fi(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Fi(0),this.hiddenUpdates=Fi(null),this.identifierPrefix=n,this.onUncaughtError=i,this.onCaughtError=o,this.onRecoverableError=h,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=g,this.incompleteTransitions=new Map}function Eh(e,t,a,n,i,o,h,g,j,z,Y,V){return e=new Up(e,t,a,h,g,j,z,V),t=1,o===!0&&(t|=24),o=_t(3,null,null,t),e.current=o,o.stateNode=e,t=Br(),t.refCount++,e.pooledCache=t,t.refCount++,o.memoizedState={element:n,isDehydrated:a,cache:t},qr(o),e}function _h(e){return e?(e=Dn,e):Dn}function Oh(e,t,a,n,i,o){i=_h(i),n.context===null?n.context=i:n.pendingContext=i,n=_a(t),n.payload={element:a},o=o===void 0?null:o,o!==null&&(n.callback=o),a=Oa(e,n,t),a!==null&&(Mt(a,e,t),zl(a,e,t))}function Ch(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function ao(e,t){Ch(e,t),(e=e.alternate)&&Ch(e,t)}function Ah(e){if(e.tag===13){var t=zn(e,67108864);t!==null&&Mt(t,e,67108864),ao(e,67108864)}}var Ei=!0;function Bp(e,t,a,n){var i=G.T;G.T=null;var o=J.p;try{J.p=2,no(e,t,a,n)}finally{J.p=o,G.T=i}}function Lp(e,t,a,n){var i=G.T;G.T=null;var o=J.p;try{J.p=8,no(e,t,a,n)}finally{J.p=o,G.T=i}}function no(e,t,a,n){if(Ei){var i=lo(n);if(i===null)Vc(e,t,n,_i,a),Mh(e,n);else if(kp(i,e,t,a,n))n.stopPropagation();else if(Mh(e,n),t&4&&-1<Hp.indexOf(e)){for(;i!==null;){var o=jn(i);if(o!==null)switch(o.tag){case 3:if(o=o.stateNode,o.current.memoizedState.isDehydrated){var h=$a(o.pendingLanes);if(h!==0){var g=o;for(g.pendingLanes|=2,g.entangledLanes|=2;h;){var j=1<<31-Tt(h);g.entanglements[1]|=j,h&=~j}ta(o),(Ae&6)===0&&(ui=_e()+500,Pl(0))}}break;case 13:g=zn(o,2),g!==null&&Mt(g,o,2),di(),ao(o,2)}if(o=lo(n),o===null&&Vc(e,t,n,_i,a),o===i)break;i=o}i!==null&&n.stopPropagation()}else Vc(e,t,n,null,a)}}function lo(e){return e=or(e),so(e)}var _i=null;function so(e){if(_i=null,e=bn(e),e!==null){var t=d(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=m(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return _i=e,null}function Rh(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(We()){case Pt:return 2;case ht:return 8;case la:case w0:return 32;case Zo:return 268435456;default:return 32}default:return 32}}var io=!1,Ga=null,Ya=null,Xa=null,ls=new Map,ss=new Map,Va=[],Hp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Mh(e,t){switch(e){case"focusin":case"focusout":Ga=null;break;case"dragenter":case"dragleave":Ya=null;break;case"mouseover":case"mouseout":Xa=null;break;case"pointerover":case"pointerout":ls.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ss.delete(t.pointerId)}}function is(e,t,a,n,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:a,eventSystemFlags:n,nativeEvent:o,targetContainers:[i]},t!==null&&(t=jn(t),t!==null&&Ah(t)),e):(e.eventSystemFlags|=n,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function kp(e,t,a,n,i){switch(t){case"focusin":return Ga=is(Ga,e,t,a,n,i),!0;case"dragenter":return Ya=is(Ya,e,t,a,n,i),!0;case"mouseover":return Xa=is(Xa,e,t,a,n,i),!0;case"pointerover":var o=i.pointerId;return ls.set(o,is(ls.get(o)||null,e,t,a,n,i)),!0;case"gotpointercapture":return o=i.pointerId,ss.set(o,is(ss.get(o)||null,e,t,a,n,i)),!0}return!1}function zh(e){var t=bn(e.target);if(t!==null){var a=d(t);if(a!==null){if(t=a.tag,t===13){if(t=m(a),t!==null){e.blockedOn=t,M0(e.priority,function(){if(a.tag===13){var n=Rt();n=Wi(n);var i=zn(a,n);i!==null&&Mt(i,a,n),ao(a,n)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Oi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=lo(e.nativeEvent);if(a===null){a=e.nativeEvent;var n=new a.constructor(a.type,a);cr=n,a.target.dispatchEvent(n),cr=null}else return t=jn(a),t!==null&&Ah(t),e.blockedOn=a,!1;t.shift()}return!0}function Dh(e,t,a){Oi(e)&&a.delete(t)}function qp(){io=!1,Ga!==null&&Oi(Ga)&&(Ga=null),Ya!==null&&Oi(Ya)&&(Ya=null),Xa!==null&&Oi(Xa)&&(Xa=null),ls.forEach(Dh),ss.forEach(Dh)}function Ci(e,t){e.blockedOn===t&&(e.blockedOn=null,io||(io=!0,l.unstable_scheduleCallback(l.unstable_NormalPriority,qp)))}var Ai=null;function Uh(e){Ai!==e&&(Ai=e,l.unstable_scheduleCallback(l.unstable_NormalPriority,function(){Ai===e&&(Ai=null);for(var t=0;t<e.length;t+=3){var a=e[t],n=e[t+1],i=e[t+2];if(typeof n!="function"){if(so(n||a)===null)continue;break}var o=jn(a);o!==null&&(e.splice(t,3),t-=3,sc(o,{pending:!0,data:i,method:a.method,action:n},n,i))}}))}function rs(e){function t(j){return Ci(j,e)}Ga!==null&&Ci(Ga,e),Ya!==null&&Ci(Ya,e),Xa!==null&&Ci(Xa,e),ls.forEach(t),ss.forEach(t);for(var a=0;a<Va.length;a++){var n=Va[a];n.blockedOn===e&&(n.blockedOn=null)}for(;0<Va.length&&(a=Va[0],a.blockedOn===null);)zh(a),a.blockedOn===null&&Va.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(n=0;n<a.length;n+=3){var i=a[n],o=a[n+1],h=i[pt]||null;if(typeof o=="function")h||Uh(a);else if(h){var g=null;if(o&&o.hasAttribute("formAction")){if(i=o,h=o[pt]||null)g=h.formAction;else if(so(i)!==null)continue}else g=h.action;typeof g=="function"?a[n+1]=g:(a.splice(n,3),n-=3),Uh(a)}}}function ro(e){this._internalRoot=e}Ri.prototype.render=ro.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(c(409));var a=t.current,n=Rt();Oh(a,n,e,t,null,null)},Ri.prototype.unmount=ro.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Oh(e.current,2,null,e,null,null),di(),t[vn]=null}};function Ri(e){this._internalRoot=e}Ri.prototype.unstable_scheduleHydration=function(e){if(e){var t=Fo();e={blockedOn:null,target:e,priority:t};for(var a=0;a<Va.length&&t!==0&&t<Va[a].priority;a++);Va.splice(a,0,e),a===0&&zh(e)}};var Bh=r.version;if(Bh!=="19.1.0")throw Error(c(527,Bh,"19.1.0"));J.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(c(188)):(e=Object.keys(e).join(","),Error(c(268,e)));return e=y(t),e=e!==null?p(e):null,e=e===null?null:e.stateNode,e};var Gp={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:G,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Mi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Mi.isDisabled&&Mi.supportsFiber)try{fl=Mi.inject(Gp),wt=Mi}catch{}}return os.createRoot=function(e,t){if(!f(e))throw Error(c(299));var a=!1,n="",i=Wf,o=If,h=ed,g=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(n=t.identifierPrefix),t.onUncaughtError!==void 0&&(i=t.onUncaughtError),t.onCaughtError!==void 0&&(o=t.onCaughtError),t.onRecoverableError!==void 0&&(h=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(g=t.unstable_transitionCallbacks)),t=Eh(e,1,!1,null,null,a,n,i,o,h,g,null),e[vn]=t.current,Xc(e),new ro(t)},os.hydrateRoot=function(e,t,a){if(!f(e))throw Error(c(299));var n=!1,i="",o=Wf,h=If,g=ed,j=null,z=null;return a!=null&&(a.unstable_strictMode===!0&&(n=!0),a.identifierPrefix!==void 0&&(i=a.identifierPrefix),a.onUncaughtError!==void 0&&(o=a.onUncaughtError),a.onCaughtError!==void 0&&(h=a.onCaughtError),a.onRecoverableError!==void 0&&(g=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(j=a.unstable_transitionCallbacks),a.formState!==void 0&&(z=a.formState)),t=Eh(e,1,!0,t,a??null,n,i,o,h,g,j,z),t.context=_h(null),a=t.current,n=Rt(),n=Wi(n),i=_a(n),i.callback=null,Oa(a,i,n),a=n,t.current.lanes=a,hl(t,a),ta(t),e[vn]=t.current,Xc(e),new Ri(t)},os.version="19.1.0",os}var Zh;function Ip(){if(Zh)return uo.exports;Zh=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(r){console.error(r)}}return l(),uo.exports=Wp(),uo.exports}var ex=Ip();Em();/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function fs(){return fs=Object.assign?Object.assign.bind():function(l){for(var r=1;r<arguments.length;r++){var u=arguments[r];for(var c in u)Object.prototype.hasOwnProperty.call(u,c)&&(l[c]=u[c])}return l},fs.apply(this,arguments)}var Za;(function(l){l.Pop="POP",l.Push="PUSH",l.Replace="REPLACE"})(Za||(Za={}));const Kh="popstate";function tx(l){l===void 0&&(l={});function r(c,f){let{pathname:d,search:m,hash:x}=c.location;return jo("",{pathname:d,search:m,hash:x},f.state&&f.state.usr||null,f.state&&f.state.key||"default")}function u(c,f){return typeof f=="string"?f:Bi(f)}return nx(r,u,null,l)}function Qe(l,r){if(l===!1||l===null||typeof l>"u")throw new Error(r)}function _m(l,r){if(!l){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function ax(){return Math.random().toString(36).substr(2,8)}function Jh(l,r){return{usr:l.state,key:l.key,idx:r}}function jo(l,r,u,c){return u===void 0&&(u=null),fs({pathname:typeof l=="string"?l:l.pathname,search:"",hash:""},typeof r=="string"?sl(r):r,{state:u,key:r&&r.key||c||ax()})}function Bi(l){let{pathname:r="/",search:u="",hash:c=""}=l;return u&&u!=="?"&&(r+=u.charAt(0)==="?"?u:"?"+u),c&&c!=="#"&&(r+=c.charAt(0)==="#"?c:"#"+c),r}function sl(l){let r={};if(l){let u=l.indexOf("#");u>=0&&(r.hash=l.substr(u),l=l.substr(0,u));let c=l.indexOf("?");c>=0&&(r.search=l.substr(c),l=l.substr(0,c)),l&&(r.pathname=l)}return r}function nx(l,r,u,c){c===void 0&&(c={});let{window:f=document.defaultView,v5Compat:d=!1}=c,m=f.history,x=Za.Pop,y=null,p=v();p==null&&(p=0,m.replaceState(fs({},m.state,{idx:p}),""));function v(){return(m.state||{idx:null}).idx}function b(){x=Za.Pop;let M=v(),R=M==null?null:M-p;p=M,y&&y({action:x,location:E.location,delta:R})}function O(M,R){x=Za.Push;let D=jo(E.location,M,R);p=v()+1;let w=Jh(D,p),q=E.createHref(D);try{m.pushState(w,"",q)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;f.location.assign(q)}d&&y&&y({action:x,location:E.location,delta:1})}function H(M,R){x=Za.Replace;let D=jo(E.location,M,R);p=v();let w=Jh(D,p),q=E.createHref(D);m.replaceState(w,"",q),d&&y&&y({action:x,location:E.location,delta:0})}function U(M){let R=f.location.origin!=="null"?f.location.origin:f.location.href,D=typeof M=="string"?M:Bi(M);return D=D.replace(/ $/,"%20"),Qe(R,"No window.location.(origin|href) available to create URL for href: "+D),new URL(D,R)}let E={get action(){return x},get location(){return l(f,m)},listen(M){if(y)throw new Error("A history only accepts one active listener");return f.addEventListener(Kh,b),y=M,()=>{f.removeEventListener(Kh,b),y=null}},createHref(M){return r(f,M)},createURL:U,encodeLocation(M){let R=U(M);return{pathname:R.pathname,search:R.search,hash:R.hash}},push:O,replace:H,go(M){return m.go(M)}};return E}var $h;(function(l){l.data="data",l.deferred="deferred",l.redirect="redirect",l.error="error"})($h||($h={}));function lx(l,r,u){return u===void 0&&(u="/"),sx(l,r,u)}function sx(l,r,u,c){let f=typeof r=="string"?sl(r):r,d=Bo(f.pathname||"/",u);if(d==null)return null;let m=Om(l);ix(m);let x=null;for(let y=0;x==null&&y<m.length;++y){let p=yx(d);x=gx(m[y],p)}return x}function Om(l,r,u,c){r===void 0&&(r=[]),u===void 0&&(u=[]),c===void 0&&(c="");let f=(d,m,x)=>{let y={relativePath:x===void 0?d.path||"":x,caseSensitive:d.caseSensitive===!0,childrenIndex:m,route:d};y.relativePath.startsWith("/")&&(Qe(y.relativePath.startsWith(c),'Absolute route path "'+y.relativePath+'" nested under path '+('"'+c+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),y.relativePath=y.relativePath.slice(c.length));let p=Ka([c,y.relativePath]),v=u.concat(y);d.children&&d.children.length>0&&(Qe(d.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+p+'".')),Om(d.children,r,v,p)),!(d.path==null&&!d.index)&&r.push({path:p,score:hx(p,d.index),routesMeta:v})};return l.forEach((d,m)=>{var x;if(d.path===""||!((x=d.path)!=null&&x.includes("?")))f(d,m);else for(let y of Cm(d.path))f(d,m,y)}),r}function Cm(l){let r=l.split("/");if(r.length===0)return[];let[u,...c]=r,f=u.endsWith("?"),d=u.replace(/\?$/,"");if(c.length===0)return f?[d,""]:[d];let m=Cm(c.join("/")),x=[];return x.push(...m.map(y=>y===""?d:[d,y].join("/"))),f&&x.push(...m),x.map(y=>l.startsWith("/")&&y===""?"/":y)}function ix(l){l.sort((r,u)=>r.score!==u.score?u.score-r.score:mx(r.routesMeta.map(c=>c.childrenIndex),u.routesMeta.map(c=>c.childrenIndex)))}const rx=/^:[\w-]+$/,cx=3,ox=2,ux=1,fx=10,dx=-2,Ph=l=>l==="*";function hx(l,r){let u=l.split("/"),c=u.length;return u.some(Ph)&&(c+=dx),r&&(c+=ox),u.filter(f=>!Ph(f)).reduce((f,d)=>f+(rx.test(d)?cx:d===""?ux:fx),c)}function mx(l,r){return l.length===r.length&&l.slice(0,-1).every((c,f)=>c===r[f])?l[l.length-1]-r[r.length-1]:0}function gx(l,r,u){let{routesMeta:c}=l,f={},d="/",m=[];for(let x=0;x<c.length;++x){let y=c[x],p=x===c.length-1,v=d==="/"?r:r.slice(d.length)||"/",b=px({path:y.relativePath,caseSensitive:y.caseSensitive,end:p},v),O=y.route;if(!b)return null;Object.assign(f,b.params),m.push({params:f,pathname:Ka([d,b.pathname]),pathnameBase:Nx(Ka([d,b.pathnameBase])),route:O}),b.pathnameBase!=="/"&&(d=Ka([d,b.pathnameBase]))}return m}function px(l,r){typeof l=="string"&&(l={path:l,caseSensitive:!1,end:!0});let[u,c]=xx(l.path,l.caseSensitive,l.end),f=r.match(u);if(!f)return null;let d=f[0],m=d.replace(/(.)\/+$/,"$1"),x=f.slice(1);return{params:c.reduce((p,v,b)=>{let{paramName:O,isOptional:H}=v;if(O==="*"){let E=x[b]||"";m=d.slice(0,d.length-E.length).replace(/(.)\/+$/,"$1")}const U=x[b];return H&&!U?p[O]=void 0:p[O]=(U||"").replace(/%2F/g,"/"),p},{}),pathname:d,pathnameBase:m,pattern:l}}function xx(l,r,u){r===void 0&&(r=!1),u===void 0&&(u=!0),_m(l==="*"||!l.endsWith("*")||l.endsWith("/*"),'Route path "'+l+'" will be treated as if it were '+('"'+l.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+l.replace(/\*$/,"/*")+'".'));let c=[],f="^"+l.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(m,x,y)=>(c.push({paramName:x,isOptional:y!=null}),y?"/?([^\\/]+)?":"/([^\\/]+)"));return l.endsWith("*")?(c.push({paramName:"*"}),f+=l==="*"||l==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):u?f+="\\/*$":l!==""&&l!=="/"&&(f+="(?:(?=\\/|$))"),[new RegExp(f,r?void 0:"i"),c]}function yx(l){try{return l.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return _m(!1,'The URL path "'+l+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+r+").")),l}}function Bo(l,r){if(r==="/")return l;if(!l.toLowerCase().startsWith(r.toLowerCase()))return null;let u=r.endsWith("/")?r.length-1:r.length,c=l.charAt(u);return c&&c!=="/"?null:l.slice(u)||"/"}function vx(l,r){r===void 0&&(r="/");let{pathname:u,search:c="",hash:f=""}=typeof l=="string"?sl(l):l;return{pathname:u?u.startsWith("/")?u:bx(u,r):r,search:Sx(c),hash:wx(f)}}function bx(l,r){let u=r.replace(/\/+$/,"").split("/");return l.split("/").forEach(f=>{f===".."?u.length>1&&u.pop():f!=="."&&u.push(f)}),u.length>1?u.join("/"):"/"}function go(l,r,u,c){return"Cannot include a '"+l+"' character in a manually specified "+("`to."+r+"` field ["+JSON.stringify(c)+"].  Please separate it out to the ")+("`to."+u+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function jx(l){return l.filter((r,u)=>u===0||r.route.path&&r.route.path.length>0)}function Lo(l,r){let u=jx(l);return r?u.map((c,f)=>f===u.length-1?c.pathname:c.pathnameBase):u.map(c=>c.pathnameBase)}function Ho(l,r,u,c){c===void 0&&(c=!1);let f;typeof l=="string"?f=sl(l):(f=fs({},l),Qe(!f.pathname||!f.pathname.includes("?"),go("?","pathname","search",f)),Qe(!f.pathname||!f.pathname.includes("#"),go("#","pathname","hash",f)),Qe(!f.search||!f.search.includes("#"),go("#","search","hash",f)));let d=l===""||f.pathname==="",m=d?"/":f.pathname,x;if(m==null)x=u;else{let b=r.length-1;if(!c&&m.startsWith("..")){let O=m.split("/");for(;O[0]==="..";)O.shift(),b-=1;f.pathname=O.join("/")}x=b>=0?r[b]:"/"}let y=vx(f,x),p=m&&m!=="/"&&m.endsWith("/"),v=(d||m===".")&&u.endsWith("/");return!y.pathname.endsWith("/")&&(p||v)&&(y.pathname+="/"),y}const Ka=l=>l.join("/").replace(/\/\/+/g,"/"),Nx=l=>l.replace(/\/+$/,"").replace(/^\/*/,"/"),Sx=l=>!l||l==="?"?"":l.startsWith("?")?l:"?"+l,wx=l=>!l||l==="#"?"":l.startsWith("#")?l:"#"+l;function Tx(l){return l!=null&&typeof l.status=="number"&&typeof l.statusText=="string"&&typeof l.internal=="boolean"&&"data"in l}const Am=["post","put","patch","delete"];new Set(Am);const Ex=["get",...Am];new Set(Ex);/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ds(){return ds=Object.assign?Object.assign.bind():function(l){for(var r=1;r<arguments.length;r++){var u=arguments[r];for(var c in u)Object.prototype.hasOwnProperty.call(u,c)&&(l[c]=u[c])}return l},ds.apply(this,arguments)}const ko=N.createContext(null),_x=N.createContext(null),Ja=N.createContext(null),Gi=N.createContext(null),ja=N.createContext({outlet:null,matches:[],isDataRoute:!1}),Rm=N.createContext(null);function Ox(l,r){let{relative:u}=r===void 0?{}:r;il()||Qe(!1);let{basename:c,navigator:f}=N.useContext(Ja),{hash:d,pathname:m,search:x}=zm(l,{relative:u}),y=m;return c!=="/"&&(y=m==="/"?c:Ka([c,m])),f.createHref({pathname:y,search:x,hash:d})}function il(){return N.useContext(Gi)!=null}function rl(){return il()||Qe(!1),N.useContext(Gi).location}function Mm(l){N.useContext(Ja).static||N.useLayoutEffect(l)}function Jt(){let{isDataRoute:l}=N.useContext(ja);return l?Yx():Cx()}function Cx(){il()||Qe(!1);let l=N.useContext(ko),{basename:r,future:u,navigator:c}=N.useContext(Ja),{matches:f}=N.useContext(ja),{pathname:d}=rl(),m=JSON.stringify(Lo(f,u.v7_relativeSplatPath)),x=N.useRef(!1);return Mm(()=>{x.current=!0}),N.useCallback(function(p,v){if(v===void 0&&(v={}),!x.current)return;if(typeof p=="number"){c.go(p);return}let b=Ho(p,JSON.parse(m),d,v.relative==="path");l==null&&r!=="/"&&(b.pathname=b.pathname==="/"?r:Ka([r,b.pathname])),(v.replace?c.replace:c.push)(b,v.state,v)},[r,c,m,d,l])}function Ax(){let{matches:l}=N.useContext(ja),r=l[l.length-1];return r?r.params:{}}function zm(l,r){let{relative:u}=r===void 0?{}:r,{future:c}=N.useContext(Ja),{matches:f}=N.useContext(ja),{pathname:d}=rl(),m=JSON.stringify(Lo(f,c.v7_relativeSplatPath));return N.useMemo(()=>Ho(l,JSON.parse(m),d,u==="path"),[l,m,d,u])}function Rx(l,r){return Mx(l,r)}function Mx(l,r,u,c){il()||Qe(!1);let{navigator:f,static:d}=N.useContext(Ja),{matches:m}=N.useContext(ja),x=m[m.length-1],y=x?x.params:{};x&&x.pathname;let p=x?x.pathnameBase:"/";x&&x.route;let v=rl(),b;if(r){var O;let R=typeof r=="string"?sl(r):r;p==="/"||(O=R.pathname)!=null&&O.startsWith(p)||Qe(!1),b=R}else b=v;let H=b.pathname||"/",U=H;if(p!=="/"){let R=p.replace(/^\//,"").split("/");U="/"+H.replace(/^\//,"").split("/").slice(R.length).join("/")}let E=lx(l,{pathname:U}),M=Lx(E&&E.map(R=>Object.assign({},R,{params:Object.assign({},y,R.params),pathname:Ka([p,f.encodeLocation?f.encodeLocation(R.pathname).pathname:R.pathname]),pathnameBase:R.pathnameBase==="/"?p:Ka([p,f.encodeLocation?f.encodeLocation(R.pathnameBase).pathname:R.pathnameBase])})),m,u,c);return r&&M?N.createElement(Gi.Provider,{value:{location:ds({pathname:"/",search:"",hash:"",state:null,key:"default"},b),navigationType:Za.Pop}},M):M}function zx(){let l=Gx(),r=Tx(l)?l.status+" "+l.statusText:l instanceof Error?l.message:JSON.stringify(l),u=l instanceof Error?l.stack:null,f={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return N.createElement(N.Fragment,null,N.createElement("h2",null,"Unexpected Application Error!"),N.createElement("h3",{style:{fontStyle:"italic"}},r),u?N.createElement("pre",{style:f},u):null,null)}const Dx=N.createElement(zx,null);class Ux extends N.Component{constructor(r){super(r),this.state={location:r.location,revalidation:r.revalidation,error:r.error}}static getDerivedStateFromError(r){return{error:r}}static getDerivedStateFromProps(r,u){return u.location!==r.location||u.revalidation!=="idle"&&r.revalidation==="idle"?{error:r.error,location:r.location,revalidation:r.revalidation}:{error:r.error!==void 0?r.error:u.error,location:u.location,revalidation:r.revalidation||u.revalidation}}componentDidCatch(r,u){console.error("React Router caught the following error during render",r,u)}render(){return this.state.error!==void 0?N.createElement(ja.Provider,{value:this.props.routeContext},N.createElement(Rm.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Bx(l){let{routeContext:r,match:u,children:c}=l,f=N.useContext(ko);return f&&f.static&&f.staticContext&&(u.route.errorElement||u.route.ErrorBoundary)&&(f.staticContext._deepestRenderedBoundaryId=u.route.id),N.createElement(ja.Provider,{value:r},c)}function Lx(l,r,u,c){var f;if(r===void 0&&(r=[]),u===void 0&&(u=null),c===void 0&&(c=null),l==null){var d;if(!u)return null;if(u.errors)l=u.matches;else if((d=c)!=null&&d.v7_partialHydration&&r.length===0&&!u.initialized&&u.matches.length>0)l=u.matches;else return null}let m=l,x=(f=u)==null?void 0:f.errors;if(x!=null){let v=m.findIndex(b=>b.route.id&&(x==null?void 0:x[b.route.id])!==void 0);v>=0||Qe(!1),m=m.slice(0,Math.min(m.length,v+1))}let y=!1,p=-1;if(u&&c&&c.v7_partialHydration)for(let v=0;v<m.length;v++){let b=m[v];if((b.route.HydrateFallback||b.route.hydrateFallbackElement)&&(p=v),b.route.id){let{loaderData:O,errors:H}=u,U=b.route.loader&&O[b.route.id]===void 0&&(!H||H[b.route.id]===void 0);if(b.route.lazy||U){y=!0,p>=0?m=m.slice(0,p+1):m=[m[0]];break}}}return m.reduceRight((v,b,O)=>{let H,U=!1,E=null,M=null;u&&(H=x&&b.route.id?x[b.route.id]:void 0,E=b.route.errorElement||Dx,y&&(p<0&&O===0?(Xx("route-fallback"),U=!0,M=null):p===O&&(U=!0,M=b.route.hydrateFallbackElement||null)));let R=r.concat(m.slice(0,O+1)),D=()=>{let w;return H?w=E:U?w=M:b.route.Component?w=N.createElement(b.route.Component,null):b.route.element?w=b.route.element:w=v,N.createElement(Bx,{match:b,routeContext:{outlet:v,matches:R,isDataRoute:u!=null},children:w})};return u&&(b.route.ErrorBoundary||b.route.errorElement||O===0)?N.createElement(Ux,{location:u.location,revalidation:u.revalidation,component:E,error:H,children:D(),routeContext:{outlet:null,matches:R,isDataRoute:!0}}):D()},null)}var Dm=function(l){return l.UseBlocker="useBlocker",l.UseRevalidator="useRevalidator",l.UseNavigateStable="useNavigate",l}(Dm||{}),Um=function(l){return l.UseBlocker="useBlocker",l.UseLoaderData="useLoaderData",l.UseActionData="useActionData",l.UseRouteError="useRouteError",l.UseNavigation="useNavigation",l.UseRouteLoaderData="useRouteLoaderData",l.UseMatches="useMatches",l.UseRevalidator="useRevalidator",l.UseNavigateStable="useNavigate",l.UseRouteId="useRouteId",l}(Um||{});function Hx(l){let r=N.useContext(ko);return r||Qe(!1),r}function kx(l){let r=N.useContext(_x);return r||Qe(!1),r}function qx(l){let r=N.useContext(ja);return r||Qe(!1),r}function Bm(l){let r=qx(),u=r.matches[r.matches.length-1];return u.route.id||Qe(!1),u.route.id}function Gx(){var l;let r=N.useContext(Rm),u=kx(),c=Bm();return r!==void 0?r:(l=u.errors)==null?void 0:l[c]}function Yx(){let{router:l}=Hx(Dm.UseNavigateStable),r=Bm(Um.UseNavigateStable),u=N.useRef(!1);return Mm(()=>{u.current=!0}),N.useCallback(function(f,d){d===void 0&&(d={}),u.current&&(typeof f=="number"?l.navigate(f):l.navigate(f,ds({fromRouteId:r},d)))},[l,r])}const Fh={};function Xx(l,r,u){Fh[l]||(Fh[l]=!0)}function Vx(l,r){l==null||l.v7_startTransition,l==null||l.v7_relativeSplatPath}function Wh(l){let{to:r,replace:u,state:c,relative:f}=l;il()||Qe(!1);let{future:d,static:m}=N.useContext(Ja),{matches:x}=N.useContext(ja),{pathname:y}=rl(),p=Jt(),v=Ho(r,Lo(x,d.v7_relativeSplatPath),y,f==="path"),b=JSON.stringify(v);return N.useEffect(()=>p(JSON.parse(b),{replace:u,state:c,relative:f}),[p,b,f,u,c]),null}function zt(l){Qe(!1)}function Qx(l){let{basename:r="/",children:u=null,location:c,navigationType:f=Za.Pop,navigator:d,static:m=!1,future:x}=l;il()&&Qe(!1);let y=r.replace(/^\/*/,"/"),p=N.useMemo(()=>({basename:y,navigator:d,static:m,future:ds({v7_relativeSplatPath:!1},x)}),[y,x,d,m]);typeof c=="string"&&(c=sl(c));let{pathname:v="/",search:b="",hash:O="",state:H=null,key:U="default"}=c,E=N.useMemo(()=>{let M=Bo(v,y);return M==null?null:{location:{pathname:M,search:b,hash:O,state:H,key:U},navigationType:f}},[y,v,b,O,H,U,f]);return E==null?null:N.createElement(Ja.Provider,{value:p},N.createElement(Gi.Provider,{children:u,value:E}))}function Zx(l){let{children:r,location:u}=l;return Rx(No(r),u)}new Promise(()=>{});function No(l,r){r===void 0&&(r=[]);let u=[];return N.Children.forEach(l,(c,f)=>{if(!N.isValidElement(c))return;let d=[...r,f];if(c.type===N.Fragment){u.push.apply(u,No(c.props.children,d));return}c.type!==zt&&Qe(!1),!c.props.index||!c.props.children||Qe(!1);let m={id:c.props.id||d.join("-"),caseSensitive:c.props.caseSensitive,element:c.props.element,Component:c.props.Component,index:c.props.index,path:c.props.path,loader:c.props.loader,action:c.props.action,errorElement:c.props.errorElement,ErrorBoundary:c.props.ErrorBoundary,hasErrorBoundary:c.props.ErrorBoundary!=null||c.props.errorElement!=null,shouldRevalidate:c.props.shouldRevalidate,handle:c.props.handle,lazy:c.props.lazy};c.props.children&&(m.children=No(c.props.children,d)),u.push(m)}),u}/**
 * React Router DOM v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function So(){return So=Object.assign?Object.assign.bind():function(l){for(var r=1;r<arguments.length;r++){var u=arguments[r];for(var c in u)Object.prototype.hasOwnProperty.call(u,c)&&(l[c]=u[c])}return l},So.apply(this,arguments)}function Kx(l,r){if(l==null)return{};var u={},c=Object.keys(l),f,d;for(d=0;d<c.length;d++)f=c[d],!(r.indexOf(f)>=0)&&(u[f]=l[f]);return u}function Jx(l){return!!(l.metaKey||l.altKey||l.ctrlKey||l.shiftKey)}function $x(l,r){return l.button===0&&(!r||r==="_self")&&!Jx(l)}const Px=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Fx="6";try{window.__reactRouterVersion=Fx}catch{}const Wx="startTransition",Ih=Jp[Wx];function Ix(l){let{basename:r,children:u,future:c,window:f}=l,d=N.useRef();d.current==null&&(d.current=tx({window:f,v5Compat:!0}));let m=d.current,[x,y]=N.useState({action:m.action,location:m.location}),{v7_startTransition:p}=c||{},v=N.useCallback(b=>{p&&Ih?Ih(()=>y(b)):y(b)},[y,p]);return N.useLayoutEffect(()=>m.listen(v),[m,v]),N.useEffect(()=>Vx(c),[c]),N.createElement(Qx,{basename:r,children:u,location:x.location,navigationType:x.action,navigator:m,future:c})}const ey=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",ty=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ll=N.forwardRef(function(r,u){let{onClick:c,relative:f,reloadDocument:d,replace:m,state:x,target:y,to:p,preventScrollReset:v,viewTransition:b}=r,O=Kx(r,Px),{basename:H}=N.useContext(Ja),U,E=!1;if(typeof p=="string"&&ty.test(p)&&(U=p,ey))try{let w=new URL(window.location.href),q=p.startsWith("//")?new URL(w.protocol+p):new URL(p),C=Bo(q.pathname,H);q.origin===w.origin&&C!=null?p=C+q.search+q.hash:E=!0}catch{}let M=Ox(p,{relative:f}),R=ay(p,{replace:m,state:x,target:y,preventScrollReset:v,relative:f,viewTransition:b});function D(w){c&&c(w),w.defaultPrevented||R(w)}return N.createElement("a",So({},O,{href:U||M,onClick:E||d?c:D,ref:u,target:y}))});var em;(function(l){l.UseScrollRestoration="useScrollRestoration",l.UseSubmit="useSubmit",l.UseSubmitFetcher="useSubmitFetcher",l.UseFetcher="useFetcher",l.useViewTransitionState="useViewTransitionState"})(em||(em={}));var tm;(function(l){l.UseFetcher="useFetcher",l.UseFetchers="useFetchers",l.UseScrollRestoration="useScrollRestoration"})(tm||(tm={}));function ay(l,r){let{target:u,replace:c,state:f,preventScrollReset:d,relative:m,viewTransition:x}=r===void 0?{}:r,y=Jt(),p=rl(),v=zm(l,{relative:m});return N.useCallback(b=>{if($x(b,u)){b.preventDefault();let O=c!==void 0?c:Bi(p)===Bi(v);y(l,{replace:O,state:f,preventScrollReset:d,relative:m,viewTransition:x})}},[p,y,v,c,f,u,l,d,m,x])}const Lm=N.createContext(),qo=()=>{const l=N.useContext(Lm);if(!l)throw new Error("useCart must be used within a CartProvider");return l},ny=({children:l})=>{const[r,u]=N.useState([]),[c,f]=N.useState(!1);N.useEffect(()=>{const b=localStorage.getItem("cart");b&&u(JSON.parse(b))},[]),N.useEffect(()=>{localStorage.setItem("cart",JSON.stringify(r))},[r]);const d=b=>{u(O=>O.find(U=>U.id===b.id)?O.map(U=>U.id===b.id?{...U,quantity:U.quantity+1}:U):[...O,{...b,quantity:1}])},m=b=>{u(O=>O.filter(H=>H.id!==b))},x=(b,O)=>{if(O<=0){m(b);return}u(H=>H.map(U=>U.id===b?{...U,quantity:O}:U))},y=()=>r.reduce((O,H)=>{const E=parseFloat(H.price)*H.quantity;return O+E},0),p=()=>r.reduce((b,O)=>b+O.quantity,0),v=()=>{u([])};return s.jsx(Lm.Provider,{value:{cartItems:r,addToCart:d,removeFromCart:m,updateQuantity:x,getTotalPrice:y,getTotalItems:p,clearCart:v,isCartOpen:c,setIsCartOpen:f},children:l})};var Hm={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},am=me.createContext&&me.createContext(Hm),ly=["attr","size","title"];function sy(l,r){if(l==null)return{};var u=iy(l,r),c,f;if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(l);for(f=0;f<d.length;f++)c=d[f],!(r.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(l,c)&&(u[c]=l[c])}return u}function iy(l,r){if(l==null)return{};var u={};for(var c in l)if(Object.prototype.hasOwnProperty.call(l,c)){if(r.indexOf(c)>=0)continue;u[c]=l[c]}return u}function Li(){return Li=Object.assign?Object.assign.bind():function(l){for(var r=1;r<arguments.length;r++){var u=arguments[r];for(var c in u)Object.prototype.hasOwnProperty.call(u,c)&&(l[c]=u[c])}return l},Li.apply(this,arguments)}function nm(l,r){var u=Object.keys(l);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(l);r&&(c=c.filter(function(f){return Object.getOwnPropertyDescriptor(l,f).enumerable})),u.push.apply(u,c)}return u}function Hi(l){for(var r=1;r<arguments.length;r++){var u=arguments[r]!=null?arguments[r]:{};r%2?nm(Object(u),!0).forEach(function(c){ry(l,c,u[c])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(u)):nm(Object(u)).forEach(function(c){Object.defineProperty(l,c,Object.getOwnPropertyDescriptor(u,c))})}return l}function ry(l,r,u){return r=cy(r),r in l?Object.defineProperty(l,r,{value:u,enumerable:!0,configurable:!0,writable:!0}):l[r]=u,l}function cy(l){var r=oy(l,"string");return typeof r=="symbol"?r:r+""}function oy(l,r){if(typeof l!="object"||!l)return l;var u=l[Symbol.toPrimitive];if(u!==void 0){var c=u.call(l,r);if(typeof c!="object")return c;throw new TypeError("@@toPrimitive must return a primitive value.")}return(r==="string"?String:Number)(l)}function km(l){return l&&l.map((r,u)=>me.createElement(r.tag,Hi({key:u},r.attr),km(r.child)))}function nt(l){return r=>me.createElement(uy,Li({attr:Hi({},l.attr)},r),km(l.child))}function uy(l){var r=u=>{var{attr:c,size:f,title:d}=l,m=sy(l,ly),x=f||u.size||"1em",y;return u.className&&(y=u.className),l.className&&(y=(y?y+" ":"")+l.className),me.createElement("svg",Li({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},u.attr,c,m,{className:y,style:Hi(Hi({color:l.color||u.color},u.style),l.style),height:x,width:x,xmlns:"http://www.w3.org/2000/svg"}),d&&me.createElement("title",null,d),l.children)};return am!==void 0?me.createElement(am.Consumer,null,u=>r(u)):r(Hm)}function fy(l){return nt({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M257.5 445.1l-22.2 22.2c-9.4 9.4-24.6 9.4-33.9 0L7 273c-9.4-9.4-9.4-24.6 0-33.9L201.4 44.7c9.4-9.4 24.6-9.4 33.9 0l22.2 22.2c9.5 9.5 9.3 25-.4 34.3L136.6 216H424c13.3 0 24 10.7 24 24v32c0 13.3-10.7 24-24 24H136.6l120.5 114.8c9.8 9.3 10 24.8.4 34.3z"},child:[]}]})(l)}function lm(l){return nt({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M509.5 184.6L458.9 32.8C452.4 13.2 434.1 0 413.4 0H272v192h238.7c-.4-2.5-.4-5-1.2-7.4zM240 0H98.6c-20.7 0-39 13.2-45.5 32.8L2.5 184.6c-.8 2.4-.8 4.9-1.2 7.4H240V0zM0 224v240c0 26.5 21.5 48 48 48h416c26.5 0 48-21.5 48-48V224H0z"},child:[]}]})(l)}function dy(l){return nt({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M512 144v288c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V144c0-26.5 21.5-48 48-48h88l12.3-32.9c7-18.7 24.9-31.1 44.9-31.1h125.5c20 0 37.9 12.4 44.9 31.1L376 96h88c26.5 0 48 21.5 48 48zM376 288c0-66.2-53.8-120-120-120s-120 53.8-120 120 53.8 120 120 120 120-53.8 120-120zm-32 0c0 48.5-39.5 88-88 88s-88-39.5-88-88 39.5-88 88-88 88 39.5 88 88z"},child:[]}]})(l)}function hy(l){return nt({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M504 256c0 136.967-111.033 248-248 248S8 392.967 8 256 119.033 8 256 8s248 111.033 248 248zM227.314 387.314l184-184c6.248-6.248 6.248-16.379 0-22.627l-22.627-22.627c-6.248-6.249-16.379-6.249-22.628 0L216 308.118l-70.059-70.059c-6.248-6.248-16.379-6.248-22.628 0l-22.627 22.627c-6.248 6.248-6.248 16.379 0 22.627l104 104c6.249 6.249 16.379 6.249 22.628.001z"},child:[]}]})(l)}function sm(l){return nt({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256,8C119,8,8,119,8,256S119,504,256,504,504,393,504,256,393,8,256,8Zm92.49,313h0l-20,25a16,16,0,0,1-22.49,2.5h0l-67-49.72a40,40,0,0,1-15-31.23V112a16,16,0,0,1,16-16h32a16,16,0,0,1,16,16V256l58,42.5A16,16,0,0,1,348.49,321Z"},child:[]}]})(l)}function my(l){return nt({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M0 432c0 26.5 21.5 48 48 48h480c26.5 0 48-21.5 48-48V256H0v176zm192-68c0-6.6 5.4-12 12-12h136c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H204c-6.6 0-12-5.4-12-12v-40zm-128 0c0-6.6 5.4-12 12-12h72c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12v-40zM576 80v48H0V80c0-26.5 21.5-48 48-48h480c26.5 0 48 21.5 48 48z"},child:[]}]})(l)}function im(l){return nt({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M504 255.531c.253 136.64-111.18 248.372-247.82 248.468-59.015.042-113.223-20.53-155.822-54.911-11.077-8.94-11.905-25.541-1.839-35.607l11.267-11.267c8.609-8.609 22.353-9.551 31.891-1.984C173.062 425.135 212.781 440 256 440c101.705 0 184-82.311 184-184 0-101.705-82.311-184-184-184-48.814 0-93.149 18.969-126.068 49.932l50.754 50.754c10.08 10.08 2.941 27.314-11.313 27.314H24c-8.837 0-16-7.163-16-16V38.627c0-14.254 17.234-21.393 27.314-11.314l49.372 49.372C129.209 34.136 189.552 8 256 8c136.81 0 247.747 110.78 248 247.531zm-180.912 78.784l9.823-12.63c8.138-10.463 6.253-25.542-4.21-33.679L288 256.349V152c0-13.255-10.745-24-24-24h-16c-13.255 0-24 10.745-24 24v135.651l65.409 50.874c10.463 8.137 25.541 6.253 33.679-4.21z"},child:[]}]})(l)}function gy(l){return nt({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M400 224h-24v-72C376 68.2 307.8 0 224 0S72 68.2 72 152v72H48c-26.5 0-48 21.5-48 48v192c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V272c0-26.5-21.5-48-48-48zm-104 0H152v-72c0-39.7 32.3-72 72-72s72 32.3 72 72v72z"},child:[]}]})(l)}function qm(l){return nt({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M172.268 501.67C26.97 291.031 0 269.413 0 192 0 85.961 85.961 0 192 0s192 85.961 192 192c0 77.413-26.97 99.031-172.268 309.67-9.535 13.774-29.93 13.773-39.464 0zM192 272c44.183 0 80-35.817 80-80s-35.817-80-80-80-80 35.817-80 80 35.817 80 80 80z"},child:[]}]})(l)}function wo(l){return nt({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"},child:[]}]})(l)}function Gm(l){return nt({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M528.12 301.319l47.273-208C578.806 78.301 567.391 64 551.99 64H159.208l-9.166-44.81C147.758 8.021 137.93 0 126.529 0H24C10.745 0 0 10.745 0 24v16c0 13.255 10.745 24 24 24h69.883l70.248 343.435C147.325 417.1 136 435.222 136 456c0 30.928 25.072 56 56 56s56-25.072 56-56c0-15.674-6.447-29.835-16.824-40h209.647C430.447 426.165 424 440.326 424 456c0 30.928 25.072 56 56 56s56-25.072 56-56c0-22.172-12.888-41.332-31.579-50.405l5.517-24.276c3.413-15.018-8.002-29.319-23.403-29.319H218.117l-6.545-32h293.145c11.206 0 20.92-7.754 23.403-18.681z"},child:[]}]})(l)}function py(l){return nt({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm121.6 313.1c4.7 4.7 4.7 12.3 0 17L338 377.6c-4.7 4.7-12.3 4.7-17 0L256 312l-65.1 65.6c-4.7 4.7-12.3 4.7-17 0L134.4 338c-4.7-4.7-4.7-12.3 0-17l65.6-65-65.6-65.1c-4.7-4.7-4.7-12.3 0-17l39.6-39.6c4.7-4.7 12.3-4.7 17 0l65 65.7 65.1-65.6c4.7-4.7 12.3-4.7 17 0l39.6 39.6c4.7 4.7 4.7 12.3 0 17L312 256l65.6 65.1z"},child:[]}]})(l)}function xy(l){return nt({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M624 352h-16V243.9c0-12.7-5.1-24.9-14.1-33.9L494 110.1c-9-9-21.2-14.1-33.9-14.1H416V48c0-26.5-21.5-48-48-48H48C21.5 0 0 21.5 0 48v320c0 26.5 21.5 48 48 48h16c0 53 43 96 96 96s96-43 96-96h128c0 53 43 96 96 96s96-43 96-96h48c8.8 0 16-7.2 16-16v-32c0-8.8-7.2-16-16-16zM160 464c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48zm320 0c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48zm80-208H416V144h44.1l99.9 99.9V256z"},child:[]}]})(l)}function Go(l){return nt({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M224 256c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-74.2-60.2-134.4-134.4-134.4z"},child:[]}]})(l)}function Ym(l){return nt({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M461.2 128H80c-8.84 0-16-7.16-16-16s7.16-16 16-16h384c8.84 0 16-7.16 16-16 0-26.51-21.49-48-48-48H64C28.65 32 0 60.65 0 96v320c0 35.35 28.65 64 64 64h397.2c28.02 0 50.8-21.53 50.8-48V176c0-26.47-22.78-48-50.8-48zM416 336c-17.67 0-32-14.33-32-32s14.33-32 32-32 32 14.33 32 32-14.33 32-32 32z"},child:[]}]})(l)}const yy=[{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/phonne-24x24.png",className:"w-5 h-5"}),label:"Điện thoại"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/laptop-24x24.png",className:"w-5 h-5"}),label:"Laptop"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/phu-kien-24x24.png",className:"w-5 h-5"}),label:"Phụ kiện"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/smartwatch-24x24.png",className:"w-5 h-5"}),label:"Smartwatch"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/watch-24x24.png",className:"w-5 h-5"}),label:"Đồng Hồ"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/tablet-24x24.png",className:"w-5 h-5"}),label:"Tablet"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/may-cu-24x24.png",className:"w-5 h-5"}),label:"Mua máy thu cũ"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/PC-24x24.png",className:"w-5 h-5"}),label:"Màn hình, Máy in"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/sim-24x24.png",className:"w-5 h-5"}),label:"Sim, Thẻ cào"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/tien-ich-24x24.png",className:"w-5 h-5"}),label:"Dịch vụ tiện ích"}];function Yo(){const l=Jt();return s.jsxs("header",{className:"w-full bg-[#ffd400]",children:[s.jsxs("div",{className:"w-full max-w-[1280px] mx-auto flex items-center px-4 py-2",children:[s.jsxs("div",{className:"flex items-center w-[600px]",children:[s.jsx("img",{src:"./assets/logo.jpg",alt:"Logo",className:"h-10 object-contain cursor-pointer",onClick:()=>l("/")}),s.jsx("div",{className:"relative ml-2 flex-1",children:s.jsxs("div",{className:"flex items-center bg-white rounded-full px-3 py-1",children:[s.jsx(wo,{className:"text-gray-500 text-sm"}),s.jsx("input",{type:"text",placeholder:"Bạn tìm gì...",className:"w-full px-2 py-1 text-sm outline-none bg-transparent"})]})})]}),s.jsxs("div",{className:"flex items-center gap-14 ml-8",children:[s.jsxs(ll,{to:"/login",className:"flex items-center gap-1 text-sm font-normal hover:underline",children:[s.jsx(Go,{}),"Đăng nhập"]}),s.jsx(ll,{to:"/signup",className:"text-sm font-normal hover:underline",children:"Đăng ký"}),s.jsxs("div",{className:"flex items-center gap-1 hover:underline cursor-pointer text-sm",children:[s.jsx(Gm,{}),"Giỏ hàng"]}),s.jsxs("div",{className:"flex items-center gap-1 bg-yellow-300 px-3 py-2 rounded-full cursor-pointer text-sm",children:[s.jsx(qm,{}),s.jsx("span",{className:"truncate max-w-[150px]",children:"Thế giới di động "})]})]})]}),s.jsx("div",{className:"w-full max-w-[1280px] mx-auto flex flex-wrap gap-7 px-4 py-3 text-sm font-normal",children:yy.map((r,u)=>s.jsxs("div",{className:"flex items-center gap-1 cursor-pointer hover:underline",children:[r.icon,s.jsx("span",{children:r.label})]},u))})]})}function jt(){return s.jsx("footer",{className:"bg-white",children:s.jsxs("div",{className:"mx-auto max-w-screen-xl px-4 py-16 sm:px-6 lg:px-8",children:[s.jsxs("div",{className:"lg:flex lg:items-start lg:gap-8",children:[s.jsx("div",{className:"text-teal-600"}),s.jsxs("div",{className:"mt-8 grid grid-cols-2 gap-8 lg:mt-0 lg:grid-cols-4 lg:gap-y-16",children:[s.jsxs("div",{className:"col-span-2 sm:col-span-1",children:[s.jsx("p",{className:"font-medium text-gray-900",children:"Tổng đài hỗ trợ"}),s.jsxs("ul",{className:"mt-6 space-y-4 text-sm",children:[s.jsx("li",{children:s.jsx("a",{href:"#",className:"text-gray-700 transition hover:opacity-75",children:" Gọi mua: 1900 232 460 (8:00 - 21:30) "})}),s.jsx("li",{children:s.jsx("a",{href:"#",className:"text-gray-700 transition hover:opacity-75",children:" Khiếu nại: 1800.1062 (8:00 - 21:30) "})}),s.jsx("li",{children:s.jsx("a",{href:"#",className:"text-gray-700 transition hover:opacity-75",children:" Bảo hành: 1900 232 464 (8:00 - 21:00)"})})]})]}),s.jsxs("div",{className:"col-span-2 sm:col-span-1",children:[s.jsx("p",{className:"font-medium text-gray-900",children:"Về công ty"}),s.jsxs("ul",{className:"mt-6 space-y-4 text-sm",children:[s.jsx("li",{children:s.jsx("a",{href:"https://mwg.vn/",className:"text-gray-700 transition hover:opacity-75",children:" Giới thiệu công ty (MWG.vn) "})}),s.jsx("li",{children:s.jsx("a",{href:"https://vieclam.thegioididong.com/",className:"text-gray-700 transition hover:opacity-75",children:" Tuyển dụng "})}),s.jsx("li",{children:s.jsx("a",{href:"https://www.thegioididong.com/lien-he",className:"text-gray-700 transition hover:opacity-75",children:" Gửi góp ý, khiếu nại "})}),s.jsx("li",{children:s.jsx("a",{href:"https://www.thegioididong.com/he-thong-sieu-thi-the-gioi-di-dong",className:"text-gray-700 transition hover:opacity-75",children:" Tìm siêu thị (2.965 shop) "})})]})]}),s.jsxs("div",{className:"col-span-2 sm:col-span-1",children:[s.jsx("p",{className:"font-medium text-gray-900",children:"Thông tin khác"}),s.jsxs("ul",{className:"mt-6 space-y-4 text-sm",children:[s.jsx("li",{children:s.jsx("a",{href:"#",className:"text-gray-700 transition hover:opacity-75",children:"Tích điểm Quà tặng VIP "})}),s.jsx("li",{children:s.jsx("a",{href:"#",className:"text-gray-700 transition hover:opacity-75",children:" Lịch sử mua hàng "})}),s.jsx("li",{children:s.jsx("a",{href:"#",className:"text-gray-700 transition hover:opacity-75",children:" Đăng ký bán hàng CTV chiết khấu cao "})}),s.jsx("li",{children:s.jsx("a",{href:"#",className:"text-gray-700 transition hover:opacity-75",children:" Tìm hiểu về mua trả chậm "})}),s.jsx("li",{children:s.jsx("a",{href:"#",className:"text-gray-700 transition hover:opacity-75",children:" Chính sách bảo hành "})}),s.jsx("li",{children:s.jsx("a",{href:"#",className:"text-gray-700 transition hover:opacity-75",children:" Xem thêm "})})]})]}),s.jsxs("div",{className:"col-span-2 sm:col-span-1",children:[s.jsx("p",{className:"font-medium text-gray-900",children:"Website cùng tập đoàn"}),s.jsx("img",{src:"./assets/logotgdd.jpg",alt:"",style:{width:"460px",height:"auto"}})]})]})]}),s.jsx("div",{className:"mt-8 border-t border-gray-100 pt-8",children:s.jsx("div",{className:"sm:flex sm:justify-between",children:s.jsx("p",{className:"text-xs text-gray-500",children:"© 2018. Công ty cổ phần Thế Giới Di Động. GPDKKD: 0303217354 do sở KH & ĐT TP.HCM cấp ngày 02/01/2007. GPMXH: 238/GP-BTTTT do Bộ Thông Tin và Truyền Thông cấp ngày 04/06/2020. Địa chỉ: 128 Trần Quang Khải, P.Tân Định, Q.1, TP.Hồ Chí Minh. Địa chỉ liên hệ và gửi chứng từ: Lô T2-1.2, Đường D1, Đ. D1, P.Tân Phú, TP.Thủ Đức, TP.Hồ Chí Minh. Điện thoại: 028 38125960. Email: <EMAIL>. Chịu trách nhiệm nội dung: Huỳnh Văn Tốt. Email: <EMAIL>. Xem chính sách sử dụng"})})})]})})}var po,rm;function vy(){if(rm)return po;rm=1;var l=function(D){return r(D)&&!u(D)};function r(R){return!!R&&typeof R=="object"}function u(R){var D=Object.prototype.toString.call(R);return D==="[object RegExp]"||D==="[object Date]"||d(R)}var c=typeof Symbol=="function"&&Symbol.for,f=c?Symbol.for("react.element"):60103;function d(R){return R.$$typeof===f}function m(R){return Array.isArray(R)?[]:{}}function x(R,D){return D.clone!==!1&&D.isMergeableObject(R)?E(m(R),R,D):R}function y(R,D,w){return R.concat(D).map(function(q){return x(q,w)})}function p(R,D){if(!D.customMerge)return E;var w=D.customMerge(R);return typeof w=="function"?w:E}function v(R){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(R).filter(function(D){return Object.propertyIsEnumerable.call(R,D)}):[]}function b(R){return Object.keys(R).concat(v(R))}function O(R,D){try{return D in R}catch{return!1}}function H(R,D){return O(R,D)&&!(Object.hasOwnProperty.call(R,D)&&Object.propertyIsEnumerable.call(R,D))}function U(R,D,w){var q={};return w.isMergeableObject(R)&&b(R).forEach(function(C){q[C]=x(R[C],w)}),b(D).forEach(function(C){H(R,C)||(O(R,C)&&w.isMergeableObject(D[C])?q[C]=p(C,w)(R[C],D[C],w):q[C]=x(D[C],w))}),q}function E(R,D,w){w=w||{},w.arrayMerge=w.arrayMerge||y,w.isMergeableObject=w.isMergeableObject||l,w.cloneUnlessOtherwiseSpecified=x;var q=Array.isArray(D),C=Array.isArray(R),W=q===C;return W?q?w.arrayMerge(R,D,w):U(R,D,w):x(D,w)}E.all=function(D,w){if(!Array.isArray(D))throw new Error("first argument should be an array");return D.reduce(function(q,C){return E(q,C,w)},{})};var M=E;return po=M,po}vy();function Xm(l){var r,u,c="";if(typeof l=="string"||typeof l=="number")c+=l;else if(typeof l=="object")if(Array.isArray(l))for(r=0;r<l.length;r++)l[r]&&(u=Xm(l[r]))&&(c&&(c+=" "),c+=u);else for(r in l)l[r]&&(c&&(c+=" "),c+=r);return c}function gs(){for(var l,r,u=0,c="";u<arguments.length;)(l=arguments[u++])&&(r=Xm(l))&&(c&&(c+=" "),c+=r);return c}const by=(l,r,u=150)=>{const[c,f]=N.useState(0),d=N.useRef(null),m=N.useRef(null),x=N.useRef(!1),y=b=>{if(b!==null&&!x.current){x.current=!0;const{transitionDuration:H}=window.getComputedStyle(b),U=Number(H.replace("s",""))*1e3;f(U);return}if(!r)return;const O=r==null?void 0:r.split(" ");if(r!=null&&r.includes("duration")){const H=O==null?void 0:O.find(E=>E.includes("duration")),U=Number(H==null?void 0:H.split("-")[1].replace(/\D/g,""));f(U);return}else r!=null&&r.includes("transition")&&f(u)};N.useEffect(()=>{x.current||y(l)},[l]);const p=b=>{d.current!==null&&clearTimeout(d.current),d.current=setTimeout(()=>{b==null||b()},50)},v=b=>{x.current!==!1&&(m.current!==null&&clearTimeout(m.current),m.current=setTimeout(()=>{b==null||b()},c))};return N.useEffect(()=>()=>{m.current!==null&&clearTimeout(m.current)},[]),{transitionDuration:c,onTransitionShow:p,onTransitionHide:v,getTransitionTime:y}},Vm=N.createContext({isOpenModal:!1,setTransitionDuration:null,scrollable:!1}),jy={wrapper:"min-[576px]:shadow-[0_0.5rem_1rem_rgba(#000, 0.15)] pointer-events-auto relative flex w-full flex-col rounded-md border-none bg-white bg-clip-padding text-current shadow-lg outline-none dark:bg-neutral-600",scrollable:"max-h-full"};me.forwardRef(({className:l,children:r,theme:u,tag:c="div",...f},d)=>{const m={...jy,...u},{scrollable:x}=N.useContext(Vm),y=gs(m.wrapper,x&&m.scrollable,l);return s.jsx(c,{className:y,...f,ref:d,children:r})});const Ny={wrapper:"flex flex-shrink-0 items-center justify-between rounded-t-md border-b-2 border-neutral-100 border-opacity-100 p-4 dark:border-opacity-50"};me.forwardRef(({className:l,children:r,theme:u,tag:c="div",...f},d)=>{const m={...Ny,...u},x=gs(m.wrapper,l);return s.jsx(c,{className:x,...f,ref:d,children:r})});const Sy={scrollable:"overflow-y-auto",wrapper:"relative flex-auto p-4"};me.forwardRef(({className:l,children:r,theme:u,tag:c="div",...f},d)=>{const m={...Sy,...u},{scrollable:x}=N.useContext(Vm),y=gs(m.wrapper,x&&m.scrollable,l);return s.jsx(c,{className:y,...f,ref:d,children:r})});const wy={wrapper:"flex flex-shrink-0 flex-wrap items-center justify-end rounded-b-md border-t-2 border-neutral-100 border-opacity-100 p-4 dark:border-opacity-50"};me.forwardRef(({className:l,children:r,theme:u,tag:c="div",...f},d)=>{const m={...wy,...u},x=gs(m.wrapper,l);return s.jsx(c,{className:x,...f,ref:d,children:r})});me.createContext({});me.createContext({});N.createContext({activeIndex:-1,animation:!0,isOpenState:!1,setIsOpenState:()=>{},setReferenceElement:()=>{},setPopperElement:()=>{},setActiveIndex:()=>{},popperElement:null,referenceElement:null,autoClose:!0,onHide:()=>{},onHidden:()=>{},onShow:()=>{},onShown:()=>{}});const Ty=l=>l.offsetHeight,Ey=l=>{if(!l||l.getClientRects().length===0)return!1;if(l instanceof HTMLElement&&l.style&&l.parentNode&&l.parentNode instanceof HTMLElement&&l.parentNode.style){const r=getComputedStyle(l),u=getComputedStyle(l.parentNode);return getComputedStyle(l).getPropertyValue("visibility")==="visible"||r.display!=="none"&&u.display!=="none"&&r.visibility!=="hidden"}return!1},Qm=N.createContext({activeSlide:0,setTransitionDuration:()=>{},block:"",visible:"",crossfade:void 0,setCarouselItems:()=>{},isFirstRender:{current:!0}}),_y={carouselWrapper:"relative",pointer:"touch-pan-y",block:"!block",visible:"opacity-100 !z-10",invisible:"opacity-0 z-0",slideRight:"translate-x-full",slideLeft:"-translate-x-full",nextBtn:"absolute bottom-0 right-0 top-0 z-30 flex w-[15%] items-center justify-center border-0 bg-none p-0 text-center text-white opacity-50 transition-opacity duration-150 ease-[cubic-bezier(0.25,0.1,0.25,1.0)] hover:text-white hover:no-underline hover:opacity-90 hover:outline-none focus:text-white focus:no-underline focus:opacity-90 focus:outline-none motion-reduce:transition-none",nextBtnIcon:"inline-block h-8 w-8 [&>svg]:h-8",prevBtn:"absolute bottom-0 left-0 top-0 z-30 flex w-[15%] items-center justify-center border-0 bg-none p-0 text-center text-white opacity-50 transition-opacity duration-150 ease-[cubic-bezier(0.25,0.1,0.25,1.0)] hover:text-white hover:no-underline hover:opacity-90 hover:outline-none focus:text-white focus:no-underline focus:opacity-90 focus:outline-none motion-reduce:transition-none",prevBtnIcon:"inline-block h-8 w-8 [&>svg]:h-8",indicatorsWrapper:"absolute bottom-0 left-0 right-0 z-30 mx-[15%] mb-4 flex list-none justify-center p-0",indicator:"mx-[3px] box-content h-[3px] w-[30px] flex-initial cursor-pointer border-0 border-y-[10px] border-solid border-transparent bg-white bg-clip-padding p-0 -indent-[999px] opacity-50 transition-opacity duration-[600ms] ease-[cubic-bezier(0.25,0.1,0.25,1.0)] motion-reduce:transition-none focus:outline-none",activeIndicator:"!opacity-100"},Oy=({tag:l="div",className:r,children:u,interval:c=5e3,ride:f=!1,keyboard:d=!0,pause:m="hover",wrap:x=!0,touch:y=!0,stopSliding:p,showControls:v,showIndicators:b,prevBtnIcon:O,nextBtnIcon:H,current:U,crossfade:E,onSlide:M,onSlid:R,theme:D,...w})=>{const[q,C]=N.useState(0),[W,$]=N.useState(0),[K,oe]=N.useState(document.visibilityState),[Ce,be]=N.useState({initialX:0,initialY:0}),[ie,rt]=N.useState([]),Ze=N.useRef(null),Ue=N.useRef(0),G=N.useRef(!0),J=N.useRef(!1),ee=N.useRef(!0),je=N.useRef(!1),S=N.useRef(!1),Q=N.useRef(null),F=N.useRef(null),Z={..._y,...D},ae=gs(Z.carouselWrapper,y&&Z.pointer,r),xe=(ne,Ee)=>{if(Ee!==void 0)return ie[Ee];const _e=ne==="prev",We=Ue.current,Pt=_e?-1:1;let ht=We;if(x)ht=(We+Pt)%ie.length;else{if(ht===ie.length-1&&ne==="next"||ht===0&&ne==="prev")return;ht+=Pt}return ht===-1?ie[ie.length-1]:ie[ht]},re=(ne,Ee)=>{if(Ee!==void 0){Ue.current=Ee,C(Ee);return}const _e=q===ie.length-1?0:q+1,We=q===0?ie.length-1:q-1;Ue.current=ne==="next"?_e:We,C(ne==="next"?_e:We)},$e=N.useCallback((ne,Ee,_e)=>{if(!ie||ie.length<2||J.current)return;ee.current&&(ee.current=!1);const We=ie[q];if(!We||!Ee)return;J.current=!0,M==null||M();const Pt=ne==="next",ht=Pt?Z.slideLeft:Z.slideRight,la=Pt?Z.slideRight:Z.slideLeft;re(ne,_e),Ee.classList.add(la,Z.block),Ty(Ee),We.classList.add(ht),E&&(We.classList.add(...Z.invisible.split(" ")),We.classList.remove(...Z.visible.split(" ")),Ee.classList.add(...Z.visible.split(" "))),Ee.classList.remove(la),F.current!==null&&clearTimeout(F.current),F.current=setTimeout(()=>{J.current=!1,R==null||R(),We.classList.remove(ht,Z.block)},W)},[q,W,E]),ge=N.useCallback(ne=>{const Ee=xe(ne),{hidden:_e}=document;if(K&&(_e||!Ey(Ze.current))){je.current=!0;return}Ee&&$e(ne,Ee)},[$e,K]),St=ne=>{const Ee=Ue.current,_e=ne>Ee?"next":"prev",We=xe(_e,ne);!We||ne>ie.length-1||ne<0||ne===q||$e(_e,We,ne)},na=N.useCallback(()=>{typeof c=="number"&&c>0&&(S.current&&(S.current=!1),Q.current=setTimeout(()=>{ge("next")},c))},[ge,c]),P=()=>{Q.current&&(clearTimeout(Q.current),Q.current=null)},Ne=N.useCallback(ne=>{be({initialX:ne.touches[0].clientX,initialY:ne.touches[0].clientY})},[]),He=N.useCallback(ne=>{if(!Ce)return;const{initialX:Ee,initialY:_e}=Ce,We=ne.touches[0].clientX,Pt=ne.touches[0].clientY,ht=Ee-We,la=_e-Pt;Math.abs(ht)>Math.abs(la)&&(ht>0?ge("next"):ge("prev")),be({initialX:0,initialY:0})},[ge,Ce]),dt=N.useCallback(ne=>{switch(ne.key){case"ArrowLeft":ne.preventDefault(),ge("prev");break;case"ArrowRight":ne.preventDefault(),ge("next");break}},[ge]);return N.useEffect(()=>{const ne=()=>{oe(document.visibilityState)};return document.addEventListener("visibilitychange",ne),()=>{document.removeEventListener("visibilitychange",ne)}},[c,f]),N.useEffect(()=>{if(!d||!Ze.current)return;const ne=Ze.current;return ne.addEventListener("keydown",dt),()=>{ne.removeEventListener("keydown",dt)}},[d,dt]),N.useEffect(()=>{if(!y||!Ze.current)return;const ne=Ze.current;return ne.addEventListener("touchmove",He),ne.addEventListener("touchstart",Ne),()=>{ne.removeEventListener("touchmove",He),ne.removeEventListener("touchstart",Ne)}},[y,He,Ne]),N.useEffect(()=>{if(!f||f===!0&&ee.current||!m||!Ze.current)return;const ne=()=>{P(),S.current=!0},Ee=()=>{p||na()},_e=Ze.current;return _e.addEventListener("mouseenter",ne),_e.addEventListener("mouseleave",Ee),_e.addEventListener("touchend",P),()=>{_e.removeEventListener("mouseenter",ne),_e.removeEventListener("mouseleave",Ee),_e.removeEventListener("touchend",P)}},[m,f,na]),N.useEffect(()=>{if(G.current){G.current=!1;return}if(!(!ie||ie.length<2)&&!(p||f===!0&&ee.current||!f||K==="hidden"||!c||S.current)){if(je.current&&K==="visible"){ge("next"),je.current=!1;return}return na(),()=>{P()}}},[q,c,f,W,K,p]),N.useEffect(()=>{G.current||(p&&!ee.current&&P(),U!==void 0&&St(U))},[p,U]),s.jsx(Qm.Provider,{value:{activeSlide:q,setTransitionDuration:$,block:Z.block,visible:Z.visible,crossfade:E,setCarouselItems:rt,isFirstRender:G},children:s.jsxs(l,{ref:Ze,className:ae,current:q,...w,children:[u,v&&s.jsxs(s.Fragment,{children:[s.jsxs("button",{onClick:()=>ge("prev"),className:Z.prevBtn,type:"button",children:[s.jsx("span",{className:Z.prevBtnIcon,children:O||s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 19.5L8.25 12l7.5-7.5"})})}),s.jsx("span",{className:"!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]",children:"Previous"})]}),s.jsxs("button",{onClick:()=>ge("next"),className:Z.nextBtn,type:"button",children:[s.jsx("span",{className:Z.nextBtnIcon,children:H||s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 4.5l7.5 7.5-7.5 7.5"})})}),s.jsx("span",{className:"!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]",children:"Next"})]})]}),b&&s.jsx("div",{className:Z.indicatorsWrapper,children:ie.map((ne,Ee)=>s.jsx("button",{"data-te-target":Ee,onClick:()=>St(Ee),className:`${Z.indicator} ${Ee===q?`${Z.activeIndicator}`:""}`},Ee))})]})})},xo=({tag:l="div",className:r,itemID:u,children:c,...f})=>{const d=N.useRef(null),{activeSlide:m,setTransitionDuration:x,block:y,visible:p,crossfade:v,setCarouselItems:b,isFirstRender:O}=N.useContext(Qm),{transitionDuration:H}=by(d.current,r);return N.useEffect(()=>{if(O!=null&&O.current){b(U=>[...U,d.current]);return}},[]),N.useEffect(()=>{if(m===u-1){const U=d.current;U.classList.add(y),v&&U.classList.add(...p.split(" ")),x(H)}},[m,u,v,H]),s.jsx(l,{ref:d,className:r,...f,children:c})};function cl(){return s.jsx("div",{className:"w-full px-10 py-6",children:s.jsx("div",{className:"flex items-start justify-between gap-4",children:s.jsx("div",{className:"w-[85%] mx-auto",children:s.jsx(Oy,{ride:"carousel",showIndicators:!0,showControls:!0,children:s.jsxs("div",{className:"relative w-full h-64 overflow-hidden after:clear-both after:block after:content-['']",children:[s.jsx(xo,{itemID:1,className:"relative float-left -mr-[100%] hidden w-full h-full transition-transform duration-[600ms] ease-in-out motion-reduce:transition-none",children:s.jsx("img",{src:"https://cdnv2.tgdd.vn/mwg-static/tgdd/Banner/23/05/23050828d3211ce7b91e92473a3690b3.jpg",className:"w-full h-full object-cover border border-gray-300 rounded-lg",alt:"Slide 1"})}),s.jsx(xo,{itemID:2,className:"relative float-left -mr-[100%] hidden w-full h-full transition-transform duration-[600ms] ease-in-out motion-reduce:transition-none",children:s.jsx("img",{src:"https://cdnv2.tgdd.vn/mwg-static/tgdd/Banner/43/85/43854a7ba231f17252741049cc5a099a.png",className:"w-full h-full object-cover border border-gray-300 rounded-lg",alt:"Slide 2"})}),s.jsx(xo,{itemID:2,className:"relative float-left -mr-[100%] hidden w-full h-full transition-transform duration-[600ms] ease-in-out motion-reduce:transition-none",children:s.jsx("img",{src:"https://cdnv2.tgdd.vn/mwg-static/tgdd/Banner/91/1b/911bdb7d43d18d76d89279f143d90f2c.png",className:"w-full h-full object-cover border border-gray-300 rounded-lg",alt:"Slide 2"})})]})})})})})}function Cy(){const l=[{name:"Samsung Galaxy S25",price:"27.690.000₫",originalPrice:"29.990.000₫",discount:"7%",img:"https://cdn.tgdd.vn/Products/Images/42/335925/samsung-galaxy-s25-edge-blue-thumb-600x600.jpg",specs:"Quad HD+ (2K+)",screenSize:'6.7"',sold:"62",rating:5},{name:"Xiaomi Redmi 13x",price:"4.290.000₫",originalPrice:"",discount:"",img:"https://cdn.tgdd.vn/Products/Images/42/332938/xiaomi-redmi-note-14-pro-thumbnew-600x600.jpg",specs:"Full HD+",screenSize:'6.79"',sold:"31.3k",rating:4.9},{name:"MacBook Air 13 inch M2 16GB/256GB",price:"9.990.000₫",originalPrice:"",discount:"",img:"https://cdn.tgdd.vn/Products/Images/44/325306/apple-macbook-air-m2-2022-xanh-den-600x600.jpg",specs:"Full HD+",screenSize:'6.67"',sold:"97.7k",rating:4.9},{name:"MacBook Air 13 inch M1 8GB/256GB",price:"8.990.000₫",originalPrice:"",discount:"",img:"https://cdn.tgdd.vn/Products/Images/42/329143/iphone-16-pro-titan-trang.png",specs:"Full HD+",screenSize:'6.77"',sold:"5k",rating:4.9},{name:"Samsung Galaxy A56 5G 12GB/256GB",price:"3.990.000₫",originalPrice:"",discount:"",img:"https://cdn.tgdd.vn/Products/Images/42/334930/samsung-galaxy-a36-5g-green-thumb-600x600.jpg",specs:"HD+",screenSize:'6.72"',sold:"2k",rating:5}],r=[{name:"Điện Thoại",active:!0},{name:"Apple",active:!1},{name:"Laptop",active:!1},{name:"Phụ Kiện",active:!1},{name:"Đồng Hồ",active:!1},{name:"PC, Máy In",active:!1}];return s.jsx("div",{className:"w-full bg-white mt-4",children:s.jsxs("div",{className:"max-w-7xl mx-auto px-4 py-3",children:[s.jsx("div",{className:"flex justify-between items-center mb-3",children:s.jsx("h2",{className:"text-lg font-bold text-black-800",children:"Khuyến mãi Online"})}),s.jsx("div",{className:"mb-4",children:s.jsxs("div",{className:"flex items-center",children:[s.jsxs("div",{className:"flex items-center gap-3 mr-8",children:[s.jsx("img",{src:"https://cdnv2.tgdd.vn/mwg-static/common/Campaign/10/0d/100d3018ffd23afe20324b164d0412cc.png",alt:"",className:"h-12 object-contain"}),s.jsx("div",{className:"bg-orange-10 text-white px-3 py-1 rounded-md font-bold",children:s.jsx("img",{src:"https://cdnv2.tgdd.vn/mwg-static/common/Campaign/d4/17/d4177404ab82e04867a0fd79bb903450.png",alt:"",className:"h-12 object-contain"})})]}),s.jsx("div",{className:"flex gap-x-20",children:r.map((u,c)=>s.jsx("button",{className:`px-4 py-2  text-sm font-medium ${u.active?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"}`,children:u.name},c))})]})}),s.jsx("div",{className:"max-w-7xl mx-auto px-2 py-2 mb-4",children:s.jsx("div",{className:"bg-gradient-to-r from-yellow-400 via-yellow-300 to-pink-200 rounded-md p-3 flex items-center",children:s.jsx("img",{src:"https://cdnv2.tgdd.vn/mwg-static/common/Campaign/c8/b7/c8b756baf5f990d065abf3acd1de19f6.png",alt:"",className:"h-8 object-contain"})})}),s.jsx("div",{className:"grid grid-cols-5 gap-4",children:l.map((u,c)=>s.jsx("div",{className:"bg-white rounded-md hover:shadow-md transition-shadow",children:s.jsxs("div",{className:"p-2 flex flex-col ",children:[s.jsx("img",{src:u.img,alt:u.name,className:"w-full h-32 object-contain mb-2"}),s.jsx("h3",{className:"font-medium text-sm ",children:u.name}),s.jsxs("div",{className:"text-xs text-gray-600 mt-1",children:[u.specs," ",u.screenSize]}),s.jsx("div",{className:"text-red-600 font-bold mt-1",children:u.price}),u.originalPrice&&s.jsx("div",{className:"text-gray-500 text-xs line-through",children:u.originalPrice}),s.jsxs("div",{className:"bg-white-100 text-xs p-1 rounded mt-2 w-full ",children:["Quà ",c===0?"3.000.000₫":c===1?"350.000₫":c===2?"600.000₫":c===3?"360.000₫":c===4?"250.000₫":"1.500.000₫"]}),s.jsxs("div",{className:"flex items-center text-xs mt-2",children:[s.jsx("span",{className:"text-yellow-500 mr-1",children:"★"}),s.jsx("span",{className:"mr-1",children:u.rating}),s.jsxs("span",{className:"text-gray-500",children:["• Đã bán ",u.sold]})]})]})},c))})]})})}function Ay(){const l=[{name:"iPhone 16 Pro Max 1TB",specs:"Quad HD+ (2K+)",price:"42.690.000₫",sold:14,image:"https://cdn.tgdd.vn/Products/Images/42/329151/iphone-16-pro-max-titan-trang-thumbtgdd-600x600.png",discount:10},{name:"iPhone 16 Pro Max 256GB",specs:"Quad HD+ (2K+)",price:"30.090.000₫",sold:58,image:"https://cdn.tgdd.vn/Products/Images/42/329149/iphone-16-pro-max-sa-mac-thumb-1-600x600.jpg",discount:17},{name:"iPhone 16 Pro 256GB",specs:"Quad HD+ (2K+)",price:"28.090.000₫",sold:"ĐANG BÁN CHẠY",image:"https://cdn.tgdd.vn/Products/Images/42/329144/iphone-16-pro-titan-trang.png",discount:46},{name:"iPhone 16 Plus 256GB",specs:"Quad HD+ (2K+)",price:"18.990.000₫",sold:"ĐANG BÁN CHẠY",image:"https://cdn.tgdd.vn/Products/Images/42/329138/iphone-16-plus-xanh-thumb-600x600.jpg",discount:20},{name:" iPhone 16 Plus 128GB",specs:"Quad HD+ (2K+)",price:"25.090.000₫",sold:"ĐANG BÁN CHẠY",image:"https://cdn.tgdd.vn/Products/Images/42/329135/iphone-16-black-600x600.png",discount:25},{name:"iPhone 16 Pro 256GB",specs:"Quad HD+ (2K+)",price:"33.890.000₫",sold:"ĐANG BÁN CHẠY",image:"https://cdn.tgdd.vn/Products/Images/42/329144/iphone-16-pro-titan-trang.png",discount:30}],r=[{name:"iPhone 15 128GB",specs:"Quad HD+ (2K+)",price:"15.390.000₫",sold:125,image:"https://cdn.tgdd.vn/Products/Images/42/281570/iphone-15-vang-thumb-600x600.jpg",discount:15},{name:"iPhone 13 128GB",specs:"Super Retina XDR 6.1",price:"11.690.000₫",sold:"ĐANG BÁN CHẠY",image:"https://cdn.tgdd.vn/Products/Images/42/223602/iphone-13-midnight-2-600x600.jpg",discount:12},{name:"Samsung Galaxy S24 Ultra 5G 12GB/256GB",specs:'Quad HD+ (2K+) 6.8"',price:"23.850.000₫",sold:89,image:"https://cdn.tgdd.vn/Products/Images/42/307174/samsung-galaxy-s24-ultra-5g-600x600.jpg",discount:8},{name:"Xiaomi 14T 5G 12GB/256GB",specs:'1.5K 6.67"',price:"11.260.000₫",sold:"ĐANG BÁN CHẠY",image:"https://cdn.tgdd.vn/Products/Images/42/329938/xiaomi-14t-black-thumb-600x600.jpg",discount:20},{name:"OPPO A3x 4GB/64GB",specs:"Always-On Retina",price:"3.260.000₫",sold:67,image:"https://cdn.tgdd.vn/Products/Images/42/328449/oppo-a3x-blue-thumb-600x600.jpg",discount:18},{name:"iPhone 16 256GB",specs:"Super Retina XDR",price:"22.290.000₫",sold:"ĐANG BÁN CHẠY",image:"https://cdn.tgdd.vn/Products/Images/42/329136/iphone-16-pink-600x600.png",discount:25}],u=N.useRef();return s.jsxs("div",{className:"bg-white mt-6 p-8 shadow rounded-md borderw-full mx-auto ml-40",children:[s.jsx("div",{className:"flex justify-between items-center mb-6",children:s.jsx("div",{className:"flex items-center gap-2 ml-4",children:s.jsx("h2",{className:"text-lg font-bold text-black-800",children:"Gợi ý cho bạn"})})}),s.jsx("div",{className:"relative px-6",children:s.jsx("div",{className:"flex gap-4 overflow-x-auto",ref:u,children:l.map((c,f)=>s.jsxs("div",{className:"w-[180px] h-auto bg-white border border-gray-200 rounded-md shadow-sm p-3 flex-shrink-0 relative flex flex-col",children:[s.jsx("img",{src:c.image,alt:c.name,className:"w-full h-32 object-contain mb-3"}),s.jsx("h3",{className:"text-sm font-medium mb-2 h-10 leading-tight break-words overflow-hidden",children:c.name}),c.specs&&s.jsx("div",{className:"text-xs text-gray-600 mb-2",children:c.specs}),s.jsxs("div",{className:"text-red-600 font-bold text-base mb-1",children:[" ",c.price]}),s.jsxs("div",{className:"text-gray-400 line-through text-sm mb-2",children:["₫ ",(parseInt(c.price.replace(/\./g,""))*(100+c.discount)/100).toLocaleString()]}),s.jsxs("div",{className:"text-orange-600 text-sm mb-2",children:["Quà ",Math.floor(Math.random()*500)+100,".000₫"]}),s.jsxs("div",{className:"flex items-center text-sm",children:[s.jsx("span",{className:"text-yellow-500",children:"★"}),s.jsxs("span",{className:"ml-1",children:["4.",Math.floor(Math.random()*9)+1]}),s.jsxs("span",{className:"text-gray-500 ml-2",children:["• Đã bán ",typeof c.sold=="number"?c.sold:Math.floor(Math.random()*50)+10,"k"]})]})]},f))})}),s.jsx("div",{className:"relative px-6 mt-4",children:s.jsx("div",{className:"flex gap-4 overflow-x-auto",children:r.map((c,f)=>s.jsxs("div",{className:"w-[180px] h-auto bg-white border border-gray-200 rounded-md shadow-sm p-3 flex-shrink-0 relative flex flex-col",children:[s.jsx("img",{src:c.image,alt:c.name,className:"w-full h-32 object-contain mb-3"}),s.jsx("h3",{className:"text-sm font-medium mb-2 h-10 leading-tight break-words overflow-hidden",children:c.name}),c.specs&&s.jsx("div",{className:"text-xs text-gray-600 mb-2",children:c.specs}),s.jsxs("div",{className:"text-red-600 font-bold text-base mb-1",children:[" ",c.price]}),s.jsxs("div",{className:"text-gray-400 line-through text-sm mb-2",children:["₫ ",(parseInt(c.price.replace(/\./g,""))*(100+c.discount)/100).toLocaleString()]}),s.jsxs("div",{className:"text-orange-600 text-sm mb-2",children:["Quà ",Math.floor(Math.random()*500)+100,".000₫"]}),s.jsxs("div",{className:"flex items-center text-sm",children:[s.jsx("span",{className:"text-yellow-500",children:"★"}),s.jsxs("span",{className:"ml-1",children:["4.",Math.floor(Math.random()*9)+1]}),s.jsxs("span",{className:"text-gray-500 ml-2",children:["• Đã bán ",typeof c.sold=="number"?c.sold:Math.floor(Math.random()*50)+10,"k"]})]})]},f))})})]})}function Ry(){const l=[{id:1,name:"Samsung Galaxy Z Flip7 FE 5G 8GB/128GB",originalPrice:"22.990.000₫",salePrice:null,image:"https://cdn.tgdd.vn/Products/Images/42/338741/samsung-galaxy-z-flip7-fe-white-thumb-600x600.jpg",installment:"Nhận ưu đãi đến 6 Triệu",specs:["Full HD+",'Chính 6.7" & Phụ 3.4"',"Hàng sắp về"],newLabel:"Mới"},{id:2,name:"OPPO Reno14 5G 12GB/512GB",originalPrice:"16.690.000₫",salePrice:null,image:"https://cdn.tgdd.vn/Products/Images/42/339174/oppo-reno14-5g-green-thumb-600x600.jpg",installment:"Quà 300.000₫",specs:["1.5K",'6.59"'],rating:5,reviews:"Đã bán 2.8k",newLabel:"Mới",saleLabel:"Trả chậm 0% trả trước 0₫"},{id:3,name:"Asus Vivobook Go 15 E1504FA R5 7520U",originalPrice:"14.190.000₫",salePrice:"12.090.000₫",image:"https://cdnv2.tgdd.vn/mwg-static/tgdd/Products/Images/44/311178/asus-vivobook-go-15-e1504fa-r5-nj776w-140225-100949-251-600x600.jpg",installment:"Quà 2.190.000₫",specs:["RAM 16 GB","SSD 512 GB"],rating:4.9,reviews:"Đã bán 21k",saleLabel:"Trả chậm 0% trả trước 0₫"},{id:4,name:"HP 15 fd0234TU i5 1334U (9Q969PA)",originalPrice:"19.390.000₫",salePrice:"15.490.000₫",image:"https://cdnv2.tgdd.vn/mwg-static/tgdd/Products/Images/44/323920/hp-15-fd0234tu-i5-9q969pa-170225-105831-192-600x600.jpg",installment:"Quà 2.190.000₫",specs:["RAM 16 GB","SSD 512 GB"],rating:4.9,reviews:"Đã bán 9.5k",saleLabel:"Trả chậm 0% trả trước 0₫"}];return s.jsx("div",{className:"max-w-7xl mx-auto px-4 py-6",children:s.jsxs("div",{className:"bg-white p-4 shadow rounded-md border border-gray-200",children:[s.jsx("div",{className:"flex justify-between items-center mb-4",children:s.jsx("div",{className:"text-lg font-bold text-black-800",children:s.jsx("span",{className:"text-base",children:"Sản Phẩm Đặc Quyền"})})}),s.jsxs("div",{className:"flex gap-4",children:[s.jsx("div",{className:"w-1/4 pr-4",children:s.jsx("img",{src:"https://cdnv2.tgdd.vn/mwg-static/tgdd/Banner/e8/e1/e8e182cf81dff9d70fc9017070c848c5.png",alt:"Mall Banner",className:"w-full h-full object-cover rounded"})}),s.jsx("div",{className:"w-2/3 grid grid-cols-4 gap-3",children:l.map(r=>s.jsxs("div",{className:"border border-gray-200 rounded-lg p-2 hover:shadow-md transition-shadow bg-white h-full",children:[r.newLabel&&s.jsx("div",{className:"text-xs text-red-500 mb-1",children:r.newLabel}),r.saleLabel&&s.jsx("div",{className:"text-xs text-gray-500 mb-1",children:r.saleLabel}),s.jsx("div",{className:"relative mb-2",children:s.jsx("img",{src:r.image,alt:r.name,className:"w-full h-32 object-contain"})}),s.jsx("h3",{className:"text-sm font-medium mb-1 line-clamp-2",children:r.name}),s.jsx("div",{className:"text-xs text-gray-600 mb-1",children:r.specs&&r.specs.map((u,c)=>s.jsx("div",{children:u},c))}),s.jsx("div",{className:"mb-1",children:r.salePrice?s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"text-red-600 font-bold text-sm",children:r.salePrice}),s.jsx("div",{className:"text-gray-400 line-through text-xs",children:r.originalPrice})]}):s.jsx("div",{className:"text-red-600 font-bold text-sm",children:r.originalPrice})}),s.jsx("div",{className:"text-xs text-orange-600 mb-1 truncate",children:r.installment}),r.rating&&s.jsxs("div",{className:"flex items-center text-xs",children:[s.jsx("span",{className:"text-yellow-500",children:"★"}),s.jsx("span",{className:"ml-1",children:r.rating}),s.jsx("span",{className:"text-gray-500 ml-2",children:r.reviews})]})]},r.id))})]})]})})}function My(l){return nt({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M405 136.798L375.202 107 256 226.202 136.798 107 107 136.798 226.202 256 107 375.202 136.798 405 256 285.798 375.202 405 405 375.202 285.798 256z"},child:[]}]})(l)}function zy(l){return nt({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 464c22.779 0 41.411-18.719 41.411-41.6h-82.823c0 22.881 18.633 41.6 41.412 41.6zm134.589-124.8V224.8c0-63.44-44.516-117.518-103.53-131.041V79.2c0-17.682-13.457-31.2-31.059-31.2s-31.059 13.518-31.059 31.2v14.559c-59.015 13.523-103.53 67.601-103.53 131.041v114.4L80 380.8v20.8h352v-20.8l-41.411-41.6z"},child:[]}]})(l)}function Zm(l,r){return function(){return l.apply(r,arguments)}}const{toString:Dy}=Object.prototype,{getPrototypeOf:Xo}=Object,Yi=(l=>r=>{const u=Dy.call(r);return l[u]||(l[u]=u.slice(8,-1).toLowerCase())})(Object.create(null)),$t=l=>(l=l.toLowerCase(),r=>Yi(r)===l),Xi=l=>r=>typeof r===l,{isArray:ol}=Array,hs=Xi("undefined");function Uy(l){return l!==null&&!hs(l)&&l.constructor!==null&&!hs(l.constructor)&&Dt(l.constructor.isBuffer)&&l.constructor.isBuffer(l)}const Km=$t("ArrayBuffer");function By(l){let r;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?r=ArrayBuffer.isView(l):r=l&&l.buffer&&Km(l.buffer),r}const Ly=Xi("string"),Dt=Xi("function"),Jm=Xi("number"),Vi=l=>l!==null&&typeof l=="object",Hy=l=>l===!0||l===!1,zi=l=>{if(Yi(l)!=="object")return!1;const r=Xo(l);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Symbol.toStringTag in l)&&!(Symbol.iterator in l)},ky=$t("Date"),qy=$t("File"),Gy=$t("Blob"),Yy=$t("FileList"),Xy=l=>Vi(l)&&Dt(l.pipe),Vy=l=>{let r;return l&&(typeof FormData=="function"&&l instanceof FormData||Dt(l.append)&&((r=Yi(l))==="formdata"||r==="object"&&Dt(l.toString)&&l.toString()==="[object FormData]"))},Qy=$t("URLSearchParams"),[Zy,Ky,Jy,$y]=["ReadableStream","Request","Response","Headers"].map($t),Py=l=>l.trim?l.trim():l.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ps(l,r,{allOwnKeys:u=!1}={}){if(l===null||typeof l>"u")return;let c,f;if(typeof l!="object"&&(l=[l]),ol(l))for(c=0,f=l.length;c<f;c++)r.call(null,l[c],c,l);else{const d=u?Object.getOwnPropertyNames(l):Object.keys(l),m=d.length;let x;for(c=0;c<m;c++)x=d[c],r.call(null,l[x],x,l)}}function $m(l,r){r=r.toLowerCase();const u=Object.keys(l);let c=u.length,f;for(;c-- >0;)if(f=u[c],r===f.toLowerCase())return f;return null}const mn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Pm=l=>!hs(l)&&l!==mn;function To(){const{caseless:l}=Pm(this)&&this||{},r={},u=(c,f)=>{const d=l&&$m(r,f)||f;zi(r[d])&&zi(c)?r[d]=To(r[d],c):zi(c)?r[d]=To({},c):ol(c)?r[d]=c.slice():r[d]=c};for(let c=0,f=arguments.length;c<f;c++)arguments[c]&&ps(arguments[c],u);return r}const Fy=(l,r,u,{allOwnKeys:c}={})=>(ps(r,(f,d)=>{u&&Dt(f)?l[d]=Zm(f,u):l[d]=f},{allOwnKeys:c}),l),Wy=l=>(l.charCodeAt(0)===65279&&(l=l.slice(1)),l),Iy=(l,r,u,c)=>{l.prototype=Object.create(r.prototype,c),l.prototype.constructor=l,Object.defineProperty(l,"super",{value:r.prototype}),u&&Object.assign(l.prototype,u)},ev=(l,r,u,c)=>{let f,d,m;const x={};if(r=r||{},l==null)return r;do{for(f=Object.getOwnPropertyNames(l),d=f.length;d-- >0;)m=f[d],(!c||c(m,l,r))&&!x[m]&&(r[m]=l[m],x[m]=!0);l=u!==!1&&Xo(l)}while(l&&(!u||u(l,r))&&l!==Object.prototype);return r},tv=(l,r,u)=>{l=String(l),(u===void 0||u>l.length)&&(u=l.length),u-=r.length;const c=l.indexOf(r,u);return c!==-1&&c===u},av=l=>{if(!l)return null;if(ol(l))return l;let r=l.length;if(!Jm(r))return null;const u=new Array(r);for(;r-- >0;)u[r]=l[r];return u},nv=(l=>r=>l&&r instanceof l)(typeof Uint8Array<"u"&&Xo(Uint8Array)),lv=(l,r)=>{const c=(l&&l[Symbol.iterator]).call(l);let f;for(;(f=c.next())&&!f.done;){const d=f.value;r.call(l,d[0],d[1])}},sv=(l,r)=>{let u;const c=[];for(;(u=l.exec(r))!==null;)c.push(u);return c},iv=$t("HTMLFormElement"),rv=l=>l.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(u,c,f){return c.toUpperCase()+f}),cm=(({hasOwnProperty:l})=>(r,u)=>l.call(r,u))(Object.prototype),cv=$t("RegExp"),Fm=(l,r)=>{const u=Object.getOwnPropertyDescriptors(l),c={};ps(u,(f,d)=>{let m;(m=r(f,d,l))!==!1&&(c[d]=m||f)}),Object.defineProperties(l,c)},ov=l=>{Fm(l,(r,u)=>{if(Dt(l)&&["arguments","caller","callee"].indexOf(u)!==-1)return!1;const c=l[u];if(Dt(c)){if(r.enumerable=!1,"writable"in r){r.writable=!1;return}r.set||(r.set=()=>{throw Error("Can not rewrite read-only method '"+u+"'")})}})},uv=(l,r)=>{const u={},c=f=>{f.forEach(d=>{u[d]=!0})};return ol(l)?c(l):c(String(l).split(r)),u},fv=()=>{},dv=(l,r)=>l!=null&&Number.isFinite(l=+l)?l:r;function hv(l){return!!(l&&Dt(l.append)&&l[Symbol.toStringTag]==="FormData"&&l[Symbol.iterator])}const mv=l=>{const r=new Array(10),u=(c,f)=>{if(Vi(c)){if(r.indexOf(c)>=0)return;if(!("toJSON"in c)){r[f]=c;const d=ol(c)?[]:{};return ps(c,(m,x)=>{const y=u(m,f+1);!hs(y)&&(d[x]=y)}),r[f]=void 0,d}}return c};return u(l,0)},gv=$t("AsyncFunction"),pv=l=>l&&(Vi(l)||Dt(l))&&Dt(l.then)&&Dt(l.catch),Wm=((l,r)=>l?setImmediate:r?((u,c)=>(mn.addEventListener("message",({source:f,data:d})=>{f===mn&&d===u&&c.length&&c.shift()()},!1),f=>{c.push(f),mn.postMessage(u,"*")}))(`axios@${Math.random()}`,[]):u=>setTimeout(u))(typeof setImmediate=="function",Dt(mn.postMessage)),xv=typeof queueMicrotask<"u"?queueMicrotask.bind(mn):typeof process<"u"&&process.nextTick||Wm,k={isArray:ol,isArrayBuffer:Km,isBuffer:Uy,isFormData:Vy,isArrayBufferView:By,isString:Ly,isNumber:Jm,isBoolean:Hy,isObject:Vi,isPlainObject:zi,isReadableStream:Zy,isRequest:Ky,isResponse:Jy,isHeaders:$y,isUndefined:hs,isDate:ky,isFile:qy,isBlob:Gy,isRegExp:cv,isFunction:Dt,isStream:Xy,isURLSearchParams:Qy,isTypedArray:nv,isFileList:Yy,forEach:ps,merge:To,extend:Fy,trim:Py,stripBOM:Wy,inherits:Iy,toFlatObject:ev,kindOf:Yi,kindOfTest:$t,endsWith:tv,toArray:av,forEachEntry:lv,matchAll:sv,isHTMLForm:iv,hasOwnProperty:cm,hasOwnProp:cm,reduceDescriptors:Fm,freezeMethods:ov,toObjectSet:uv,toCamelCase:rv,noop:fv,toFiniteNumber:dv,findKey:$m,global:mn,isContextDefined:Pm,isSpecCompliantForm:hv,toJSONObject:mv,isAsyncFn:gv,isThenable:pv,setImmediate:Wm,asap:xv};function fe(l,r,u,c,f){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=l,this.name="AxiosError",r&&(this.code=r),u&&(this.config=u),c&&(this.request=c),f&&(this.response=f,this.status=f.status?f.status:null)}k.inherits(fe,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:k.toJSONObject(this.config),code:this.code,status:this.status}}});const Im=fe.prototype,e0={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(l=>{e0[l]={value:l}});Object.defineProperties(fe,e0);Object.defineProperty(Im,"isAxiosError",{value:!0});fe.from=(l,r,u,c,f,d)=>{const m=Object.create(Im);return k.toFlatObject(l,m,function(y){return y!==Error.prototype},x=>x!=="isAxiosError"),fe.call(m,l.message,r,u,c,f),m.cause=l,m.name=l.name,d&&Object.assign(m,d),m};const yv=null;function Eo(l){return k.isPlainObject(l)||k.isArray(l)}function t0(l){return k.endsWith(l,"[]")?l.slice(0,-2):l}function om(l,r,u){return l?l.concat(r).map(function(f,d){return f=t0(f),!u&&d?"["+f+"]":f}).join(u?".":""):r}function vv(l){return k.isArray(l)&&!l.some(Eo)}const bv=k.toFlatObject(k,{},null,function(r){return/^is[A-Z]/.test(r)});function Qi(l,r,u){if(!k.isObject(l))throw new TypeError("target must be an object");r=r||new FormData,u=k.toFlatObject(u,{metaTokens:!0,dots:!1,indexes:!1},!1,function(E,M){return!k.isUndefined(M[E])});const c=u.metaTokens,f=u.visitor||v,d=u.dots,m=u.indexes,y=(u.Blob||typeof Blob<"u"&&Blob)&&k.isSpecCompliantForm(r);if(!k.isFunction(f))throw new TypeError("visitor must be a function");function p(U){if(U===null)return"";if(k.isDate(U))return U.toISOString();if(!y&&k.isBlob(U))throw new fe("Blob is not supported. Use a Buffer instead.");return k.isArrayBuffer(U)||k.isTypedArray(U)?y&&typeof Blob=="function"?new Blob([U]):Buffer.from(U):U}function v(U,E,M){let R=U;if(U&&!M&&typeof U=="object"){if(k.endsWith(E,"{}"))E=c?E:E.slice(0,-2),U=JSON.stringify(U);else if(k.isArray(U)&&vv(U)||(k.isFileList(U)||k.endsWith(E,"[]"))&&(R=k.toArray(U)))return E=t0(E),R.forEach(function(w,q){!(k.isUndefined(w)||w===null)&&r.append(m===!0?om([E],q,d):m===null?E:E+"[]",p(w))}),!1}return Eo(U)?!0:(r.append(om(M,E,d),p(U)),!1)}const b=[],O=Object.assign(bv,{defaultVisitor:v,convertValue:p,isVisitable:Eo});function H(U,E){if(!k.isUndefined(U)){if(b.indexOf(U)!==-1)throw Error("Circular reference detected in "+E.join("."));b.push(U),k.forEach(U,function(R,D){(!(k.isUndefined(R)||R===null)&&f.call(r,R,k.isString(D)?D.trim():D,E,O))===!0&&H(R,E?E.concat(D):[D])}),b.pop()}}if(!k.isObject(l))throw new TypeError("data must be an object");return H(l),r}function um(l){const r={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(l).replace(/[!'()~]|%20|%00/g,function(c){return r[c]})}function Vo(l,r){this._pairs=[],l&&Qi(l,this,r)}const a0=Vo.prototype;a0.append=function(r,u){this._pairs.push([r,u])};a0.toString=function(r){const u=r?function(c){return r.call(this,c,um)}:um;return this._pairs.map(function(f){return u(f[0])+"="+u(f[1])},"").join("&")};function jv(l){return encodeURIComponent(l).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function n0(l,r,u){if(!r)return l;const c=u&&u.encode||jv;k.isFunction(u)&&(u={serialize:u});const f=u&&u.serialize;let d;if(f?d=f(r,u):d=k.isURLSearchParams(r)?r.toString():new Vo(r,u).toString(c),d){const m=l.indexOf("#");m!==-1&&(l=l.slice(0,m)),l+=(l.indexOf("?")===-1?"?":"&")+d}return l}class fm{constructor(){this.handlers=[]}use(r,u,c){return this.handlers.push({fulfilled:r,rejected:u,synchronous:c?c.synchronous:!1,runWhen:c?c.runWhen:null}),this.handlers.length-1}eject(r){this.handlers[r]&&(this.handlers[r]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(r){k.forEach(this.handlers,function(c){c!==null&&r(c)})}}const l0={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Nv=typeof URLSearchParams<"u"?URLSearchParams:Vo,Sv=typeof FormData<"u"?FormData:null,wv=typeof Blob<"u"?Blob:null,Tv={isBrowser:!0,classes:{URLSearchParams:Nv,FormData:Sv,Blob:wv},protocols:["http","https","file","blob","url","data"]},Qo=typeof window<"u"&&typeof document<"u",_o=typeof navigator=="object"&&navigator||void 0,Ev=Qo&&(!_o||["ReactNative","NativeScript","NS"].indexOf(_o.product)<0),_v=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Ov=Qo&&window.location.href||"http://localhost",Cv=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Qo,hasStandardBrowserEnv:Ev,hasStandardBrowserWebWorkerEnv:_v,navigator:_o,origin:Ov},Symbol.toStringTag,{value:"Module"})),ft={...Cv,...Tv};function Av(l,r){return Qi(l,new ft.classes.URLSearchParams,Object.assign({visitor:function(u,c,f,d){return ft.isNode&&k.isBuffer(u)?(this.append(c,u.toString("base64")),!1):d.defaultVisitor.apply(this,arguments)}},r))}function Rv(l){return k.matchAll(/\w+|\[(\w*)]/g,l).map(r=>r[0]==="[]"?"":r[1]||r[0])}function Mv(l){const r={},u=Object.keys(l);let c;const f=u.length;let d;for(c=0;c<f;c++)d=u[c],r[d]=l[d];return r}function s0(l){function r(u,c,f,d){let m=u[d++];if(m==="__proto__")return!0;const x=Number.isFinite(+m),y=d>=u.length;return m=!m&&k.isArray(f)?f.length:m,y?(k.hasOwnProp(f,m)?f[m]=[f[m],c]:f[m]=c,!x):((!f[m]||!k.isObject(f[m]))&&(f[m]=[]),r(u,c,f[m],d)&&k.isArray(f[m])&&(f[m]=Mv(f[m])),!x)}if(k.isFormData(l)&&k.isFunction(l.entries)){const u={};return k.forEachEntry(l,(c,f)=>{r(Rv(c),f,u,0)}),u}return null}function zv(l,r,u){if(k.isString(l))try{return(r||JSON.parse)(l),k.trim(l)}catch(c){if(c.name!=="SyntaxError")throw c}return(u||JSON.stringify)(l)}const xs={transitional:l0,adapter:["xhr","http","fetch"],transformRequest:[function(r,u){const c=u.getContentType()||"",f=c.indexOf("application/json")>-1,d=k.isObject(r);if(d&&k.isHTMLForm(r)&&(r=new FormData(r)),k.isFormData(r))return f?JSON.stringify(s0(r)):r;if(k.isArrayBuffer(r)||k.isBuffer(r)||k.isStream(r)||k.isFile(r)||k.isBlob(r)||k.isReadableStream(r))return r;if(k.isArrayBufferView(r))return r.buffer;if(k.isURLSearchParams(r))return u.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),r.toString();let x;if(d){if(c.indexOf("application/x-www-form-urlencoded")>-1)return Av(r,this.formSerializer).toString();if((x=k.isFileList(r))||c.indexOf("multipart/form-data")>-1){const y=this.env&&this.env.FormData;return Qi(x?{"files[]":r}:r,y&&new y,this.formSerializer)}}return d||f?(u.setContentType("application/json",!1),zv(r)):r}],transformResponse:[function(r){const u=this.transitional||xs.transitional,c=u&&u.forcedJSONParsing,f=this.responseType==="json";if(k.isResponse(r)||k.isReadableStream(r))return r;if(r&&k.isString(r)&&(c&&!this.responseType||f)){const m=!(u&&u.silentJSONParsing)&&f;try{return JSON.parse(r)}catch(x){if(m)throw x.name==="SyntaxError"?fe.from(x,fe.ERR_BAD_RESPONSE,this,null,this.response):x}}return r}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ft.classes.FormData,Blob:ft.classes.Blob},validateStatus:function(r){return r>=200&&r<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};k.forEach(["delete","get","head","post","put","patch"],l=>{xs.headers[l]={}});const Dv=k.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Uv=l=>{const r={};let u,c,f;return l&&l.split(`
`).forEach(function(m){f=m.indexOf(":"),u=m.substring(0,f).trim().toLowerCase(),c=m.substring(f+1).trim(),!(!u||r[u]&&Dv[u])&&(u==="set-cookie"?r[u]?r[u].push(c):r[u]=[c]:r[u]=r[u]?r[u]+", "+c:c)}),r},dm=Symbol("internals");function us(l){return l&&String(l).trim().toLowerCase()}function Di(l){return l===!1||l==null?l:k.isArray(l)?l.map(Di):String(l)}function Bv(l){const r=Object.create(null),u=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let c;for(;c=u.exec(l);)r[c[1]]=c[2];return r}const Lv=l=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(l.trim());function yo(l,r,u,c,f){if(k.isFunction(c))return c.call(this,r,u);if(f&&(r=u),!!k.isString(r)){if(k.isString(c))return r.indexOf(c)!==-1;if(k.isRegExp(c))return c.test(r)}}function Hv(l){return l.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(r,u,c)=>u.toUpperCase()+c)}function kv(l,r){const u=k.toCamelCase(" "+r);["get","set","has"].forEach(c=>{Object.defineProperty(l,c+u,{value:function(f,d,m){return this[c].call(this,r,f,d,m)},configurable:!0})})}let Nt=class{constructor(r){r&&this.set(r)}set(r,u,c){const f=this;function d(x,y,p){const v=us(y);if(!v)throw new Error("header name must be a non-empty string");const b=k.findKey(f,v);(!b||f[b]===void 0||p===!0||p===void 0&&f[b]!==!1)&&(f[b||y]=Di(x))}const m=(x,y)=>k.forEach(x,(p,v)=>d(p,v,y));if(k.isPlainObject(r)||r instanceof this.constructor)m(r,u);else if(k.isString(r)&&(r=r.trim())&&!Lv(r))m(Uv(r),u);else if(k.isHeaders(r))for(const[x,y]of r.entries())d(y,x,c);else r!=null&&d(u,r,c);return this}get(r,u){if(r=us(r),r){const c=k.findKey(this,r);if(c){const f=this[c];if(!u)return f;if(u===!0)return Bv(f);if(k.isFunction(u))return u.call(this,f,c);if(k.isRegExp(u))return u.exec(f);throw new TypeError("parser must be boolean|regexp|function")}}}has(r,u){if(r=us(r),r){const c=k.findKey(this,r);return!!(c&&this[c]!==void 0&&(!u||yo(this,this[c],c,u)))}return!1}delete(r,u){const c=this;let f=!1;function d(m){if(m=us(m),m){const x=k.findKey(c,m);x&&(!u||yo(c,c[x],x,u))&&(delete c[x],f=!0)}}return k.isArray(r)?r.forEach(d):d(r),f}clear(r){const u=Object.keys(this);let c=u.length,f=!1;for(;c--;){const d=u[c];(!r||yo(this,this[d],d,r,!0))&&(delete this[d],f=!0)}return f}normalize(r){const u=this,c={};return k.forEach(this,(f,d)=>{const m=k.findKey(c,d);if(m){u[m]=Di(f),delete u[d];return}const x=r?Hv(d):String(d).trim();x!==d&&delete u[d],u[x]=Di(f),c[x]=!0}),this}concat(...r){return this.constructor.concat(this,...r)}toJSON(r){const u=Object.create(null);return k.forEach(this,(c,f)=>{c!=null&&c!==!1&&(u[f]=r&&k.isArray(c)?c.join(", "):c)}),u}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([r,u])=>r+": "+u).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(r){return r instanceof this?r:new this(r)}static concat(r,...u){const c=new this(r);return u.forEach(f=>c.set(f)),c}static accessor(r){const c=(this[dm]=this[dm]={accessors:{}}).accessors,f=this.prototype;function d(m){const x=us(m);c[x]||(kv(f,m),c[x]=!0)}return k.isArray(r)?r.forEach(d):d(r),this}};Nt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);k.reduceDescriptors(Nt.prototype,({value:l},r)=>{let u=r[0].toUpperCase()+r.slice(1);return{get:()=>l,set(c){this[u]=c}}});k.freezeMethods(Nt);function vo(l,r){const u=this||xs,c=r||u,f=Nt.from(c.headers);let d=c.data;return k.forEach(l,function(x){d=x.call(u,d,f.normalize(),r?r.status:void 0)}),f.normalize(),d}function i0(l){return!!(l&&l.__CANCEL__)}function ul(l,r,u){fe.call(this,l??"canceled",fe.ERR_CANCELED,r,u),this.name="CanceledError"}k.inherits(ul,fe,{__CANCEL__:!0});function r0(l,r,u){const c=u.config.validateStatus;!u.status||!c||c(u.status)?l(u):r(new fe("Request failed with status code "+u.status,[fe.ERR_BAD_REQUEST,fe.ERR_BAD_RESPONSE][Math.floor(u.status/100)-4],u.config,u.request,u))}function qv(l){const r=/^([-+\w]{1,25})(:?\/\/|:)/.exec(l);return r&&r[1]||""}function Gv(l,r){l=l||10;const u=new Array(l),c=new Array(l);let f=0,d=0,m;return r=r!==void 0?r:1e3,function(y){const p=Date.now(),v=c[d];m||(m=p),u[f]=y,c[f]=p;let b=d,O=0;for(;b!==f;)O+=u[b++],b=b%l;if(f=(f+1)%l,f===d&&(d=(d+1)%l),p-m<r)return;const H=v&&p-v;return H?Math.round(O*1e3/H):void 0}}function Yv(l,r){let u=0,c=1e3/r,f,d;const m=(p,v=Date.now())=>{u=v,f=null,d&&(clearTimeout(d),d=null),l.apply(null,p)};return[(...p)=>{const v=Date.now(),b=v-u;b>=c?m(p,v):(f=p,d||(d=setTimeout(()=>{d=null,m(f)},c-b)))},()=>f&&m(f)]}const ki=(l,r,u=3)=>{let c=0;const f=Gv(50,250);return Yv(d=>{const m=d.loaded,x=d.lengthComputable?d.total:void 0,y=m-c,p=f(y),v=m<=x;c=m;const b={loaded:m,total:x,progress:x?m/x:void 0,bytes:y,rate:p||void 0,estimated:p&&x&&v?(x-m)/p:void 0,event:d,lengthComputable:x!=null,[r?"download":"upload"]:!0};l(b)},u)},hm=(l,r)=>{const u=l!=null;return[c=>r[0]({lengthComputable:u,total:l,loaded:c}),r[1]]},mm=l=>(...r)=>k.asap(()=>l(...r)),Xv=ft.hasStandardBrowserEnv?((l,r)=>u=>(u=new URL(u,ft.origin),l.protocol===u.protocol&&l.host===u.host&&(r||l.port===u.port)))(new URL(ft.origin),ft.navigator&&/(msie|trident)/i.test(ft.navigator.userAgent)):()=>!0,Vv=ft.hasStandardBrowserEnv?{write(l,r,u,c,f,d){const m=[l+"="+encodeURIComponent(r)];k.isNumber(u)&&m.push("expires="+new Date(u).toGMTString()),k.isString(c)&&m.push("path="+c),k.isString(f)&&m.push("domain="+f),d===!0&&m.push("secure"),document.cookie=m.join("; ")},read(l){const r=document.cookie.match(new RegExp("(^|;\\s*)("+l+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove(l){this.write(l,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Qv(l){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(l)}function Zv(l,r){return r?l.replace(/\/?\/$/,"")+"/"+r.replace(/^\/+/,""):l}function c0(l,r,u){let c=!Qv(r);return l&&(c||u==!1)?Zv(l,r):r}const gm=l=>l instanceof Nt?{...l}:l;function xn(l,r){r=r||{};const u={};function c(p,v,b,O){return k.isPlainObject(p)&&k.isPlainObject(v)?k.merge.call({caseless:O},p,v):k.isPlainObject(v)?k.merge({},v):k.isArray(v)?v.slice():v}function f(p,v,b,O){if(k.isUndefined(v)){if(!k.isUndefined(p))return c(void 0,p,b,O)}else return c(p,v,b,O)}function d(p,v){if(!k.isUndefined(v))return c(void 0,v)}function m(p,v){if(k.isUndefined(v)){if(!k.isUndefined(p))return c(void 0,p)}else return c(void 0,v)}function x(p,v,b){if(b in r)return c(p,v);if(b in l)return c(void 0,p)}const y={url:d,method:d,data:d,baseURL:m,transformRequest:m,transformResponse:m,paramsSerializer:m,timeout:m,timeoutMessage:m,withCredentials:m,withXSRFToken:m,adapter:m,responseType:m,xsrfCookieName:m,xsrfHeaderName:m,onUploadProgress:m,onDownloadProgress:m,decompress:m,maxContentLength:m,maxBodyLength:m,beforeRedirect:m,transport:m,httpAgent:m,httpsAgent:m,cancelToken:m,socketPath:m,responseEncoding:m,validateStatus:x,headers:(p,v,b)=>f(gm(p),gm(v),b,!0)};return k.forEach(Object.keys(Object.assign({},l,r)),function(v){const b=y[v]||f,O=b(l[v],r[v],v);k.isUndefined(O)&&b!==x||(u[v]=O)}),u}const o0=l=>{const r=xn({},l);let{data:u,withXSRFToken:c,xsrfHeaderName:f,xsrfCookieName:d,headers:m,auth:x}=r;r.headers=m=Nt.from(m),r.url=n0(c0(r.baseURL,r.url,r.allowAbsoluteUrls),l.params,l.paramsSerializer),x&&m.set("Authorization","Basic "+btoa((x.username||"")+":"+(x.password?unescape(encodeURIComponent(x.password)):"")));let y;if(k.isFormData(u)){if(ft.hasStandardBrowserEnv||ft.hasStandardBrowserWebWorkerEnv)m.setContentType(void 0);else if((y=m.getContentType())!==!1){const[p,...v]=y?y.split(";").map(b=>b.trim()).filter(Boolean):[];m.setContentType([p||"multipart/form-data",...v].join("; "))}}if(ft.hasStandardBrowserEnv&&(c&&k.isFunction(c)&&(c=c(r)),c||c!==!1&&Xv(r.url))){const p=f&&d&&Vv.read(d);p&&m.set(f,p)}return r},Kv=typeof XMLHttpRequest<"u",Jv=Kv&&function(l){return new Promise(function(u,c){const f=o0(l);let d=f.data;const m=Nt.from(f.headers).normalize();let{responseType:x,onUploadProgress:y,onDownloadProgress:p}=f,v,b,O,H,U;function E(){H&&H(),U&&U(),f.cancelToken&&f.cancelToken.unsubscribe(v),f.signal&&f.signal.removeEventListener("abort",v)}let M=new XMLHttpRequest;M.open(f.method.toUpperCase(),f.url,!0),M.timeout=f.timeout;function R(){if(!M)return;const w=Nt.from("getAllResponseHeaders"in M&&M.getAllResponseHeaders()),C={data:!x||x==="text"||x==="json"?M.responseText:M.response,status:M.status,statusText:M.statusText,headers:w,config:l,request:M};r0(function($){u($),E()},function($){c($),E()},C),M=null}"onloadend"in M?M.onloadend=R:M.onreadystatechange=function(){!M||M.readyState!==4||M.status===0&&!(M.responseURL&&M.responseURL.indexOf("file:")===0)||setTimeout(R)},M.onabort=function(){M&&(c(new fe("Request aborted",fe.ECONNABORTED,l,M)),M=null)},M.onerror=function(){c(new fe("Network Error",fe.ERR_NETWORK,l,M)),M=null},M.ontimeout=function(){let q=f.timeout?"timeout of "+f.timeout+"ms exceeded":"timeout exceeded";const C=f.transitional||l0;f.timeoutErrorMessage&&(q=f.timeoutErrorMessage),c(new fe(q,C.clarifyTimeoutError?fe.ETIMEDOUT:fe.ECONNABORTED,l,M)),M=null},d===void 0&&m.setContentType(null),"setRequestHeader"in M&&k.forEach(m.toJSON(),function(q,C){M.setRequestHeader(C,q)}),k.isUndefined(f.withCredentials)||(M.withCredentials=!!f.withCredentials),x&&x!=="json"&&(M.responseType=f.responseType),p&&([O,U]=ki(p,!0),M.addEventListener("progress",O)),y&&M.upload&&([b,H]=ki(y),M.upload.addEventListener("progress",b),M.upload.addEventListener("loadend",H)),(f.cancelToken||f.signal)&&(v=w=>{M&&(c(!w||w.type?new ul(null,l,M):w),M.abort(),M=null)},f.cancelToken&&f.cancelToken.subscribe(v),f.signal&&(f.signal.aborted?v():f.signal.addEventListener("abort",v)));const D=qv(f.url);if(D&&ft.protocols.indexOf(D)===-1){c(new fe("Unsupported protocol "+D+":",fe.ERR_BAD_REQUEST,l));return}M.send(d||null)})},$v=(l,r)=>{const{length:u}=l=l?l.filter(Boolean):[];if(r||u){let c=new AbortController,f;const d=function(p){if(!f){f=!0,x();const v=p instanceof Error?p:this.reason;c.abort(v instanceof fe?v:new ul(v instanceof Error?v.message:v))}};let m=r&&setTimeout(()=>{m=null,d(new fe(`timeout ${r} of ms exceeded`,fe.ETIMEDOUT))},r);const x=()=>{l&&(m&&clearTimeout(m),m=null,l.forEach(p=>{p.unsubscribe?p.unsubscribe(d):p.removeEventListener("abort",d)}),l=null)};l.forEach(p=>p.addEventListener("abort",d));const{signal:y}=c;return y.unsubscribe=()=>k.asap(x),y}},Pv=function*(l,r){let u=l.byteLength;if(u<r){yield l;return}let c=0,f;for(;c<u;)f=c+r,yield l.slice(c,f),c=f},Fv=async function*(l,r){for await(const u of Wv(l))yield*Pv(u,r)},Wv=async function*(l){if(l[Symbol.asyncIterator]){yield*l;return}const r=l.getReader();try{for(;;){const{done:u,value:c}=await r.read();if(u)break;yield c}}finally{await r.cancel()}},pm=(l,r,u,c)=>{const f=Fv(l,r);let d=0,m,x=y=>{m||(m=!0,c&&c(y))};return new ReadableStream({async pull(y){try{const{done:p,value:v}=await f.next();if(p){x(),y.close();return}let b=v.byteLength;if(u){let O=d+=b;u(O)}y.enqueue(new Uint8Array(v))}catch(p){throw x(p),p}},cancel(y){return x(y),f.return()}},{highWaterMark:2})},Zi=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",u0=Zi&&typeof ReadableStream=="function",Iv=Zi&&(typeof TextEncoder=="function"?(l=>r=>l.encode(r))(new TextEncoder):async l=>new Uint8Array(await new Response(l).arrayBuffer())),f0=(l,...r)=>{try{return!!l(...r)}catch{return!1}},e1=u0&&f0(()=>{let l=!1;const r=new Request(ft.origin,{body:new ReadableStream,method:"POST",get duplex(){return l=!0,"half"}}).headers.has("Content-Type");return l&&!r}),xm=64*1024,Oo=u0&&f0(()=>k.isReadableStream(new Response("").body)),qi={stream:Oo&&(l=>l.body)};Zi&&(l=>{["text","arrayBuffer","blob","formData","stream"].forEach(r=>{!qi[r]&&(qi[r]=k.isFunction(l[r])?u=>u[r]():(u,c)=>{throw new fe(`Response type '${r}' is not supported`,fe.ERR_NOT_SUPPORT,c)})})})(new Response);const t1=async l=>{if(l==null)return 0;if(k.isBlob(l))return l.size;if(k.isSpecCompliantForm(l))return(await new Request(ft.origin,{method:"POST",body:l}).arrayBuffer()).byteLength;if(k.isArrayBufferView(l)||k.isArrayBuffer(l))return l.byteLength;if(k.isURLSearchParams(l)&&(l=l+""),k.isString(l))return(await Iv(l)).byteLength},a1=async(l,r)=>{const u=k.toFiniteNumber(l.getContentLength());return u??t1(r)},n1=Zi&&(async l=>{let{url:r,method:u,data:c,signal:f,cancelToken:d,timeout:m,onDownloadProgress:x,onUploadProgress:y,responseType:p,headers:v,withCredentials:b="same-origin",fetchOptions:O}=o0(l);p=p?(p+"").toLowerCase():"text";let H=$v([f,d&&d.toAbortSignal()],m),U;const E=H&&H.unsubscribe&&(()=>{H.unsubscribe()});let M;try{if(y&&e1&&u!=="get"&&u!=="head"&&(M=await a1(v,c))!==0){let C=new Request(r,{method:"POST",body:c,duplex:"half"}),W;if(k.isFormData(c)&&(W=C.headers.get("content-type"))&&v.setContentType(W),C.body){const[$,K]=hm(M,ki(mm(y)));c=pm(C.body,xm,$,K)}}k.isString(b)||(b=b?"include":"omit");const R="credentials"in Request.prototype;U=new Request(r,{...O,signal:H,method:u.toUpperCase(),headers:v.normalize().toJSON(),body:c,duplex:"half",credentials:R?b:void 0});let D=await fetch(U);const w=Oo&&(p==="stream"||p==="response");if(Oo&&(x||w&&E)){const C={};["status","statusText","headers"].forEach(oe=>{C[oe]=D[oe]});const W=k.toFiniteNumber(D.headers.get("content-length")),[$,K]=x&&hm(W,ki(mm(x),!0))||[];D=new Response(pm(D.body,xm,$,()=>{K&&K(),E&&E()}),C)}p=p||"text";let q=await qi[k.findKey(qi,p)||"text"](D,l);return!w&&E&&E(),await new Promise((C,W)=>{r0(C,W,{data:q,headers:Nt.from(D.headers),status:D.status,statusText:D.statusText,config:l,request:U})})}catch(R){throw E&&E(),R&&R.name==="TypeError"&&/fetch/i.test(R.message)?Object.assign(new fe("Network Error",fe.ERR_NETWORK,l,U),{cause:R.cause||R}):fe.from(R,R&&R.code,l,U)}}),Co={http:yv,xhr:Jv,fetch:n1};k.forEach(Co,(l,r)=>{if(l){try{Object.defineProperty(l,"name",{value:r})}catch{}Object.defineProperty(l,"adapterName",{value:r})}});const ym=l=>`- ${l}`,l1=l=>k.isFunction(l)||l===null||l===!1,d0={getAdapter:l=>{l=k.isArray(l)?l:[l];const{length:r}=l;let u,c;const f={};for(let d=0;d<r;d++){u=l[d];let m;if(c=u,!l1(u)&&(c=Co[(m=String(u)).toLowerCase()],c===void 0))throw new fe(`Unknown adapter '${m}'`);if(c)break;f[m||"#"+d]=c}if(!c){const d=Object.entries(f).map(([x,y])=>`adapter ${x} `+(y===!1?"is not supported by the environment":"is not available in the build"));let m=r?d.length>1?`since :
`+d.map(ym).join(`
`):" "+ym(d[0]):"as no adapter specified";throw new fe("There is no suitable adapter to dispatch the request "+m,"ERR_NOT_SUPPORT")}return c},adapters:Co};function bo(l){if(l.cancelToken&&l.cancelToken.throwIfRequested(),l.signal&&l.signal.aborted)throw new ul(null,l)}function vm(l){return bo(l),l.headers=Nt.from(l.headers),l.data=vo.call(l,l.transformRequest),["post","put","patch"].indexOf(l.method)!==-1&&l.headers.setContentType("application/x-www-form-urlencoded",!1),d0.getAdapter(l.adapter||xs.adapter)(l).then(function(c){return bo(l),c.data=vo.call(l,l.transformResponse,c),c.headers=Nt.from(c.headers),c},function(c){return i0(c)||(bo(l),c&&c.response&&(c.response.data=vo.call(l,l.transformResponse,c.response),c.response.headers=Nt.from(c.response.headers))),Promise.reject(c)})}const h0="1.8.4",Ki={};["object","boolean","number","function","string","symbol"].forEach((l,r)=>{Ki[l]=function(c){return typeof c===l||"a"+(r<1?"n ":" ")+l}});const bm={};Ki.transitional=function(r,u,c){function f(d,m){return"[Axios v"+h0+"] Transitional option '"+d+"'"+m+(c?". "+c:"")}return(d,m,x)=>{if(r===!1)throw new fe(f(m," has been removed"+(u?" in "+u:"")),fe.ERR_DEPRECATED);return u&&!bm[m]&&(bm[m]=!0,console.warn(f(m," has been deprecated since v"+u+" and will be removed in the near future"))),r?r(d,m,x):!0}};Ki.spelling=function(r){return(u,c)=>(console.warn(`${c} is likely a misspelling of ${r}`),!0)};function s1(l,r,u){if(typeof l!="object")throw new fe("options must be an object",fe.ERR_BAD_OPTION_VALUE);const c=Object.keys(l);let f=c.length;for(;f-- >0;){const d=c[f],m=r[d];if(m){const x=l[d],y=x===void 0||m(x,d,l);if(y!==!0)throw new fe("option "+d+" must be "+y,fe.ERR_BAD_OPTION_VALUE);continue}if(u!==!0)throw new fe("Unknown option "+d,fe.ERR_BAD_OPTION)}}const Ui={assertOptions:s1,validators:Ki},aa=Ui.validators;let gn=class{constructor(r){this.defaults=r,this.interceptors={request:new fm,response:new fm}}async request(r,u){try{return await this._request(r,u)}catch(c){if(c instanceof Error){let f={};Error.captureStackTrace?Error.captureStackTrace(f):f=new Error;const d=f.stack?f.stack.replace(/^.+\n/,""):"";try{c.stack?d&&!String(c.stack).endsWith(d.replace(/^.+\n.+\n/,""))&&(c.stack+=`
`+d):c.stack=d}catch{}}throw c}}_request(r,u){typeof r=="string"?(u=u||{},u.url=r):u=r||{},u=xn(this.defaults,u);const{transitional:c,paramsSerializer:f,headers:d}=u;c!==void 0&&Ui.assertOptions(c,{silentJSONParsing:aa.transitional(aa.boolean),forcedJSONParsing:aa.transitional(aa.boolean),clarifyTimeoutError:aa.transitional(aa.boolean)},!1),f!=null&&(k.isFunction(f)?u.paramsSerializer={serialize:f}:Ui.assertOptions(f,{encode:aa.function,serialize:aa.function},!0)),u.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?u.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:u.allowAbsoluteUrls=!0),Ui.assertOptions(u,{baseUrl:aa.spelling("baseURL"),withXsrfToken:aa.spelling("withXSRFToken")},!0),u.method=(u.method||this.defaults.method||"get").toLowerCase();let m=d&&k.merge(d.common,d[u.method]);d&&k.forEach(["delete","get","head","post","put","patch","common"],U=>{delete d[U]}),u.headers=Nt.concat(m,d);const x=[];let y=!0;this.interceptors.request.forEach(function(E){typeof E.runWhen=="function"&&E.runWhen(u)===!1||(y=y&&E.synchronous,x.unshift(E.fulfilled,E.rejected))});const p=[];this.interceptors.response.forEach(function(E){p.push(E.fulfilled,E.rejected)});let v,b=0,O;if(!y){const U=[vm.bind(this),void 0];for(U.unshift.apply(U,x),U.push.apply(U,p),O=U.length,v=Promise.resolve(u);b<O;)v=v.then(U[b++],U[b++]);return v}O=x.length;let H=u;for(b=0;b<O;){const U=x[b++],E=x[b++];try{H=U(H)}catch(M){E.call(this,M);break}}try{v=vm.call(this,H)}catch(U){return Promise.reject(U)}for(b=0,O=p.length;b<O;)v=v.then(p[b++],p[b++]);return v}getUri(r){r=xn(this.defaults,r);const u=c0(r.baseURL,r.url,r.allowAbsoluteUrls);return n0(u,r.params,r.paramsSerializer)}};k.forEach(["delete","get","head","options"],function(r){gn.prototype[r]=function(u,c){return this.request(xn(c||{},{method:r,url:u,data:(c||{}).data}))}});k.forEach(["post","put","patch"],function(r){function u(c){return function(d,m,x){return this.request(xn(x||{},{method:r,headers:c?{"Content-Type":"multipart/form-data"}:{},url:d,data:m}))}}gn.prototype[r]=u(),gn.prototype[r+"Form"]=u(!0)});let i1=class m0{constructor(r){if(typeof r!="function")throw new TypeError("executor must be a function.");let u;this.promise=new Promise(function(d){u=d});const c=this;this.promise.then(f=>{if(!c._listeners)return;let d=c._listeners.length;for(;d-- >0;)c._listeners[d](f);c._listeners=null}),this.promise.then=f=>{let d;const m=new Promise(x=>{c.subscribe(x),d=x}).then(f);return m.cancel=function(){c.unsubscribe(d)},m},r(function(d,m,x){c.reason||(c.reason=new ul(d,m,x),u(c.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(r){if(this.reason){r(this.reason);return}this._listeners?this._listeners.push(r):this._listeners=[r]}unsubscribe(r){if(!this._listeners)return;const u=this._listeners.indexOf(r);u!==-1&&this._listeners.splice(u,1)}toAbortSignal(){const r=new AbortController,u=c=>{r.abort(c)};return this.subscribe(u),r.signal.unsubscribe=()=>this.unsubscribe(u),r.signal}static source(){let r;return{token:new m0(function(f){r=f}),cancel:r}}};function r1(l){return function(u){return l.apply(null,u)}}function c1(l){return k.isObject(l)&&l.isAxiosError===!0}const Ao={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Ao).forEach(([l,r])=>{Ao[r]=l});function g0(l){const r=new gn(l),u=Zm(gn.prototype.request,r);return k.extend(u,gn.prototype,r,{allOwnKeys:!0}),k.extend(u,r,null,{allOwnKeys:!0}),u.create=function(f){return g0(xn(l,f))},u}const ue=g0(xs);ue.Axios=gn;ue.CanceledError=ul;ue.CancelToken=i1;ue.isCancel=i0;ue.VERSION=h0;ue.toFormData=Qi;ue.AxiosError=fe;ue.Cancel=ue.CanceledError;ue.all=function(r){return Promise.all(r)};ue.spread=r1;ue.isAxiosError=c1;ue.mergeConfig=xn;ue.AxiosHeaders=Nt;ue.formToJSON=l=>s0(k.isHTMLForm(l)?new FormData(l):l);ue.getAdapter=d0.getAdapter;ue.HttpStatusCode=Ao;ue.default=ue;const{Axios:mb,AxiosError:gb,CanceledError:pb,isCancel:xb,CancelToken:yb,VERSION:vb,all:bb,Cancel:jb,isAxiosError:Nb,spread:Sb,toFormData:wb,AxiosHeaders:Tb,HttpStatusCode:Eb,formToJSON:_b,getAdapter:Ob,mergeConfig:Cb}=ue,o1=()=>{const[l,r]=N.useState([]),[u,c]=N.useState(0),[f,d]=N.useState(!1),m=async()=>{try{const y=localStorage.getItem("userEmail"),v=(await ue.get(`http://localhost:5000/api/notifications/${y}`)).data,b=l.length;v.length>b&&v[0].title.includes("Cập nhật đơn hàng")&&localStorage.setItem("orderUpdate","true"),r(v),c(v.filter(O=>!O.is_read).length)}catch(y){console.error("Lỗi khi tải thông báo:",y)}};N.useEffect(()=>{m();const y=setInterval(m,3e4);return()=>clearInterval(y)},[]);const x=async y=>{try{await ue.put(`http://localhost:5000/api/notifications/${y}/read`),m()}catch(p){console.error("Lỗi khi đánh dấu đã đọc:",p)}};return s.jsxs("div",{className:"relative",children:[s.jsxs("button",{onClick:()=>d(!f),className:"relative p-2 text-gray-600 hover:text-gray-800",children:[s.jsx(zy,{className:"text-2xl"}),u>0&&s.jsx("span",{className:"absolute top-0 right-0 bg-red-500 text-white rounded-full w-5 h-5 text-xs flex items-center justify-center",children:u})]}),f&&s.jsxs("div",{className:"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-xl z-50 max-h-96 overflow-y-auto",children:[s.jsx("div",{className:"p-4 border-b",children:s.jsx("h3",{className:"text-lg font-semibold",children:"Thông báo"})}),s.jsx("div",{className:"divide-y",children:l.length===0?s.jsx("div",{className:"p-4 text-gray-500 text-center",children:"Không có thông báo nào"}):l.map(y=>s.jsxs("div",{className:`p-4 hover:bg-gray-50 cursor-pointer ${y.is_read?"":"bg-blue-50"}`,onClick:()=>x(y.id),children:[s.jsx("div",{className:"text-sm font-medium text-gray-900",children:y.title}),s.jsx("div",{className:"text-sm text-gray-500 mt-1",children:y.message}),s.jsx("div",{className:"text-xs text-gray-400 mt-1",children:new Date(y.created_at).toLocaleString("vi-VN")})]},y.id))})]})]})},jm=l=>Math.floor(parseFloat(l)).toString().replace(/\B(?=(\d{3})+(?!\d))/g,"."),u1=({id:l,image:r,title:u,originalPrice:c,price:f,discount:d})=>{const m=Jt(),x=()=>{const y=localStorage.getItem("userAddress")||"";m("/cartpay",{state:{id:l,image:r,title:u,originalPrice:c,price:f,discount:d,userAddress:y}})};return s.jsxs("div",{className:"bg-white shadow-md rounded-lg p-3 w-full max-w-xs  ",children:[s.jsx(ll,{to:`/product/${l}`,children:s.jsx("img",{src:r,alt:u,className:"w-full h-36 object-contain mb-2 cursor-pointer hover:opacity-90 transition"})}),s.jsx("h2",{className:"text-sm font-medium mb-2 line-clamp-2",children:u}),s.jsx("div",{className:"text-xs text-gray-500 mb-2",children:"Quad HD+ (2K+)"}),s.jsxs("div",{className:"text-red-600 text-base font-bold mb-1",children:[jm(f),"₫"]}),s.jsxs("div",{className:"text-gray-400 line-through text-xs mb-2",children:[jm(c),"₫"]}),s.jsxs("div",{className:"text-orange-500 text-xs font-medium mb-2",children:["Quà ",d,".000₫"]}),s.jsxs("div",{className:"flex items-center text-xs text-gray-600 mb-2",children:[s.jsx("span",{className:"text-yellow-500",children:"★"}),s.jsx("span",{className:"ml-1",children:"4.4 • Đã bán 14k"})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded",children:"Đang bán Chạy"}),s.jsx("button",{onClick:x,className:"bg-red-500 hover:bg-red-600 text-white px-2 py-1 text-xs rounded",children:"Mua Ngay"})]})]})},Nm=[],p0=({searchQuery:l="",categoryFilter:r=""})=>{const[u,c]=N.useState([]);N.useEffect(()=>{(async()=>{try{const m=await ue.get("http://localhost:5000/api/products"),x=[...Nm,...m.data];c(x)}catch(m){console.error("Lỗi khi lấy sản phẩm từ API:",m),c(Nm)}})()},[]);const f=u.filter(d=>{const m=!l||d.title.toLowerCase().includes(l.toLowerCase()),x=!r||d.category===r;return m&&x});return console.log("Filtered products count:",f.length),s.jsxs("div",{className:"w-full max-w-[1280px] mx-auto px-4 py-6 min-h-screen",children:[s.jsxs("h1",{className:"text-xl font-bold mb-6",children:[l?`Kết quả tìm kiếm: "${l}"`:r?`Sản phẩm ${r.charAt(0).toUpperCase()+r.slice(1)}`:"Sản phẩm",s.jsxs("span",{className:"text-gray-500 text-sm ml-2",children:["(",f.length," sản phẩm)"]})]}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:f.map(d=>s.jsx(u1,{...d},d.id))})]})};function Kt({onFilterChange:l}){const[r,u]=N.useState(null),[c,f]=N.useState(!1),[d,m]=N.useState([]),[x,y]=N.useState([]),[p,v]=N.useState([]),[b,O]=N.useState(""),[H,U]=N.useState(""),[E,M]=N.useState(""),[R,D]=N.useState(""),[w,q]=N.useState("province"),[C,W]=N.useState(""),[$,K]=N.useState(""),[oe,Ce]=N.useState(""),be=Jt(),{cartItems:ie,getTotalItems:rt,getTotalPrice:Ze,updateQuantity:Ue,removeFromCart:G,isCartOpen:J,setIsCartOpen:ee}=qo(),je=P=>Math.floor(parseFloat(P.toString().replace(/\./g,""))).toString().replace(/\B(?=(\d{3})+(?!\d))/g,".");N.useEffect(()=>{l&&l(!!($||oe))},[$,oe,l]),N.useEffect(()=>{const P=localStorage.getItem("user");P&&u(JSON.parse(P));const Ne=localStorage.getItem("userAddress");if(Ne){const He=Ne.length>25?Ne.substring(0,25)+"...":Ne;D(He)}else D("Địa chỉ của bạn");S()},[]);const S=async()=>{try{const Ne=await(await fetch("https://provinces.open-api.vn/api/p/")).json();m(Ne)}catch(P){console.error("Error fetching provinces:",P)}},Q=async P=>{try{const He=await(await fetch(`https://provinces.open-api.vn/api/p/${P}?depth=2`)).json();y(He.districts||[]),v([])}catch(Ne){console.error("Error fetching districts:",Ne)}},F=async P=>{try{const He=await(await fetch(`https://provinces.open-api.vn/api/d/${P}?depth=2`)).json();v(He.wards||[])}catch(Ne){console.error("Error fetching wards:",Ne)}},Z=()=>{window.confirm("Bạn có chắc chắn muốn đăng xuất không?")&&(localStorage.removeItem("token"),localStorage.removeItem("user"),be("/"))},ae=[{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/phonne-24x24.png",className:"w-5 h-5"}),label:"Điện thoại"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/laptop-24x24.png",className:"w-5 h-5"}),label:"Laptop"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/phu-kien-24x24.png",className:"w-5 h-5"}),label:"Phụ kiện"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/smartwatch-24x24.png",className:"w-5 h-5"}),label:"Smartwatch"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/watch-24x24.png",className:"w-5 h-5"}),label:"Đồng Hồ"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/tablet-24x24.png",className:"w-5 h-5"}),label:"Tablet"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/may-cu-24x24.png",className:"w-5 h-5"}),label:"Mua máy thu cũ"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/PC-24x24.png",className:"w-5 h-5"}),label:"Màn hình, Máy in"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/sim-24x24.png",className:"w-5 h-5"}),label:"Sim, Thẻ cào"},{icon:s.jsx("img",{src:"https://cdn.tgdd.vn/content/tien-ich-24x24.png",className:"w-5 h-5"}),label:"Dịch vụ tiện ích"}],xe=P=>{O(P.code),U(""),M(""),Q(P.code),q("district"),W("")},re=P=>{U(P.code),M(""),F(P.code),q("ward"),W("")},$e=P=>{M(P.code);const Ne=d.find(ne=>ne.code==b),He=x.find(ne=>ne.code==H),dt=`${P.name}, ${He.name}, ${Ne.name}`;D(dt.length>25?dt.substring(0,25)+"...":dt),localStorage.setItem("userAddress",dt),f(!1),q("province"),W("")},ge=()=>{let P=[];return w==="province"?P=d:w==="district"?P=x:w==="ward"&&(P=p),C?P.filter(Ne=>{const He=Ne.name.toLowerCase(),dt=C.toLowerCase();return He.split(" ").some(Ee=>Ee.startsWith(dt))||He.includes(dt)}):P},St=()=>{if(E&&H&&b){const P=d.find(He=>He.code==b),Ne=x.find(He=>He.code==H);return`${Ne==null?void 0:Ne.name}, ${P==null?void 0:P.name}`}if(H&&b){const P=d.find(Ne=>Ne.code==b);return P==null?void 0:P.name}return"Quận 1, Hồ Chí Minh"},na=P=>{const He={"Điện thoại":"phone",Laptop:"laptop","Phụ kiện":"accessory",Smartwatch:"smartwatch","Đồng Hồ":"watch",Tablet:"tablet"}[P];He&&(Ce(He),K(""))};return s.jsxs("div",{children:[s.jsxs("header",{className:"w-full bg-[#ffd400]",children:[s.jsxs("div",{className:"w-full max-w-[1280px] mx-auto flex items-center justify-between px-4 py-2",children:[s.jsxs("div",{className:"flex items-center flex-1 max-w-[600px]",children:[s.jsx("img",{src:"/assets/logo.jpg",alt:"Logo",className:"h-8 object-contain cursor-pointer mr-4",onClick:()=>{K(""),Ce("")}}),s.jsx("div",{className:"relative flex-1",children:s.jsxs("div",{className:"flex items-center bg-white rounded-full px-4 py-2",children:[s.jsx(wo,{className:"text-gray-500 text-sm mr-3"}),s.jsx("input",{type:"text",placeholder:"Bạn tìm gì...",value:$,onChange:P=>K(P.target.value),className:"w-full px-2 py-1 text-sm outline-none bg-transparent"})]})})]}),s.jsxs("div",{className:"flex items-center gap-6 ml-6",children:[r?s.jsxs(s.Fragment,{children:[s.jsx(o1,{}),s.jsxs("div",{className:"flex items-center gap-1 text-sm hover:underline cursor-pointer",onClick:()=>be("/wallet"),children:[s.jsx(Ym,{}),s.jsx("span",{children:"Ví"})]}),s.jsxs("div",{className:"flex items-center gap-1 text-sm hover:underline cursor-pointer",onClick:()=>be("/orders"),children:[s.jsx("span",{children:"📦"}),s.jsx("span",{children:"Đơn hàng"})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("img",{src:r.avatar||"https://i.pravatar.cc/40",alt:"avatar",className:"w-8 h-8 rounded-full cursor-pointer hover:ring-2 hover:ring-blue-300",onClick:()=>be("/profile"),title:"Xem profile"}),s.jsx("span",{className:"text-sm cursor-pointer max-w-[100px] truncate",onClick:()=>be("/profile"),children:r.first_name})]})]}):s.jsxs(s.Fragment,{children:[s.jsxs(ll,{to:"/login",className:"flex items-center gap-1 text-sm hover:underline",children:[s.jsx(Go,{}),s.jsx("span",{children:"Đăng nhập"})]}),s.jsx(ll,{to:"/signup",className:"text-sm hover:underline",children:"Đăng ký"})]}),s.jsxs("div",{className:"flex items-center gap-1 text-sm hover:underline cursor-pointer",onClick:()=>be("/cart"),children:[s.jsx(Gm,{}),s.jsxs("span",{children:["Giỏ (",rt(),")"]})]}),s.jsx(ll,{to:"/support",className:"text-sm font-semibold text-gray-900",children:"Hỗ trợ"}),r&&s.jsx("button",{onClick:Z,className:"text-sm hover:underline",children:"Thoát"}),s.jsxs("div",{className:"flex items-center gap-1 bg-yellow-300 px-3 py-2 rounded-full cursor-pointer text-sm hover:bg-yellow-400 transition-colors",onClick:()=>f(!0),children:[s.jsx(qm,{}),s.jsx("span",{className:"truncate max-w-[150px]",children:R})]})]})]}),s.jsxs("div",{className:"w-full max-w-[1280px] mx-auto px-4 py-2 text-sm font-normal",children:[" ",s.jsx("div",{className:"flex justify-between items-center",children:ae.map((P,Ne)=>s.jsxs("div",{className:"flex items-center gap-1 cursor-pointer hover:underline",onClick:()=>na(P.label),children:[P.icon,s.jsx("span",{children:P.label})]},Ne))})]})]}),c&&s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:s.jsxs("div",{className:"bg-white rounded-lg w-[600px] max-w-[90vw] max-h-[80vh] flex flex-col",children:[s.jsxs("div",{className:"flex items-center justify-between p-6 border-b",children:[s.jsx("h3",{className:"text-xl font-bold",children:"Chọn địa chỉ nhận hàng"}),s.jsx("button",{onClick:()=>f(!1),className:"text-gray-400 hover:text-gray-600 text-2xl",children:"×"})]}),s.jsxs("div",{className:"px-6 py-3 bg-gray-50 border-b",children:[s.jsx("p",{className:"text-sm text-gray-600",children:"Địa chỉ đang chọn:"}),s.jsx("p",{className:"font-medium",children:St()})]}),s.jsx("div",{className:"p-6 border-b",children:s.jsxs("div",{className:"relative",children:[s.jsx(wo,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),s.jsx("input",{type:"text",placeholder:"Tìm nhanh tỉnh thành, quận huyện, phường xã",value:C,onChange:P=>W(P.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})}),s.jsxs("div",{className:"flex border-b",children:[s.jsx("button",{onClick:()=>q("province"),className:`flex-1 py-3 px-4 text-center font-medium ${w==="province"?"text-blue-600 border-b-2 border-blue-600":"text-gray-600 hover:text-gray-800"}`,children:"Tỉnh/TP"}),s.jsx("button",{onClick:()=>q("district"),disabled:!b,className:`flex-1 py-3 px-4 text-center font-medium ${w==="district"&&b?"text-blue-600 border-b-2 border-blue-600":"text-gray-400"}`,children:"Quận/Huyện"}),s.jsx("button",{onClick:()=>q("ward"),disabled:!H,className:`flex-1 py-3 px-4 text-center font-medium ${w==="ward"&&H?"text-blue-600 border-b-2 border-blue-600":"text-gray-400"}`,children:"Phường/Xã"})]}),s.jsx("div",{className:"flex-1 overflow-y-auto p-6",children:s.jsx("div",{className:"grid grid-cols-2 gap-2",children:ge().map(P=>s.jsx("button",{onClick:()=>{w==="province"?xe(P):w==="district"?re(P):w==="ward"&&$e(P)},className:"text-left p-3 hover:bg-gray-100 rounded-lg transition-colors",children:P.name},P.code))})}),s.jsx("div",{className:"p-6 border-t",children:s.jsx("button",{onClick:()=>{q("province"),W(""),O(""),U(""),M("")},className:"text-blue-600 hover:text-blue-800 flex items-center gap-2"})})]})}),$||oe?s.jsx("div",{className:"w-full max-w-[1280px] mx-auto px-4 py-6",children:s.jsx(p0,{searchQuery:$,categoryFilter:oe})}):s.jsx(s.Fragment,{}),J&&s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:s.jsxs("div",{className:"bg-white rounded-lg w-[800px] max-w-[90vw] max-h-[80vh] flex flex-col",children:[s.jsxs("div",{className:"flex items-center justify-between p-6 border-b",children:[s.jsx("h3",{className:"text-xl font-bold",children:"Giỏ hàng của bạn"}),s.jsx("button",{onClick:()=>ee(!1),className:"text-gray-400 hover:text-gray-600 text-2xl",children:"×"})]}),s.jsx("div",{className:"flex-1 overflow-y-auto p-6",children:ie.length===0?s.jsx("p",{className:"text-center text-gray-500",children:"Giỏ hàng trống"}):s.jsx("div",{className:"space-y-4",children:ie.map(P=>s.jsxs("div",{className:"flex items-center gap-4 p-4 border rounded-lg",children:[s.jsx("img",{src:P.image,alt:P.title,className:"w-16 h-16 object-contain"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-medium",children:P.title}),s.jsxs("p",{className:"text-red-600 font-bold",children:[je(P.price),"₫"]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("button",{onClick:()=>Ue(P.id,P.quantity-1),className:"w-8 h-8 flex items-center justify-center border rounded",children:"-"}),s.jsx("span",{className:"w-8 text-center",children:P.quantity}),s.jsx("button",{onClick:()=>Ue(P.id,P.quantity+1),className:"w-8 h-8 flex items-center justify-center border rounded",children:"+"})]}),s.jsx("button",{onClick:()=>G(P.id),className:"text-red-500 hover:text-red-700",children:"Xóa"})]},P.id))})}),ie.length>0&&s.jsxs("div",{className:"p-6 border-t",children:[s.jsxs("div",{className:"flex justify-between items-center mb-4",children:[s.jsx("span",{className:"text-lg font-bold",children:"Tổng cộng:"}),s.jsxs("span",{className:"text-xl font-bold text-red-600",children:[je(Ze()),"₫"]})]}),s.jsx("button",{className:"w-full bg-[#ffd400] hover:bg-yellow-500 text-black font-medium py-3 rounded-lg",children:"Thanh toán"})]})]})})]})}function f1(){const[l,r]=N.useState(""),[u,c]=N.useState(""),f=Jt(),d=async()=>{var m,x,y;try{const p=await ue.post("http://localhost:5000/api/auth/login",{email:l,password:u}),{token:v,user:b}=p.data;localStorage.setItem("token",v),localStorage.setItem("user",JSON.stringify(b)),localStorage.setItem("userEmail",b.email),b.role==="admin"?f("/admin"):f("/home")}catch(p){console.log("Lỗi chi tiết:",((m=p.response)==null?void 0:m.data)||p.message),alert(((y=(x=p.response)==null?void 0:x.data)==null?void 0:y.error)||"Đăng nhập thất bại")}};return s.jsxs("div",{children:[s.jsx(Yo,{}),s.jsx(cl,{}),s.jsx("div",{className:" bg-gray-100 flex flex-col justify-center ",children:s.jsxs("div",{className:"p-10 xs:p-0 mx-auto md:w-full md:max-w-md",children:[s.jsx("h1",{className:"font-bold text-center text-2xl mb-5",children:"-Login Form-"}),s.jsxs("div",{className:"bg-white shadow w-full rounded-lg divide-y divide-gray-200",children:[s.jsxs("div",{className:"px-5 py-7",children:[s.jsx("label",{className:"font-semibold text-sm text-gray-600 pb-1 block",children:"E-mail"}),s.jsx("input",{type:"text",className:"border rounded-lg px-3 py-2 mt-1 mb-5 text-sm w-full",value:l,onChange:m=>r(m.target.value)}),s.jsx("label",{className:"font-semibold text-sm text-gray-600 pb-1 block",children:"Password"}),s.jsx("input",{type:"password",className:"border rounded-lg px-3 py-2 mt-1 mb-5 text-sm w-full",value:u,onChange:m=>c(m.target.value)}),s.jsxs("button",{type:"button",onClick:d,className:"transition duration-200 bg-[#ffd400] hover:bg-[#ffd400] focus:bg-bg-[#ffd400] focus:shadow-sm focus:ring-4 focus:ring-bg-[#ffd400] focus:ring-opacity-50 text-white w-full py-2.5 rounded-lg text-sm shadow-sm hover:shadow-md font-semibold text-center inline-block",children:[s.jsx("span",{className:"inline-block mr-2",children:"Login"}),s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"w-4 h-4 inline-block",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M17 8l4 4m0 0l-4 4m4-4H3"})})]})]}),s.jsx("div",{class:"p-5",children:s.jsxs("div",{class:"grid grid-cols-3 gap-1",children:[s.jsx("button",{type:"button",class:"transition duration-200 border border-gray-200 text-gray-500 w-full py-2.5 rounded-lg text-sm shadow-sm hover:shadow-md font-normal text-center inline-block",children:"MailUp"}),s.jsx("button",{type:"button",class:"transition duration-200 border border-gray-200 text-gray-500 w-full py-2.5 rounded-lg text-sm shadow-sm hover:shadow-md font-normal text-center inline-block",children:"Google"}),s.jsx("button",{type:"button",class:"transition duration-200 border border-gray-200 text-gray-500 w-full py-2.5 rounded-lg text-sm shadow-sm hover:shadow-md font-normal text-center inline-block",children:"Github"})]})}),s.jsxs("div",{class:"py-5",children:[s.jsxs("div",{class:"grid grid-cols-2 gap-1",children:[s.jsx("div",{class:"text-center sm:text-left whitespace-nowrap",children:s.jsxs("button",{class:"transition duration-200 mx-5 px-5 py-4 cursor-pointer font-normal text-sm rounded-lg text-gray-500 hover:bg-gray-100 focus:outline-none focus:bg-gray-200 focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 ring-inset",children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"w-4 h-4 inline-block align-text-top",children:s.jsx("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"})}),s.jsx("span",{class:"inline-block ml-1",children:"Forgot Password"})]})}),s.jsx("div",{class:"text-center sm:text-right whitespace-nowrap",children:s.jsxs("button",{class:"transition duration-200 mx-5 px-5 py-4 cursor-pointer font-normal text-sm rounded-lg text-gray-500 hover:bg-gray-100 focus:outline-none focus:bg-gray-200 focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 ring-inset",children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"w-4 h-4 inline-block align-text-bottom	",children:s.jsx("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"})}),s.jsx("span",{class:"inline-block ml-1",children:"Help"})]})})]}),s.jsx("div",{className:"text-center mt-6",children:s.jsxs("p",{className:"text-sm text-slate-600",children:["Chưa có tài khoản?"," ",s.jsx("button",{onClick:()=>f("/signup"),className:"text-blue-600 hover:underline focus:outline-none",children:"Đăng Kí Ngay"})]})})]})]})]})}),s.jsx(jt,{})]})}function d1(){const[l,r]=N.useState({name:"",lname:"",email:"",password:"",cpassword:""}),[u,c]=N.useState(""),[f,d]=N.useState(""),[m,x]=N.useState(!1),y=Jt(),p=b=>{const{name:O,value:H}=b.target;r(U=>({...U,[O]:H}))},v=async b=>{b.preventDefault(),c(""),d(""),x(!0);try{const O=await fetch("http://localhost:5000/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:l.name,lname:l.lname,email:l.email,password:l.password,cpassword:l.cpassword})}),H=await O.json();if(!O.ok)throw new Error(H.error||"Đăng ký thất bại");d(H.message||"Đăng ký thành công! Đang chuyển hướng..."),r({name:"",lname:"",email:"",password:"",cpassword:""}),setTimeout(()=>{y("/login")},3e3)}catch(O){c(O.message)}finally{x(!1)}};return s.jsxs("div",{children:[s.jsx(Yo,{}),s.jsx(cl,{}),s.jsxs("div",{className:"max-w-4xl max-sm:max-w-lg mx-auto    mt-6",children:[s.jsxs("div",{className:"text-center mb-12 sm:mb-16",children:[s.jsx("a",{href:"/"}),s.jsx("h4",{className:"text-slate-600 text-base mt-6",children:"Đăng ký tài khoản mới"})]}),s.jsxs("form",{onSubmit:v,children:[s.jsxs("div",{className:"grid sm:grid-cols-2 gap-8",children:[s.jsxs("div",{children:[s.jsx("label",{className:"text-slate-800 text-sm font-medium mb-2 block",children:"Họ"}),s.jsx("input",{name:"name",type:"text",value:l.name,onChange:p,className:"bg-slate-100 w-full text-slate-800 text-sm px-4 py-3 rounded focus:bg-transparent outline-blue-500 transition-all",placeholder:"Nhập họ",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"text-slate-800 text-sm font-medium mb-2 block",children:"Tên"}),s.jsx("input",{name:"lname",type:"text",value:l.lname,onChange:p,className:"bg-slate-100 w-full text-slate-800 text-sm px-4 py-3 rounded focus:bg-transparent outline-blue-500 transition-all",placeholder:"Nhập tên",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"text-slate-800 text-sm font-medium mb-2 block",children:"Email"}),s.jsx("input",{name:"email",type:"email",value:l.email,onChange:p,className:"bg-slate-100 w-full text-slate-800 text-sm px-4 py-3 rounded focus:bg-transparent outline-blue-500 transition-all",placeholder:"Nhập email",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"text-slate-800 text-sm font-medium mb-2 block",children:"Mật khẩu"}),s.jsx("input",{name:"password",type:"password",value:l.password,onChange:p,className:"bg-slate-100 w-full text-slate-800 text-sm px-4 py-3 rounded focus:bg-transparent outline-blue-500 transition-all",placeholder:"Nhập mật khẩu",required:!0,minLength:6})]}),s.jsxs("div",{children:[s.jsx("label",{className:"text-slate-800 text-sm font-medium mb-2 block",children:"Xác nhận mật khẩu"}),s.jsx("input",{name:"cpassword",type:"password",value:l.cpassword,onChange:p,className:"bg-slate-100 w-full text-slate-800 text-sm px-4 py-3 rounded focus:bg-transparent outline-blue-500 transition-all",placeholder:"Nhập lại mật khẩu",required:!0,minLength:6})]})]}),u&&s.jsx("div",{className:"mt-4 p-3 bg-red-100 text-red-700 rounded text-sm",children:u}),f&&s.jsx("div",{className:"mt-4 p-3 bg-green-100 text-green-700 rounded text-sm",children:f}),s.jsx("div",{className:"mt-12",children:s.jsx("button",{type:"submit",disabled:m,className:`mx-auto block py-3 px-6 text-sm font-medium tracking-wider rounded text-white ${m?"bg-blue-400":"bg-blue-600 hover:bg-blue-700"} focus:outline-none transition-colors`,children:m?"Đang xử lý...":"Đăng ký"})})]}),s.jsx("div",{className:"text-center mt-6",children:s.jsxs("p",{className:"text-sm text-slate-600",children:["Đã có tài khoản?"," ",s.jsx("button",{onClick:()=>y("/login"),className:"text-blue-600 hover:underline focus:outline-none",children:"Đăng nhập ngay"})]})})]}),s.jsx(jt,{})]})}const h1=()=>{const[l,r]=N.useState([]),[u,c]=N.useState(!0),[f,d]=N.useState(null),[m,x]=N.useState(null),[y,p]=N.useState(""),v=async()=>{try{const O=localStorage.getItem("token");console.log("Token:",O);const H=await ue.get("http://localhost:5000/api/support",{headers:{Authorization:`Bearer ${O}`}});console.log("Response data:",H.data),r(H.data),c(!1)}catch(O){console.error("Error details:",O),d("Không thể tải danh sách yêu cầu hỗ trợ"),c(!1)}};N.useEffect(()=>{v()},[]);const b=async O=>{try{const H=localStorage.getItem("token");console.log("Sending reply:",{requestId:O,reply:y,token:H}),await ue.post(`http://localhost:5000/api/support/${O}/reply`,{reply:y},{headers:{Authorization:`Bearer ${H}`,"Content-Type":"application/json"}}),p(""),x(null),v(),alert("Phản hồi đã được gửi thành công!")}catch(H){console.error("Error sending reply:",H),alert("Không thể gửi phản hồi. Vui lòng thử lại.")}};return u?s.jsx("div",{className:"text-center py-4",children:"Đang tải..."}):f?s.jsx("div",{className:"text-red-500 text-center py-4",children:f}):s.jsxs("div",{className:"space-y-6",children:[s.jsx("h2",{className:"text-2xl font-bold",children:"Quản lý yêu cầu hỗ trợ"}),l.length===0?s.jsx("p",{className:"text-gray-500 text-center py-8",children:"Chưa có yêu cầu hỗ trợ nào."}):s.jsx("div",{className:"grid gap-6",children:l.map(O=>s.jsxs("div",{className:`bg-white p-6 rounded-lg shadow ${O.status==="pending"?"border-l-4 border-yellow-500":"border-l-4 border-green-500"}`,children:[s.jsxs("div",{className:"flex justify-between items-start mb-4",children:[s.jsxs("div",{children:[s.jsx("h3",{className:"font-semibold text-lg",children:O.name}),s.jsx("p",{className:"text-gray-600",children:O.email})]}),s.jsx("div",{className:"text-sm text-gray-500",children:new Date(O.created_at).toLocaleString("vi-VN")})]}),s.jsxs("div",{className:"mb-4",children:[s.jsxs("div",{className:"text-sm font-semibold text-gray-500 mb-1",children:["Chủ đề: ",O.topic]}),s.jsx("p",{className:"text-gray-700",children:O.message})]}),O.reply?s.jsxs("div",{className:"bg-green-50 p-4 rounded-md",children:[s.jsx("div",{className:"font-semibold text-green-800 mb-2",children:"Phản hồi:"}),s.jsx("p",{className:"text-green-700",children:O.reply}),s.jsxs("div",{className:"text-sm text-green-600 mt-2",children:["Đã trả lời: ",new Date(O.replied_at).toLocaleString("vi-VN")]})]}):s.jsx("div",{children:m===O.id?s.jsxs("div",{className:"space-y-3",children:[s.jsx("textarea",{value:y,onChange:H=>p(H.target.value),className:"w-full border rounded-md p-3 h-32",placeholder:"Nhập nội dung phản hồi..."}),s.jsxs("div",{className:"flex gap-2",children:[s.jsx("button",{onClick:()=>b(O.id),className:"bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600",children:"Gửi phản hồi"}),s.jsx("button",{onClick:()=>{x(null),p("")},className:"bg-gray-300 px-4 py-2 rounded-md hover:bg-gray-400",children:"Hủy"})]})]}):s.jsx("button",{onClick:()=>x(O.id),className:"text-blue-500 hover:text-blue-600",children:"Trả lời yêu cầu này"})})]},O.id))})]})},m1=()=>{const[l,r]=N.useState([]),[u,c]=N.useState(null);N.useEffect(()=>{f()},[]);const f=async()=>{try{const x=localStorage.getItem("token"),y=await ue.get("http://localhost:5000/api/orders",{headers:{Authorization:`Bearer ${x}`}});r(y.data),c(null)}catch(x){console.error("Lỗi khi lấy danh sách đơn hàng:",x),c("Không thể tải danh sách đơn hàng")}},d=async(x,y)=>{var p,v,b;try{const O=localStorage.getItem("token"),H=await ue.put(`http://localhost:5000/api/orders/${x}`,{status:y},{headers:{Authorization:`Bearer ${O}`,"Content-Type":"application/json"}});if(H.data.success)r(l.map(U=>U.id===x?{...U,status:y}:U));else throw new Error(H.data.error||"Cập nhật thất bại")}catch(O){console.error("Error updating status:",((p=O.response)==null?void 0:p.data)||O),alert(((b=(v=O.response)==null?void 0:v.data)==null?void 0:b.error)||"Không thể cập nhật trạng thái đơn hàng")}},m=x=>{switch(x){case"pending":return"bg-yellow-100 text-yellow-800";case"confirmed":return"bg-blue-100 text-blue-800";case"shipping":return"bg-purple-100 text-purple-800";case"completed":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return u?s.jsx("div",{className:"p-6 text-red-600 text-center",children:u}):s.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[s.jsx("h1",{className:"text-4xl font-extrabold text-center text-gray-800 mb-8",children:"📦 Quản lý đơn hàng"}),s.jsx("div",{className:"overflow-x-auto shadow rounded-lg",children:s.jsxs("table",{className:"min-w-full text-sm text-gray-800 bg-white border border-gray-200",children:[s.jsx("thead",{className:"bg-gray-100 text-xs uppercase font-semibold text-gray-600",children:s.jsxs("tr",{children:[s.jsx("th",{className:"px-4 py-3 text-left",children:"ID"}),s.jsx("th",{className:"px-4 py-3 text-left",children:"Khách hàng"}),s.jsx("th",{className:"px-4 py-3 text-left",children:"Email"}),s.jsx("th",{className:"px-4 py-3 text-left",children:"Sản phẩm"}),s.jsx("th",{className:"px-4 py-3 text-left",children:"Giá"}),s.jsx("th",{className:"px-4 py-3 text-left",children:"Liên hệ"}),s.jsx("th",{className:"px-4 py-3 text-left",children:"Địa chỉ"}),s.jsx("th",{className:"px-4 py-3 text-left",children:"Ngày đặt"}),s.jsx("th",{className:"px-4 py-3 text-left",children:"Trạng thái"}),s.jsx("th",{className:"px-4 py-3 text-left",children:"Cập nhật"})]})}),s.jsx("tbody",{children:l.map(x=>s.jsxs("tr",{className:"border-t hover:bg-gray-50 transition duration-150",children:[s.jsx("td",{className:"px-4 py-3",children:x.id}),s.jsx("td",{className:"px-4 py-3 font-medium",children:x.full_name}),s.jsx("td",{className:"px-4 py-3",children:x.email}),s.jsx("td",{className:"px-4 py-3",children:x.product_title}),s.jsxs("td",{className:"px-4 py-3 text-green-600 font-semibold",children:[x.product_price,"đ"]}),s.jsx("td",{className:"px-4 py-3",children:x.phone}),s.jsx("td",{className:"px-4 py-3",children:x.address}),s.jsx("td",{className:"px-4 py-3",children:new Date(x.created_at).toLocaleDateString("vi-VN")}),s.jsx("td",{className:"px-4 py-3",children:s.jsx("span",{className:`inline-block px-3 py-1 rounded-full text-xs font-semibold ${m(x.status)}`,children:x.status||"pending"})}),s.jsx("td",{className:"px-4 py-3",children:s.jsxs("select",{className:"border rounded px-2 py-1 bg-white text-sm",value:x.status||"pending",onChange:y=>d(x.id,y.target.value),children:[s.jsx("option",{value:"confirmed",children:"Chờ xác nhận"}),s.jsx("option",{value:"confirmed",children:"Đã xác nhận"}),s.jsx("option",{value:"shipping",children:"Đang giao hàng"}),s.jsx("option",{value:"completed",children:"Hoàn thành"}),s.jsx("option",{value:"cancelled",children:"Đã hủy"})]})})]},x.id))})]})})]})};function g1(){const[l,r]=N.useState([]),u=async()=>{try{const d=localStorage.getItem("token"),m=await ue.get("http://localhost:5000/api/admin/deposits",{headers:{Authorization:`Bearer ${d}`}});r(m.data)}catch(d){console.error("Lỗi lấy danh sách nạp tiền:",d)}},c=async d=>{try{const m=localStorage.getItem("token");await ue.put(`http://localhost:5000/api/admin/deposits/${d}/approve`,{},{headers:{Authorization:`Bearer ${m}`}}),u(),alert("Đã duyệt yêu cầu nạp tiền")}catch{alert("Lỗi duyệt yêu cầu")}},f=async d=>{try{const m=localStorage.getItem("token");await ue.put(`http://localhost:5000/api/admin/deposits/${d}/reject`,{},{headers:{Authorization:`Bearer ${m}`}}),u(),alert("Đã từ chối yêu cầu nạp tiền")}catch{alert("Lỗi từ chối yêu cầu")}};return N.useEffect(()=>{u()},[]),s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold mb-6 text-center",children:"Quản lý ví"}),s.jsx("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:s.jsxs("table",{className:"w-full",children:[s.jsx("thead",{className:"bg-gray-50",children:s.jsxs("tr",{children:[s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Người dùng"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Số tiền"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Mã nạp"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Trạng thái"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Thời gian"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Hành động"})]})}),s.jsx("tbody",{className:"divide-y divide-gray-200",children:l.map(d=>s.jsxs("tr",{children:[s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:d.user_email}),s.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[parseInt(d.amount).toLocaleString()," VNĐ"]}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-mono text-blue-600",children:d.transfer_code}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${d.status==="pending"?"bg-yellow-100 text-yellow-800":d.status==="approved"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:d.status==="pending"?"Chờ duyệt":d.status==="approved"?"Đã duyệt":"Từ chối"})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(d.created_at).toLocaleString("vi-VN")}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:d.status==="pending"&&s.jsxs("div",{className:"flex gap-2",children:[s.jsx("button",{onClick:()=>c(d.id),className:"bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-xs",children:"Duyệt"}),s.jsx("button",{onClick:()=>f(d.id),className:"bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-xs",children:"Từ chối"})]})})]},d.id))})]})})]})}const p1=()=>{const[l,r]=N.useState("products"),[u,c]=N.useState([]),[f,d]=N.useState({title:"",originalPrice:"",price:"",discount:"",tag:"",image:"",category:"phone"}),[m,x]=N.useState(null),[y,p]=N.useState(""),v=Jt(),b=[{value:"phone",label:"📱 Điện thoại"},{value:"laptop",label:"💻 Laptop"},{value:"accessory",label:"🎧 Phụ kiện"},{value:"smartwatch",label:"⌚ Smartwatch"},{value:"watch",label:"⏰ Đồng hồ"},{value:"tablet",label:"📱 Tablet"}],O=()=>{window.confirm("Bạn có chắc chắn muốn đăng xuất không?")&&(localStorage.removeItem("token"),localStorage.removeItem("user"),v("/"))},H=async()=>{try{const C=await ue.get("http://localhost:5000/api/products");c(C.data)}catch(C){console.error("Lỗi khi lấy sản phẩm:",C)}};N.useEffect(()=>{H()},[]);const U=async C=>{var W,$,K;C.preventDefault();try{const oe={title:f.title,originalPrice:f.originalPrice.replace(/\./g,""),price:f.price.replace(/\./g,""),discount:f.discount,tag:f.tag,image:f.image,category:f.category};if(console.log("Sending data:",oe),console.log("Editing product:",m),m){const Ce=await ue.put(`http://localhost:5000/api/products/${m.id}`,oe);console.log("Update response:",Ce.data),alert("Cập nhật sản phẩm thành công!"),x(null)}else{const Ce=await ue.post("http://localhost:5000/api/products",oe);console.log("Create response:",Ce.data),alert("Thêm sản phẩm thành công!")}d({title:"",originalPrice:"",price:"",discount:"",tag:"",image:"",category:"phone"}),p(""),H()}catch(oe){console.error("Chi tiết lỗi:",((W=oe.response)==null?void 0:W.data)||oe.message),alert(`Lỗi: ${((K=($=oe.response)==null?void 0:$.data)==null?void 0:K.error)||oe.message}`)}},E=async C=>{try{await ue.delete(`http://localhost:5000/api/products/${C}`),H()}catch(W){console.error("Lỗi khi xoá sản phẩm:",W)}},M=C=>{x(C);const W=Math.floor(parseFloat(C.originalPrice)),$=Math.floor(parseFloat(C.price));d({title:C.title,originalPrice:W.toString().replace(/\B(?=(\d{3})+(?!\d))/g,"."),price:$.toString().replace(/\B(?=(\d{3})+(?!\d))/g,"."),discount:C.discount,tag:C.tag,image:C.image,category:C.category||"phone"}),p(C.image)},R=C=>C.replace(/\D/g,"").replace(/\B(?=(\d{3})+(?!\d))/g,"."),D=(C,W)=>{const $=R(W);d({...f,[C]:$})},w=C=>Math.floor(parseFloat(C)).toString().replace(/\B(?=(\d{3})+(?!\d))/g,"."),q=C=>{const W=C.target.files[0];if(W){const $=new FileReader;$.onloadend=()=>{p($.result),d({...f,image:$.result})},$.readAsDataURL(W)}};return s.jsxs("div",{className:"max-w-6xl mx-auto p-6",children:[s.jsx("div",{className:"flex justify-end mb-4",children:s.jsxs("button",{onClick:O,className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center gap-2",children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z",clipRule:"evenodd"})}),"Đăng xuất"]})}),s.jsxs("div",{className:"flex gap-4 mb-6 justify-center",children:[s.jsx("button",{onClick:()=>r("products"),className:`px-6 py-2 rounded-lg ${l==="products"?"bg-blue-600 text-white":"bg-gray-200 hover:bg-gray-300"}`,children:"Quản lý sản phẩm"}),s.jsx("button",{onClick:()=>r("orders"),className:`px-6 py-2 rounded-lg ${l==="orders"?"bg-blue-600 text-white":"bg-gray-200 hover:bg-gray-300"}`,children:"Quản lý đơn hàng"}),s.jsx("button",{onClick:()=>r("wallet"),className:`px-6 py-2 rounded-lg ${l==="wallet"?"bg-blue-600 text-white":"bg-gray-200 hover:bg-gray-300"}`,children:"Quản lý ví"}),s.jsx("button",{onClick:()=>r("support"),className:`px-6 py-2 rounded-lg ${l==="support"?"bg-blue-600 text-white":"bg-gray-200 hover:bg-gray-300"}`,children:"Quản lý hỗ trợ"})]}),l==="products"?s.jsxs(s.Fragment,{children:[s.jsx("h1",{className:"text-3xl font-bold mb-6 text-center",children:" Quản lý sản phẩm"}),s.jsxs("form",{onSubmit:U,className:"bg-white p-6 rounded-lg shadow grid grid-cols-1 md:grid-cols-2 gap-4 mb-8",children:[s.jsx("input",{type:"text",placeholder:"Tên sản phẩm",value:f.title,onChange:C=>d({...f,title:C.target.value}),className:"border border-gray-300 rounded px-4 py-2",required:!0}),s.jsx("select",{value:f.category,onChange:C=>d({...f,category:C.target.value}),className:"border border-gray-300 rounded px-4 py-2",required:!0,children:b.map(C=>s.jsx("option",{value:C.value,children:C.label},C.value))}),s.jsx("input",{type:"text",placeholder:"Giá gốc (VD: 46.637.000)",value:f.originalPrice,onChange:C=>D("originalPrice",C.target.value),className:"border border-gray-300 rounded px-4 py-2",required:!0}),s.jsx("input",{type:"text",placeholder:"Giá bán (VD: 42.690.000)",value:f.price,onChange:C=>D("price",C.target.value),className:"border border-gray-300 rounded px-4 py-2",required:!0}),s.jsx("input",{type:"text",placeholder:"Giảm giá (%)",value:f.discount,onChange:C=>d({...f,discount:C.target.value}),className:"border border-gray-300 rounded px-4 py-2"}),s.jsx("input",{type:"text",placeholder:"Tag",value:f.tag,onChange:C=>d({...f,tag:C.target.value}),className:"border border-gray-300 rounded px-4 py-2"}),s.jsx("input",{type:"file",accept:"image/*",onChange:q,className:"border border-gray-300 rounded px-4 py-2"}),y&&s.jsx("div",{className:"col-span-2",children:s.jsx("img",{src:y,alt:"Preview",className:"w-32 h-32 object-cover rounded border"})}),s.jsx("button",{type:"submit",className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600",children:m?"Cập nhật sản phẩm":"Thêm sản phẩm"})]}),s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:" Danh sách sản phẩm"}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6",children:u.map(C=>s.jsxs("div",{className:"bg-white rounded-lg shadow p-4 flex flex-col items-center text-center",children:[s.jsx("img",{src:C.image,alt:C.title,className:"w-32 h-32 object-cover rounded mb-2 border"}),s.jsx("h3",{className:"text-lg font-semibold",children:C.title}),s.jsxs("p",{className:"text-gray-600",children:["💰 Giá: ",s.jsxs("span",{className:"text-green-600",children:[w(C.price),"₫"]})]}),s.jsxs("p",{className:"text-sm text-gray-500",children:["Giá gốc: ",w(C.originalPrice),"₫"]}),s.jsxs("p",{className:"text-sm text-gray-500",children:["Giảm: ",C.discount,"%"]}),s.jsxs("p",{className:"text-sm text-gray-500",children:["Tag: ",C.tag]}),s.jsxs("div",{className:"mt-3 flex gap-2",children:[s.jsx("button",{onClick:()=>M(C),className:"bg-yellow-400 hover:bg-yellow-500 text-white px-3 py-1 rounded",children:"Sửa"}),s.jsx("button",{onClick:()=>E(C.id),className:"bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded",children:"Xóa"})]})]},C.id))})]}):l==="orders"?s.jsx(m1,{}):l==="wallet"?s.jsx(g1,{}):s.jsx(h1,{})]})},x1=({children:l,adminRequired:r=!1})=>{const[u,c]=N.useState("checking"),[f,d]=N.useState(!1);return N.useEffect(()=>{const m=localStorage.getItem("token"),x=JSON.parse(localStorage.getItem("user")||"{}");m&&m.trim()!==""?(c("authenticated"),d(x.role==="admin")):c("unauthenticated")},[]),u==="checking"?s.jsx("div",{children:"Loading..."}):u==="unauthenticated"?s.jsx(Wh,{to:"/login",replace:!0}):r&&!f?s.jsx(Wh,{to:"/",replace:!0}):l},y1=()=>{const l=Jt(),{cartItems:r,updateQuantity:u,removeFromCart:c,getTotalPrice:f,clearCart:d}=qo();console.log("Cart items:",r),console.log("Total price from context:",f());const m=x=>Math.floor(parseFloat(x)).toString().replace(/\B(?=(\d{3})+(?!\d))/g,".");return s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx(Kt,{}),s.jsxs("div",{className:"max-w-6xl mx-auto px-4 py-8",children:[s.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Giỏ hàng của bạn"}),r.length===0?s.jsxs("div",{className:"text-center py-16",children:[s.jsx("div",{className:"mb-8",children:s.jsx("svg",{className:"w-24 h-24 mx-auto text-gray-300",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V19H17V6H7Z"})})}),s.jsx("h2",{className:"text-2xl font-semibold text-gray-600 mb-4",children:"Giỏ hàng trống"}),s.jsx("p",{className:"text-gray-500 mb-8",children:"Không có sản phẩm nào trong giỏ hàng"}),s.jsx("button",{onClick:()=>l("/home"),className:"bg-blue-500 hover:bg-blue-600 text-white px-8 py-3 rounded-lg font-medium",children:"Về trang chủ"}),s.jsxs("p",{className:"text-sm text-gray-400 mt-4",children:["Bạn cần giúp đỡ? Gọi ",s.jsx("span",{className:"text-blue-500",children:"1900 232 460"})," (08:00 - 21:30)"]})]}):s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[s.jsx("div",{className:"lg:col-span-2",children:s.jsxs("div",{className:"bg-white rounded-lg shadow-sm",children:[s.jsx("div",{className:"p-6 border-b",children:s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsxs("h3",{className:"text-lg font-semibold",children:["Sản phẩm (",r.length,")"]}),s.jsx("button",{onClick:d,className:"text-red-500 hover:text-red-700 text-sm",children:"Xóa tất cả"})]})}),s.jsx("div",{className:"divide-y",children:r.map(x=>s.jsxs("div",{className:"p-6 flex gap-4",children:[s.jsx("img",{src:x.image,alt:x.title,className:"w-20 h-20 object-contain rounded border"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:x.title}),s.jsxs("p",{className:"text-red-600 font-bold text-lg",children:[m(x.price),"₫"]}),x.originalPrice&&s.jsxs("p",{className:"text-gray-400 line-through text-sm",children:[m(x.originalPrice),"₫"]})]}),s.jsxs("div",{className:"flex flex-col items-end gap-3",children:[s.jsx("button",{onClick:()=>c(x.id),className:"text-gray-400 hover:text-red-500",children:s.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})})}),s.jsxs("div",{className:"flex items-center border rounded",children:[s.jsx("button",{onClick:()=>u(x.id,x.quantity-1),className:"w-8 h-8 flex items-center justify-center hover:bg-gray-100",children:"-"}),s.jsx("span",{className:"w-12 text-center",children:x.quantity}),s.jsx("button",{onClick:()=>u(x.id,x.quantity+1),className:"w-8 h-8 flex items-center justify-center hover:bg-gray-100",children:"+"})]})]})]},x.id))})]})}),s.jsx("div",{className:"lg:col-span-1",children:s.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6 sticky top-4",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Tóm tắt đơn hàng"}),s.jsxs("div",{className:"space-y-3 mb-4",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Tạm tính:"}),s.jsxs("span",{children:[m(f()),"₫"]})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Phí vận chuyển:"}),s.jsx("span",{className:"text-green-600",children:"Miễn phí"})]}),s.jsx("hr",{}),s.jsxs("div",{className:"flex justify-between text-lg font-bold",children:[s.jsx("span",{children:"Tổng cộng:"}),s.jsxs("span",{className:"text-red-600",children:[m(f()),"₫"]})]})]}),s.jsx("button",{onClick:()=>l("/cartpay",{state:{cartItems:r,totalPrice:f(),isMultipleItems:!0}}),className:"w-full bg-[#ffd400] hover:bg-yellow-500 text-black font-medium py-3 rounded-lg mb-3",children:"Thanh toán"}),s.jsx("button",{onClick:()=>l("/home"),className:"w-full border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium py-3 rounded-lg",children:"Tiếp tục mua sắm"})]})})]})]}),s.jsx(jt,{})]})},Sm=l=>Math.floor(parseFloat(l)).toString().replace(/\B(?=(\d{3})+(?!\d))/g,"."),v1=()=>{const{id:l}=Ax(),[r,u]=N.useState(null),[c,f]=N.useState(!0),[d,m]=N.useState(""),{addToCart:x}=qo(),y=()=>{r&&(x(r),alert("Đã thêm sản phẩm vào giỏ hàng!"))};return N.useEffect(()=>{(async()=>{try{const v=await ue.get(`http://localhost:5000/api/products/${l}`);u(v.data)}catch{m("Không tìm thấy sản phẩm.")}finally{f(!1)}})()},[l]),c?s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx(Kt,{}),s.jsx(cl,{}),s.jsx("div",{className:"p-6",children:"Đang tải..."}),s.jsx(jt,{})]}):d?s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx(Kt,{}),s.jsx("div",{className:"p-6 text-red-500",children:d}),s.jsx(jt,{})]}):r?s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx(Kt,{}),s.jsx("section",{className:"py-8 bg-white",children:s.jsx("div",{className:"max-w-screen-xl px-4 mx-auto",children:s.jsxs("div",{className:"lg:grid lg:grid-cols-2 lg:gap-8 xl:gap-16",children:[s.jsx("div",{className:"shrink-0 max-w-md lg:max-w-lg mx-auto",children:s.jsx("img",{src:r.image,alt:r.title,className:"w-full max-w-md rounded-lg shadow-md mb-4"})}),s.jsxs("div",{className:"mt-6 sm:mt-8 lg:mt-0",children:[s.jsx("h1",{className:"text-xl font-semibold text-gray-900 sm:text-2xl",children:r.title}),s.jsxs("div",{className:"mt-4 sm:items-center sm:gap-4 sm:flex",children:[s.jsxs("p",{className:"text-2xl font-extrabold text-red-600 sm:text-3xl",children:[Sm(r.price),"₫"]}),s.jsxs("p",{className:"text-gray-500 line-through mb-1",children:[Sm(r.originalPrice),"₫"]}),s.jsxs("div",{className:"flex items-center gap-2 mt-2 sm:mt-0",children:[s.jsx("div",{className:"flex items-center gap-1",children:[...Array(5)].map((p,v)=>s.jsx("svg",{className:"w-4 h-4 text-yellow-300",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M13.849 4.22c-.684-1.626-3.014-1.626-3.698 0L8.397 8.387l-4.552.361c-1.775.14-2.495 2.331-1.142 3.477l3.468 2.937-1.06 4.392c-.413 1.713 1.472 3.067 2.992 2.149L12 19.35l3.897 2.354c1.52.918 3.405-.436 2.992-2.15l-1.06-4.39 3.468-2.938c1.353-1.146.633-3.336-1.142-3.477l-4.552-.36-1.754-4.17Z"})},v))}),s.jsx("p",{className:"text-sm font-medium text-gray-600",children:"(5.0)"}),s.jsx("a",{href:"#",className:"text-sm font-medium text-blue-600 underline hover:no-underline",children:"345 Reviews"})]})]}),s.jsxs("div",{className:"mt-6 sm:gap-4 sm:items-center sm:flex sm:mt-8",children:[s.jsxs("button",{className:"flex items-center justify-center py-2.5 px-5 text-sm font-medium text-gray-900 bg-white rounded-lg border border-gray-200 hover:bg-gray-100 focus:ring-4 focus:ring-gray-100",children:[s.jsx("svg",{className:"w-5 h-5 -ms-2 me-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z"})}),"Yêu thích"]}),s.jsxs("button",{onClick:y,className:"text-white mt-4 sm:mt-0 bg-[#ffd400] hover:bg-yellow-500 focus:ring-4 focus:ring-yellow-300 font-medium rounded-lg text-sm px-5 py-2.5 flex items-center justify-center",children:[s.jsx("svg",{className:"w-5 h-5 -ms-2 me-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 4h1.5L8 16m0 0h8m-8 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4Zm8 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4Zm.75-3H7.5M11 7H6.312M17 4v6m-3-3h6"})}),"Thêm vào giỏ"]})]}),s.jsx("hr",{className:"my-6 md:my-8 border-gray-200"}),s.jsx("p",{className:"mb-6 text-gray-600",children:r.tag}),s.jsx("p",{className:"text-gray-700",children:"Hãy mua ngay chúng tôi luôn bán những sản phẩm tốt nhất trong thị trường hiện nay"})]})]})})}),s.jsx(jt,{})]}):null};function b1(l){return nt({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"22",y1:"2",x2:"11",y2:"13"},child:[]},{tag:"polygon",attr:{points:"22 2 15 22 11 13 2 9 22 2"},child:[]}]})(l)}const j1=()=>{const[l,r]=N.useState(!1),[u,c]=N.useState(""),[f,d]=N.useState([]),m=()=>r(!l),x=async()=>{if(!u.trim())return;const v=[...f,{text:u,isUser:!0}];d(v);try{const b=await ue.post("http://localhost:5000/api/chatbot/chat",{prompt:u});d([...v,{text:b.data.text,isUser:!1}]),c("")}catch(b){console.error("Error:",b)}},y=v=>{v.key==="Enter"&&x()},p=({text:v})=>v.includes("/assets/")?s.jsxs(s.Fragment,{children:[s.jsx("div",{children:v.split("/assets/")[0]}),s.jsx("img",{src:`/assets/${v.split("/assets/")[1]}`,alt:"Product",className:"w-full h-auto rounded-lg mt-2"})]}):s.jsx("div",{children:v});return s.jsxs("div",{children:[s.jsx("button",{onClick:m,className:"fixed bottom-6 right-6 w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white flex items-center justify-center text-3xl shadow-xl hover:scale-110 hover:shadow-2xl transition-all duration-300 ease-in-out",children:"🗯️"}),l&&s.jsxs("div",{className:"fixed bottom-24 right-6 w-80 bg-white rounded-2xl shadow-2xl flex flex-col overflow-hidden z-50 border border-gray-200",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 bg-blue-600 text-white",children:[s.jsx("h3",{className:"text-lg font-bold",children:"ChatBot"}),s.jsx("button",{onClick:m,className:"text-2xl hover:text-gray-300",children:s.jsx(My,{})})]}),s.jsx("div",{className:"flex-1 overflow-y-auto p-4 space-y-2 bg-gray-50 h-80 max-h-80",children:f.map((v,b)=>s.jsx("div",{className:`flex ${v.isUser?"justify-end":"justify-start"}`,children:s.jsx("div",{className:`max-w-[70%] p-3 rounded-2xl text-sm shadow
                                    ${v.isUser?"bg-blue-500 text-white rounded-br-none":"bg-white text-gray-800 rounded-bl-none"}`,children:s.jsx(p,{text:v.text})})},b))}),s.jsxs("div",{className:"flex items-center border-t p-3 bg-white",children:[s.jsx("input",{type:"text",value:u,onChange:v=>c(v.target.value),onKeyDown:y,placeholder:"Nhập tin nhắn...",className:"flex-1 p-2 rounded-full border border-gray-300 focus:ring-2 focus:ring-blue-500 outline-none mr-2"}),s.jsx("button",{onClick:x,className:"p-2 rounded-full bg-green-500 text-white hover:bg-green-600 transition",children:s.jsx(b1,{})})]})]})]})},N1=()=>{var $;const l=rl(),{cartItems:r,totalPrice:u,isMultipleItems:c,...f}=l.state||{},[d,m]=N.useState(""),[x,y]=N.useState(""),[p,v]=N.useState(""),[b,O]=N.useState((f==null?void 0:f.userAddress)||""),[H,U]=N.useState(!1),[E,M]=N.useState("cod"),[R,D]=N.useState(null);N.useEffect(()=>{const K=localStorage.getItem("userAddress");K&&O(K),w()},[]);const w=async()=>{try{const K=localStorage.getItem("token");if(K){const oe=await ue.get("http://localhost:5000/api/wallet",{headers:{Authorization:`Bearer ${K}`}});D(oe.data.wallet)}}catch(K){console.error("Lỗi lấy thông tin ví:",K)}},q=K=>Math.floor(parseFloat(K)).toString().replace(/\B(?=(\d{3})+(?!\d))/g,"."),C=()=>c?u:f==null?void 0:f.price,W=async()=>{var Ce;if(!d||!p||!b||!x){alert("Vui lòng điền đầy đủ thông tin.");return}if(!/^[0-9]{10}$/.test(p)){alert("Số điện thoại không hợp lệ.");return}if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(x)){alert("Email không hợp lệ.");return}if(E==="wallet"){const be=C();if(!R||R.balance<be){alert("Số dư ví không đủ để thanh toán. Vui lòng nạp thêm tiền hoặc chọn thanh toán khi nhận hàng.");return}}U(!0);try{const be=localStorage.getItem("token");if(c)for(const ie of r)await ue.post("http://localhost:5000/api/orders",{fullName:d,email:x,phone:p,address:b,productId:ie.id,productTitle:ie.title,productPrice:ie.price,quantity:ie.quantity,paymentMethod:E},{headers:{Authorization:`Bearer ${be}`}});else await ue.post("http://localhost:5000/api/orders",{fullName:d,email:x,phone:p,address:b,productId:f.id,productTitle:f.title,productPrice:f.price,paymentMethod:E},{headers:{Authorization:`Bearer ${be}`}});alert(E==="wallet"?"Đặt hàng và thanh toán thành công! Số tiền đã được trừ từ ví.":"Đặt hàng thành công! Bạn sẽ thanh toán khi nhận hàng."),m(""),y(""),v(""),O(""),E==="wallet"&&w()}catch(be){console.error("Chi tiết lỗi:",(Ce=be.response)==null?void 0:Ce.data),alert("Đặt hàng thất bại. Vui lòng thử lại.")}finally{U(!1)}};return c&&(`${r==null?void 0:r.length}`,($=r==null?void 0:r[0])==null||$.image),s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx(Kt,{}),s.jsxs("div",{className:"p-4 max-w-6xl mx-auto",children:[s.jsx("h1",{className:"text-3xl font-bold mb-6 text-center",children:"Thanh Toán Đơn Hàng"}),s.jsxs("div",{className:"flex gap-12",children:[s.jsx("div",{className:"w-1/2",children:c?s.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[s.jsxs("h3",{className:"text-xl font-semibold mb-4",children:["Danh sách sản phẩm (",r==null?void 0:r.length,")"]}),s.jsx("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:r==null?void 0:r.map(K=>s.jsxs("div",{className:"flex gap-4 p-4 border rounded-lg",children:[s.jsx("img",{src:K.image,alt:K.title,className:"w-20 h-20 object-contain rounded"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-medium text-sm",children:K.title}),s.jsxs("p",{className:"text-red-600 font-bold",children:[q(K.price),"₫"]}),s.jsxs("p",{className:"text-gray-600 text-sm",children:["Số lượng: ",K.quantity]})]})]},K.id))}),s.jsx("div",{className:"mt-4 pt-4 border-t",children:s.jsxs("div",{className:"flex justify-between text-xl font-bold",children:[s.jsx("span",{children:"Tổng cộng:"}),s.jsxs("span",{className:"text-red-600",children:[q(u),"₫"]})]})})]}):s.jsx("img",{src:f==null?void 0:f.image,alt:f==null?void 0:f.title,className:"w-full rounded-lg shadow-lg"})}),s.jsxs("div",{className:"w-1/2 bg-white p-6 rounded-lg shadow-md",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:c?`Đơn hàng (${r==null?void 0:r.length} sản phẩm)`:f==null?void 0:f.title}),!c&&s.jsx("div",{className:"flex items-center gap-4 mb-4",children:s.jsxs("span",{className:"text-red-500 text-2xl font-bold",children:[q(f==null?void 0:f.price),"₫"]})}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-lg font-medium mb-2",children:"Họ và tên"}),s.jsx("input",{type:"text",className:"w-full p-3 border rounded-md shadow-sm",value:d,onChange:K=>m(K.target.value)})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-lg font-medium mb-2",children:"Email"}),s.jsx("input",{type:"email",className:"w-full p-3 border rounded-md shadow-sm",value:x,onChange:K=>y(K.target.value)})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-lg font-medium mb-2",children:"Số điện thoại"}),s.jsx("input",{type:"text",className:"w-full p-3 border rounded-md shadow-sm",value:p,onChange:K=>v(K.target.value)})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-lg font-medium mb-2",children:"Địa chỉ nhận hàng"}),s.jsx("input",{type:"text",className:"w-full p-3 border rounded-md shadow-sm",value:b,onChange:K=>O(K.target.value)})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-lg font-medium mb-4",children:"Phương thức thanh toán"}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("div",{className:`border-2 rounded-lg p-4 cursor-pointer transition-all ${E==="cod"?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>M("cod"),children:s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("input",{type:"radio",name:"paymentMethod",value:"cod",checked:E==="cod",onChange:()=>M("cod"),className:"w-4 h-4 text-blue-600"}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center",children:s.jsx("span",{className:"text-green-600 text-lg",children:"💵"})}),s.jsxs("div",{children:[s.jsx("p",{className:"font-semibold text-gray-800",children:"Thanh toán khi nhận hàng"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Thanh toán bằng tiền mặt khi nhận được sản phẩm"})]})]})]})}),s.jsx("div",{className:`border-2 rounded-lg p-4 cursor-pointer transition-all ${E==="wallet"?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>M("wallet"),children:s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("input",{type:"radio",name:"paymentMethod",value:"wallet",checked:E==="wallet",onChange:()=>M("wallet"),className:"w-4 h-4 text-purple-600"}),s.jsxs("div",{className:"flex items-center gap-3 flex-1",children:[s.jsx("div",{className:"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center",children:s.jsx("span",{className:"text-purple-600 text-lg",children:"💳"})}),s.jsxs("div",{className:"flex-1",children:[s.jsx("p",{className:"font-semibold text-gray-800",children:"Thanh toán bằng ví điện tử"}),s.jsxs("p",{className:"text-sm text-gray-600",children:["Số dư hiện tại: ",s.jsxs("span",{className:"font-semibold text-purple-600",children:[q((R==null?void 0:R.balance)||0),"₫"]})]})]}),R&&R.balance<C()&&s.jsx("div",{className:"text-red-500 text-sm font-medium",children:"Không đủ số dư"})]})]})})]})]}),s.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-lg font-semibold",children:"Tổng thanh toán:"}),s.jsxs("span",{className:"text-2xl font-bold text-red-600",children:[q(C()),"₫"]})]}),E==="wallet"&&R&&R.balance>=C()&&s.jsxs("div",{className:"mt-2 text-sm text-gray-600",children:["Số dư còn lại sau thanh toán: ",s.jsxs("span",{className:"font-semibold text-green-600",children:[q(R.balance-C()),"₫"]})]})]}),s.jsx("button",{onClick:W,className:`w-full ${H||E==="wallet"&&R&&R.balance<C()?"bg-gray-400 cursor-not-allowed":"bg-red-500 hover:bg-red-600"} text-white py-3 rounded-md text-lg transition-colors`,disabled:H||E==="wallet"&&R&&R.balance<C(),children:H?"Đang xử lý...":"Xác Nhận Đặt Hàng"})]})]})]})]}),s.jsx(jt,{})]})};function S1(){Jt();const[l,r]=N.useState({name:"",email:"",topic:"Giao hàng",message:""}),[u,c]=N.useState(!1),[f,d]=N.useState(""),m=y=>{r({...l,[y.target.name]:y.target.value})},x=async y=>{y.preventDefault(),c(!1),d(""),console.log(" Gửi yêu cầu:",l);try{const p=await fetch("http://localhost:5000/api/support",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)}),v=await p.json();console.log(" Phản hồi từ server:",v),p.ok?(c(!0),r({name:"",email:"",topic:"Giao hàng",message:""})):d(v.error||"Gửi thất bại. Vui lòng thử lại.")}catch(p){console.error("❌ Lỗi:",p),d("Không thể kết nối đến server.")}};return s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx(Kt,{}),s.jsx(cl,{}),s.jsx("div",{className:"max-w-2xl mx-auto pt-10 pb-10 px-6",children:s.jsxs("div",{className:"bg-white shadow-md rounded-xl p-6 relative",children:[s.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Liên hệ hỗ trợ"}),s.jsx("p",{className:"mb-6 text-gray-600",children:"Bạn gặp vấn đề? Hãy gửi thông tin cho chúng tôi để được hỗ trợ nhanh nhất."}),u&&s.jsx("div",{className:"mb-4 text-green-600 font-semibold",children:"Gửi thành công! Chúng tôi sẽ phản hồi sớm nhất."}),f&&s.jsx("div",{className:"mb-4 text-red-600 font-semibold",children:f}),s.jsxs("form",{onSubmit:x,className:"space-y-4",children:[s.jsx("input",{type:"text",name:"name",placeholder:"Họ và tên",value:l.name,onChange:m,required:!0,className:"w-full border border-gray-300 p-2 rounded-md"}),s.jsx("input",{type:"email",name:"email",placeholder:"Email",value:l.email,onChange:m,required:!0,className:"w-full border border-gray-300 p-2 rounded-md"}),s.jsxs("select",{name:"topic",value:l.topic,onChange:m,className:"w-full border border-gray-300 p-2 rounded-md",children:[s.jsx("option",{children:"Giao hàng"}),s.jsx("option",{children:"Thanh toán"}),s.jsx("option",{children:"Sản phẩm"}),s.jsx("option",{children:"Khác"})]}),s.jsx("textarea",{name:"message",placeholder:"Mô tả vấn đề bạn gặp phải...",value:l.message,onChange:m,required:!0,className:"w-full border border-gray-300 p-2 rounded-md h-32"}),s.jsx("button",{type:"submit",className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"Gửi hỗ trợ"})]})]})}),s.jsx(jt,{})]})}function w1(){const[l,r]=N.useState(0),u=[{id:1,image:"https://cdnv2.tgdd.vn/mwg-static/tgdd/Banner/63/d9/63d9fe3bed63b3069180b3ea0a373dfb.png",alt:"Promo Banner 1"},{id:2,image:"https://cdnv2.tgdd.vn/mwg-static/tgdd/Banner/9d/9b/9d9b6ba919e45d8e3f48700d86703135.png",alt:"Promo Banner 2"},{id:3,image:"https://cdnv2.tgdd.vn/mwg-static/tgdd/Banner/39/98/399816da9baf6a03ab4a53baf21d0596.png",alt:"Promo Banner 3"}];return N.useEffect(()=>{const c=setInterval(()=>{r(f=>(f+1)%u.length)},3e3);return()=>clearInterval(c)},[u.length]),s.jsx("div",{className:"w-full bg-white py-4",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4",children:s.jsxs("div",{className:"relative overflow-hidden rounded-lg",children:[s.jsx("div",{className:"flex transition-transform duration-500 ease-in-out",style:{transform:`translateX(-${l*100}%)`},children:u.map(c=>s.jsx("div",{className:"w-full flex-shrink-0",children:s.jsx("img",{src:c.image,alt:c.alt,className:"w-full h-56 object-fill"})},c.id))}),s.jsx("div",{className:"absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-2",children:u.map((c,f)=>s.jsx("button",{onClick:()=>r(f),className:`w-2 h-2 rounded-full ${l===f?"bg-white":"bg-white/50"}`},f))})]})})})}function x0(l){var r,u,c="";if(typeof l=="string"||typeof l=="number")c+=l;else if(typeof l=="object")if(Array.isArray(l)){var f=l.length;for(r=0;r<f;r++)l[r]&&(u=x0(l[r]))&&(c&&(c+=" "),c+=u)}else for(u in l)l[u]&&(c&&(c+=" "),c+=u);return c}function pn(){for(var l,r,u=0,c="",f=arguments.length;u<f;u++)(l=arguments[u])&&(r=x0(l))&&(c&&(c+=" "),c+=r);return c}function T1(l){if(typeof document>"u")return;let r=document.head||document.getElementsByTagName("head")[0],u=document.createElement("style");u.type="text/css",r.firstChild?r.insertBefore(u,r.firstChild):r.appendChild(u),u.styleSheet?u.styleSheet.cssText=l:u.appendChild(document.createTextNode(l))}T1(`:root{--toastify-color-light: #fff;--toastify-color-dark: #121212;--toastify-color-info: #3498db;--toastify-color-success: #07bc0c;--toastify-color-warning: #f1c40f;--toastify-color-error: hsl(6, 78%, 57%);--toastify-color-transparent: rgba(255, 255, 255, .7);--toastify-icon-color-info: var(--toastify-color-info);--toastify-icon-color-success: var(--toastify-color-success);--toastify-icon-color-warning: var(--toastify-color-warning);--toastify-icon-color-error: var(--toastify-color-error);--toastify-container-width: fit-content;--toastify-toast-width: 320px;--toastify-toast-offset: 16px;--toastify-toast-top: max(var(--toastify-toast-offset), env(safe-area-inset-top));--toastify-toast-right: max(var(--toastify-toast-offset), env(safe-area-inset-right));--toastify-toast-left: max(var(--toastify-toast-offset), env(safe-area-inset-left));--toastify-toast-bottom: max(var(--toastify-toast-offset), env(safe-area-inset-bottom));--toastify-toast-background: #fff;--toastify-toast-padding: 14px;--toastify-toast-min-height: 64px;--toastify-toast-max-height: 800px;--toastify-toast-bd-radius: 6px;--toastify-toast-shadow: 0px 4px 12px rgba(0, 0, 0, .1);--toastify-font-family: sans-serif;--toastify-z-index: 9999;--toastify-text-color-light: #757575;--toastify-text-color-dark: #fff;--toastify-text-color-info: #fff;--toastify-text-color-success: #fff;--toastify-text-color-warning: #fff;--toastify-text-color-error: #fff;--toastify-spinner-color: #616161;--toastify-spinner-color-empty-area: #e0e0e0;--toastify-color-progress-light: linear-gradient(to right, #4cd964, #5ac8fa, #007aff, #34aadc, #5856d6, #ff2d55);--toastify-color-progress-dark: #bb86fc;--toastify-color-progress-info: var(--toastify-color-info);--toastify-color-progress-success: var(--toastify-color-success);--toastify-color-progress-warning: var(--toastify-color-warning);--toastify-color-progress-error: var(--toastify-color-error);--toastify-color-progress-bgo: .2}.Toastify__toast-container{z-index:var(--toastify-z-index);-webkit-transform:translate3d(0,0,var(--toastify-z-index));position:fixed;width:var(--toastify-container-width);box-sizing:border-box;color:#fff;display:flex;flex-direction:column}.Toastify__toast-container--top-left{top:var(--toastify-toast-top);left:var(--toastify-toast-left)}.Toastify__toast-container--top-center{top:var(--toastify-toast-top);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--top-right{top:var(--toastify-toast-top);right:var(--toastify-toast-right);align-items:end}.Toastify__toast-container--bottom-left{bottom:var(--toastify-toast-bottom);left:var(--toastify-toast-left)}.Toastify__toast-container--bottom-center{bottom:var(--toastify-toast-bottom);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--bottom-right{bottom:var(--toastify-toast-bottom);right:var(--toastify-toast-right);align-items:end}.Toastify__toast{--y: 0;position:relative;touch-action:none;width:var(--toastify-toast-width);min-height:var(--toastify-toast-min-height);box-sizing:border-box;margin-bottom:1rem;padding:var(--toastify-toast-padding);border-radius:var(--toastify-toast-bd-radius);box-shadow:var(--toastify-toast-shadow);max-height:var(--toastify-toast-max-height);font-family:var(--toastify-font-family);z-index:0;display:flex;flex:1 auto;align-items:center;word-break:break-word}@media only screen and (max-width: 480px){.Toastify__toast-container{width:100vw;left:env(safe-area-inset-left);margin:0}.Toastify__toast-container--top-left,.Toastify__toast-container--top-center,.Toastify__toast-container--top-right{top:env(safe-area-inset-top);transform:translate(0)}.Toastify__toast-container--bottom-left,.Toastify__toast-container--bottom-center,.Toastify__toast-container--bottom-right{bottom:env(safe-area-inset-bottom);transform:translate(0)}.Toastify__toast-container--rtl{right:env(safe-area-inset-right);left:initial}.Toastify__toast{--toastify-toast-width: 100%;margin-bottom:0;border-radius:0}}.Toastify__toast-container[data-stacked=true]{width:var(--toastify-toast-width)}.Toastify__toast--stacked{position:absolute;width:100%;transform:translate3d(0,var(--y),0) scale(var(--s));transition:transform .3s}.Toastify__toast--stacked[data-collapsed] .Toastify__toast-body,.Toastify__toast--stacked[data-collapsed] .Toastify__close-button{transition:opacity .1s}.Toastify__toast--stacked[data-collapsed=false]{overflow:visible}.Toastify__toast--stacked[data-collapsed=true]:not(:last-child)>*{opacity:0}.Toastify__toast--stacked:after{content:"";position:absolute;left:0;right:0;height:calc(var(--g) * 1px);bottom:100%}.Toastify__toast--stacked[data-pos=top]{top:0}.Toastify__toast--stacked[data-pos=bot]{bottom:0}.Toastify__toast--stacked[data-pos=bot].Toastify__toast--stacked:before{transform-origin:top}.Toastify__toast--stacked[data-pos=top].Toastify__toast--stacked:before{transform-origin:bottom}.Toastify__toast--stacked:before{content:"";position:absolute;left:0;right:0;bottom:0;height:100%;transform:scaleY(3);z-index:-1}.Toastify__toast--rtl{direction:rtl}.Toastify__toast--close-on-click{cursor:pointer}.Toastify__toast-icon{margin-inline-end:10px;width:22px;flex-shrink:0;display:flex}.Toastify--animate{animation-fill-mode:both;animation-duration:.5s}.Toastify--animate-icon{animation-fill-mode:both;animation-duration:.3s}.Toastify__toast-theme--dark{background:var(--toastify-color-dark);color:var(--toastify-text-color-dark)}.Toastify__toast-theme--light,.Toastify__toast-theme--colored.Toastify__toast--default{background:var(--toastify-color-light);color:var(--toastify-text-color-light)}.Toastify__toast-theme--colored.Toastify__toast--info{color:var(--toastify-text-color-info);background:var(--toastify-color-info)}.Toastify__toast-theme--colored.Toastify__toast--success{color:var(--toastify-text-color-success);background:var(--toastify-color-success)}.Toastify__toast-theme--colored.Toastify__toast--warning{color:var(--toastify-text-color-warning);background:var(--toastify-color-warning)}.Toastify__toast-theme--colored.Toastify__toast--error{color:var(--toastify-text-color-error);background:var(--toastify-color-error)}.Toastify__progress-bar-theme--light{background:var(--toastify-color-progress-light)}.Toastify__progress-bar-theme--dark{background:var(--toastify-color-progress-dark)}.Toastify__progress-bar--info{background:var(--toastify-color-progress-info)}.Toastify__progress-bar--success{background:var(--toastify-color-progress-success)}.Toastify__progress-bar--warning{background:var(--toastify-color-progress-warning)}.Toastify__progress-bar--error{background:var(--toastify-color-progress-error)}.Toastify__progress-bar-theme--colored.Toastify__progress-bar--info,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--success,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--warning,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--error{background:var(--toastify-color-transparent)}.Toastify__close-button{color:#fff;position:absolute;top:6px;right:6px;background:transparent;outline:none;border:none;padding:0;cursor:pointer;opacity:.7;transition:.3s ease;z-index:1}.Toastify__toast--rtl .Toastify__close-button{left:6px;right:unset}.Toastify__close-button--light{color:#000;opacity:.3}.Toastify__close-button>svg{fill:currentColor;height:16px;width:14px}.Toastify__close-button:hover,.Toastify__close-button:focus{opacity:1}@keyframes Toastify__trackProgress{0%{transform:scaleX(1)}to{transform:scaleX(0)}}.Toastify__progress-bar{position:absolute;bottom:0;left:0;width:100%;height:100%;z-index:1;opacity:.7;transform-origin:left}.Toastify__progress-bar--animated{animation:Toastify__trackProgress linear 1 forwards}.Toastify__progress-bar--controlled{transition:transform .2s}.Toastify__progress-bar--rtl{right:0;left:initial;transform-origin:right;border-bottom-left-radius:initial}.Toastify__progress-bar--wrp{position:absolute;overflow:hidden;bottom:0;left:0;width:100%;height:5px;border-bottom-left-radius:var(--toastify-toast-bd-radius);border-bottom-right-radius:var(--toastify-toast-bd-radius)}.Toastify__progress-bar--wrp[data-hidden=true]{opacity:0}.Toastify__progress-bar--bg{opacity:var(--toastify-color-progress-bgo);width:100%;height:100%}.Toastify__spinner{width:20px;height:20px;box-sizing:border-box;border:2px solid;border-radius:100%;border-color:var(--toastify-spinner-color-empty-area);border-right-color:var(--toastify-spinner-color);animation:Toastify__spin .65s linear infinite}@keyframes Toastify__bounceInRight{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(3000px,0,0)}60%{opacity:1;transform:translate3d(-25px,0,0)}75%{transform:translate3d(10px,0,0)}90%{transform:translate3d(-5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutRight{20%{opacity:1;transform:translate3d(-20px,var(--y),0)}to{opacity:0;transform:translate3d(2000px,var(--y),0)}}@keyframes Toastify__bounceInLeft{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(-3000px,0,0)}60%{opacity:1;transform:translate3d(25px,0,0)}75%{transform:translate3d(-10px,0,0)}90%{transform:translate3d(5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutLeft{20%{opacity:1;transform:translate3d(20px,var(--y),0)}to{opacity:0;transform:translate3d(-2000px,var(--y),0)}}@keyframes Toastify__bounceInUp{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,3000px,0)}60%{opacity:1;transform:translate3d(0,-20px,0)}75%{transform:translate3d(0,10px,0)}90%{transform:translate3d(0,-5px,0)}to{transform:translateZ(0)}}@keyframes Toastify__bounceOutUp{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,-2000px,0)}}@keyframes Toastify__bounceInDown{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,-3000px,0)}60%{opacity:1;transform:translate3d(0,25px,0)}75%{transform:translate3d(0,-10px,0)}90%{transform:translate3d(0,5px,0)}to{transform:none}}@keyframes Toastify__bounceOutDown{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,2000px,0)}}.Toastify__bounce-enter--top-left,.Toastify__bounce-enter--bottom-left{animation-name:Toastify__bounceInLeft}.Toastify__bounce-enter--top-right,.Toastify__bounce-enter--bottom-right{animation-name:Toastify__bounceInRight}.Toastify__bounce-enter--top-center{animation-name:Toastify__bounceInDown}.Toastify__bounce-enter--bottom-center{animation-name:Toastify__bounceInUp}.Toastify__bounce-exit--top-left,.Toastify__bounce-exit--bottom-left{animation-name:Toastify__bounceOutLeft}.Toastify__bounce-exit--top-right,.Toastify__bounce-exit--bottom-right{animation-name:Toastify__bounceOutRight}.Toastify__bounce-exit--top-center{animation-name:Toastify__bounceOutUp}.Toastify__bounce-exit--bottom-center{animation-name:Toastify__bounceOutDown}@keyframes Toastify__zoomIn{0%{opacity:0;transform:scale3d(.3,.3,.3)}50%{opacity:1}}@keyframes Toastify__zoomOut{0%{opacity:1}50%{opacity:0;transform:translate3d(0,var(--y),0) scale3d(.3,.3,.3)}to{opacity:0}}.Toastify__zoom-enter{animation-name:Toastify__zoomIn}.Toastify__zoom-exit{animation-name:Toastify__zoomOut}@keyframes Toastify__flipIn{0%{transform:perspective(400px) rotateX(90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotateX(-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotateX(10deg);opacity:1}80%{transform:perspective(400px) rotateX(-5deg)}to{transform:perspective(400px)}}@keyframes Toastify__flipOut{0%{transform:translate3d(0,var(--y),0) perspective(400px)}30%{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(-20deg);opacity:1}to{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(90deg);opacity:0}}.Toastify__flip-enter{animation-name:Toastify__flipIn}.Toastify__flip-exit{animation-name:Toastify__flipOut}@keyframes Toastify__slideInRight{0%{transform:translate3d(110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInLeft{0%{transform:translate3d(-110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInUp{0%{transform:translate3d(0,110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInDown{0%{transform:translate3d(0,-110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideOutRight{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(110%,var(--y),0)}}@keyframes Toastify__slideOutLeft{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(-110%,var(--y),0)}}@keyframes Toastify__slideOutDown{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,500px,0)}}@keyframes Toastify__slideOutUp{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,-500px,0)}}.Toastify__slide-enter--top-left,.Toastify__slide-enter--bottom-left{animation-name:Toastify__slideInLeft}.Toastify__slide-enter--top-right,.Toastify__slide-enter--bottom-right{animation-name:Toastify__slideInRight}.Toastify__slide-enter--top-center{animation-name:Toastify__slideInDown}.Toastify__slide-enter--bottom-center{animation-name:Toastify__slideInUp}.Toastify__slide-exit--top-left,.Toastify__slide-exit--bottom-left{animation-name:Toastify__slideOutLeft;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-right,.Toastify__slide-exit--bottom-right{animation-name:Toastify__slideOutRight;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-center{animation-name:Toastify__slideOutUp;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--bottom-center{animation-name:Toastify__slideOutDown;animation-timing-function:ease-in;animation-duration:.3s}@keyframes Toastify__spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}
`);var ys=l=>typeof l=="number"&&!isNaN(l),yn=l=>typeof l=="string",ba=l=>typeof l=="function",E1=l=>yn(l)||ys(l),Ro=l=>yn(l)||ba(l)?l:null,_1=(l,r)=>l===!1||ys(l)&&l>0?l:r,Mo=l=>N.isValidElement(l)||yn(l)||ba(l)||ys(l);function O1(l,r,u=300){let{scrollHeight:c,style:f}=l;requestAnimationFrame(()=>{f.minHeight="initial",f.height=c+"px",f.transition=`all ${u}ms`,requestAnimationFrame(()=>{f.height="0",f.padding="0",f.margin="0",setTimeout(r,u)})})}function C1({enter:l,exit:r,appendPosition:u=!1,collapse:c=!0,collapseDuration:f=300}){return function({children:d,position:m,preventExitTransition:x,done:y,nodeRef:p,isIn:v,playToast:b}){let O=u?`${l}--${m}`:l,H=u?`${r}--${m}`:r,U=N.useRef(0);return N.useLayoutEffect(()=>{let E=p.current,M=O.split(" "),R=D=>{D.target===p.current&&(b(),E.removeEventListener("animationend",R),E.removeEventListener("animationcancel",R),U.current===0&&D.type!=="animationcancel"&&E.classList.remove(...M))};E.classList.add(...M),E.addEventListener("animationend",R),E.addEventListener("animationcancel",R)},[]),N.useEffect(()=>{let E=p.current,M=()=>{E.removeEventListener("animationend",M),c?O1(E,y,f):y()};v||(x?M():(U.current=1,E.className+=` ${H}`,E.addEventListener("animationend",M)))},[v]),me.createElement(me.Fragment,null,d)}}function wm(l,r){return{content:y0(l.content,l.props),containerId:l.props.containerId,id:l.props.toastId,theme:l.props.theme,type:l.props.type,data:l.props.data||{},isLoading:l.props.isLoading,icon:l.props.icon,reason:l.removalReason,status:r}}function y0(l,r,u=!1){return N.isValidElement(l)&&!yn(l.type)?N.cloneElement(l,{closeToast:r.closeToast,toastProps:r,data:r.data,isPaused:u}):ba(l)?l({closeToast:r.closeToast,toastProps:r,data:r.data,isPaused:u}):l}function A1({closeToast:l,theme:r,ariaLabel:u="close"}){return me.createElement("button",{className:`Toastify__close-button Toastify__close-button--${r}`,type:"button",onClick:c=>{c.stopPropagation(),l(!0)},"aria-label":u},me.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},me.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}function R1({delay:l,isRunning:r,closeToast:u,type:c="default",hide:f,className:d,controlledProgress:m,progress:x,rtl:y,isIn:p,theme:v}){let b=f||m&&x===0,O={animationDuration:`${l}ms`,animationPlayState:r?"running":"paused"};m&&(O.transform=`scaleX(${x})`);let H=pn("Toastify__progress-bar",m?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${v}`,`Toastify__progress-bar--${c}`,{"Toastify__progress-bar--rtl":y}),U=ba(d)?d({rtl:y,type:c,defaultClassName:H}):pn(H,d),E={[m&&x>=1?"onTransitionEnd":"onAnimationEnd"]:m&&x<1?null:()=>{p&&u()}};return me.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":b},me.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${v} Toastify__progress-bar--${c}`}),me.createElement("div",{role:"progressbar","aria-hidden":b?"true":"false","aria-label":"notification timer",className:U,style:O,...E}))}var M1=1,v0=()=>`${M1++}`;function z1(l,r,u){let c=1,f=0,d=[],m=[],x=r,y=new Map,p=new Set,v=D=>(p.add(D),()=>p.delete(D)),b=()=>{m=Array.from(y.values()),p.forEach(D=>D())},O=({containerId:D,toastId:w,updateId:q})=>{let C=D?D!==l:l!==1,W=y.has(w)&&q==null;return C||W},H=(D,w)=>{y.forEach(q=>{var C;(w==null||w===q.props.toastId)&&((C=q.toggle)==null||C.call(q,D))})},U=D=>{var w,q;(q=(w=D.props)==null?void 0:w.onClose)==null||q.call(w,D.removalReason),D.isActive=!1},E=D=>{if(D==null)y.forEach(U);else{let w=y.get(D);w&&U(w)}b()},M=()=>{f-=d.length,d=[]},R=D=>{var w,q;let{toastId:C,updateId:W}=D.props,$=W==null;D.staleId&&y.delete(D.staleId),D.isActive=!0,y.set(C,D),b(),u(wm(D,$?"added":"updated")),$&&((q=(w=D.props).onOpen)==null||q.call(w))};return{id:l,props:x,observe:v,toggle:H,removeToast:E,toasts:y,clearQueue:M,buildToast:(D,w)=>{if(O(w))return;let{toastId:q,updateId:C,data:W,staleId:$,delay:K}=w,oe=C==null;oe&&f++;let Ce={...x,style:x.toastStyle,key:c++,...Object.fromEntries(Object.entries(w).filter(([ie,rt])=>rt!=null)),toastId:q,updateId:C,data:W,isIn:!1,className:Ro(w.className||x.toastClassName),progressClassName:Ro(w.progressClassName||x.progressClassName),autoClose:w.isLoading?!1:_1(w.autoClose,x.autoClose),closeToast(ie){y.get(q).removalReason=ie,E(q)},deleteToast(){let ie=y.get(q);if(ie!=null){if(u(wm(ie,"removed")),y.delete(q),f--,f<0&&(f=0),d.length>0){R(d.shift());return}b()}}};Ce.closeButton=x.closeButton,w.closeButton===!1||Mo(w.closeButton)?Ce.closeButton=w.closeButton:w.closeButton===!0&&(Ce.closeButton=Mo(x.closeButton)?x.closeButton:!0);let be={content:D,props:Ce,staleId:$};x.limit&&x.limit>0&&f>x.limit&&oe?d.push(be):ys(K)?setTimeout(()=>{R(be)},K):R(be)},setProps(D){x=D},setToggle:(D,w)=>{let q=y.get(D);q&&(q.toggle=w)},isToastActive:D=>{var w;return(w=y.get(D))==null?void 0:w.isActive},getSnapshot:()=>m}}var gt=new Map,ms=[],zo=new Set,D1=l=>zo.forEach(r=>r(l)),b0=()=>gt.size>0;function U1(){ms.forEach(l=>N0(l.content,l.options)),ms=[]}var B1=(l,{containerId:r})=>{var u;return(u=gt.get(r||1))==null?void 0:u.toasts.get(l)};function j0(l,r){var u;if(r)return!!((u=gt.get(r))!=null&&u.isToastActive(l));let c=!1;return gt.forEach(f=>{f.isToastActive(l)&&(c=!0)}),c}function L1(l){if(!b0()){ms=ms.filter(r=>l!=null&&r.options.toastId!==l);return}if(l==null||E1(l))gt.forEach(r=>{r.removeToast(l)});else if(l&&("containerId"in l||"id"in l)){let r=gt.get(l.containerId);r?r.removeToast(l.id):gt.forEach(u=>{u.removeToast(l.id)})}}var H1=(l={})=>{gt.forEach(r=>{r.props.limit&&(!l.containerId||r.id===l.containerId)&&r.clearQueue()})};function N0(l,r){Mo(l)&&(b0()||ms.push({content:l,options:r}),gt.forEach(u=>{u.buildToast(l,r)}))}function k1(l){var r;(r=gt.get(l.containerId||1))==null||r.setToggle(l.id,l.fn)}function S0(l,r){gt.forEach(u=>{(r==null||!(r!=null&&r.containerId)||(r==null?void 0:r.containerId)===u.id)&&u.toggle(l,r==null?void 0:r.id)})}function q1(l){let r=l.containerId||1;return{subscribe(u){let c=z1(r,l,D1);gt.set(r,c);let f=c.observe(u);return U1(),()=>{f(),gt.delete(r)}},setProps(u){var c;(c=gt.get(r))==null||c.setProps(u)},getSnapshot(){var u;return(u=gt.get(r))==null?void 0:u.getSnapshot()}}}function G1(l){return zo.add(l),()=>{zo.delete(l)}}function Y1(l){return l&&(yn(l.toastId)||ys(l.toastId))?l.toastId:v0()}function vs(l,r){return N0(l,r),r.toastId}function Ji(l,r){return{...r,type:r&&r.type||l,toastId:Y1(r)}}function $i(l){return(r,u)=>vs(r,Ji(l,u))}function Te(l,r){return vs(l,Ji("default",r))}Te.loading=(l,r)=>vs(l,Ji("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...r}));function X1(l,{pending:r,error:u,success:c},f){let d;r&&(d=yn(r)?Te.loading(r,f):Te.loading(r.render,{...f,...r}));let m={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},x=(p,v,b)=>{if(v==null){Te.dismiss(d);return}let O={type:p,...m,...f,data:b},H=yn(v)?{render:v}:v;return d?Te.update(d,{...O,...H}):Te(H.render,{...O,...H}),b},y=ba(l)?l():l;return y.then(p=>x("success",c,p)).catch(p=>x("error",u,p)),y}Te.promise=X1;Te.success=$i("success");Te.info=$i("info");Te.error=$i("error");Te.warning=$i("warning");Te.warn=Te.warning;Te.dark=(l,r)=>vs(l,Ji("default",{theme:"dark",...r}));function V1(l){L1(l)}Te.dismiss=V1;Te.clearWaitingQueue=H1;Te.isActive=j0;Te.update=(l,r={})=>{let u=B1(l,r);if(u){let{props:c,content:f}=u,d={delay:100,...c,...r,toastId:r.toastId||l,updateId:v0()};d.toastId!==l&&(d.staleId=l);let m=d.render||f;delete d.render,vs(m,d)}};Te.done=l=>{Te.update(l,{progress:1})};Te.onChange=G1;Te.play=l=>S0(!0,l);Te.pause=l=>S0(!1,l);function Q1(l){var r;let{subscribe:u,getSnapshot:c,setProps:f}=N.useRef(q1(l)).current;f(l);let d=(r=N.useSyncExternalStore(u,c,c))==null?void 0:r.slice();function m(x){if(!d)return[];let y=new Map;return l.newestOnTop&&d.reverse(),d.forEach(p=>{let{position:v}=p.props;y.has(v)||y.set(v,[]),y.get(v).push(p)}),Array.from(y,p=>x(p[0],p[1]))}return{getToastToRender:m,isToastActive:j0,count:d==null?void 0:d.length}}function Z1(l){let[r,u]=N.useState(!1),[c,f]=N.useState(!1),d=N.useRef(null),m=N.useRef({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:x,pauseOnHover:y,closeToast:p,onClick:v,closeOnClick:b}=l;k1({id:l.toastId,containerId:l.containerId,fn:u}),N.useEffect(()=>{if(l.pauseOnFocusLoss)return O(),()=>{H()}},[l.pauseOnFocusLoss]);function O(){document.hasFocus()||R(),window.addEventListener("focus",M),window.addEventListener("blur",R)}function H(){window.removeEventListener("focus",M),window.removeEventListener("blur",R)}function U($){if(l.draggable===!0||l.draggable===$.pointerType){D();let K=d.current;m.canCloseOnClick=!0,m.canDrag=!0,K.style.transition="none",l.draggableDirection==="x"?(m.start=$.clientX,m.removalDistance=K.offsetWidth*(l.draggablePercent/100)):(m.start=$.clientY,m.removalDistance=K.offsetHeight*(l.draggablePercent===80?l.draggablePercent*1.5:l.draggablePercent)/100)}}function E($){let{top:K,bottom:oe,left:Ce,right:be}=d.current.getBoundingClientRect();$.nativeEvent.type!=="touchend"&&l.pauseOnHover&&$.clientX>=Ce&&$.clientX<=be&&$.clientY>=K&&$.clientY<=oe?R():M()}function M(){u(!0)}function R(){u(!1)}function D(){m.didMove=!1,document.addEventListener("pointermove",q),document.addEventListener("pointerup",C)}function w(){document.removeEventListener("pointermove",q),document.removeEventListener("pointerup",C)}function q($){let K=d.current;if(m.canDrag&&K){m.didMove=!0,r&&R(),l.draggableDirection==="x"?m.delta=$.clientX-m.start:m.delta=$.clientY-m.start,m.start!==$.clientX&&(m.canCloseOnClick=!1);let oe=l.draggableDirection==="x"?`${m.delta}px, var(--y)`:`0, calc(${m.delta}px + var(--y))`;K.style.transform=`translate3d(${oe},0)`,K.style.opacity=`${1-Math.abs(m.delta/m.removalDistance)}`}}function C(){w();let $=d.current;if(m.canDrag&&m.didMove&&$){if(m.canDrag=!1,Math.abs(m.delta)>m.removalDistance){f(!0),l.closeToast(!0),l.collapseAll();return}$.style.transition="transform 0.2s, opacity 0.2s",$.style.removeProperty("transform"),$.style.removeProperty("opacity")}}let W={onPointerDown:U,onPointerUp:E};return x&&y&&(W.onMouseEnter=R,l.stacked||(W.onMouseLeave=M)),b&&(W.onClick=$=>{v&&v($),m.canCloseOnClick&&p(!0)}),{playToast:M,pauseToast:R,isRunning:r,preventExitTransition:c,toastRef:d,eventHandlers:W}}var K1=typeof window<"u"?N.useLayoutEffect:N.useEffect,Pi=({theme:l,type:r,isLoading:u,...c})=>me.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:l==="colored"?"currentColor":`var(--toastify-icon-color-${r})`,...c});function J1(l){return me.createElement(Pi,{...l},me.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))}function $1(l){return me.createElement(Pi,{...l},me.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))}function P1(l){return me.createElement(Pi,{...l},me.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))}function F1(l){return me.createElement(Pi,{...l},me.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))}function W1(){return me.createElement("div",{className:"Toastify__spinner"})}var Do={info:$1,warning:J1,success:P1,error:F1,spinner:W1},I1=l=>l in Do;function eb({theme:l,type:r,isLoading:u,icon:c}){let f=null,d={theme:l,type:r};return c===!1||(ba(c)?f=c({...d,isLoading:u}):N.isValidElement(c)?f=N.cloneElement(c,d):u?f=Do.spinner():I1(r)&&(f=Do[r](d))),f}var tb=l=>{let{isRunning:r,preventExitTransition:u,toastRef:c,eventHandlers:f,playToast:d}=Z1(l),{closeButton:m,children:x,autoClose:y,onClick:p,type:v,hideProgressBar:b,closeToast:O,transition:H,position:U,className:E,style:M,progressClassName:R,updateId:D,role:w,progress:q,rtl:C,toastId:W,deleteToast:$,isIn:K,isLoading:oe,closeOnClick:Ce,theme:be,ariaLabel:ie}=l,rt=pn("Toastify__toast",`Toastify__toast-theme--${be}`,`Toastify__toast--${v}`,{"Toastify__toast--rtl":C},{"Toastify__toast--close-on-click":Ce}),Ze=ba(E)?E({rtl:C,position:U,type:v,defaultClassName:rt}):pn(rt,E),Ue=eb(l),G=!!q||!y,J={closeToast:O,type:v,theme:be},ee=null;return m===!1||(ba(m)?ee=m(J):N.isValidElement(m)?ee=N.cloneElement(m,J):ee=A1(J)),me.createElement(H,{isIn:K,done:$,position:U,preventExitTransition:u,nodeRef:c,playToast:d},me.createElement("div",{id:W,tabIndex:0,onClick:p,"data-in":K,className:Ze,...f,style:M,ref:c,...K&&{role:w,"aria-label":ie}},Ue!=null&&me.createElement("div",{className:pn("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!oe})},Ue),y0(x,l,!r),ee,!l.customProgressBar&&me.createElement(R1,{...D&&!G?{key:`p-${D}`}:{},rtl:C,theme:be,delay:y,isRunning:r,isIn:K,closeToast:O,hide:b,type:v,className:R,controlledProgress:G,progress:q||0})))},ab=(l,r=!1)=>({enter:`Toastify--animate Toastify__${l}-enter`,exit:`Toastify--animate Toastify__${l}-exit`,appendPosition:r}),nb=C1(ab("bounce",!0)),lb={position:"top-right",transition:nb,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light","aria-label":"Notifications Alt+T",hotKeys:l=>l.altKey&&l.code==="KeyT"};function sb(l){let r={...lb,...l},u=l.stacked,[c,f]=N.useState(!0),d=N.useRef(null),{getToastToRender:m,isToastActive:x,count:y}=Q1(r),{className:p,style:v,rtl:b,containerId:O,hotKeys:H}=r;function U(M){let R=pn("Toastify__toast-container",`Toastify__toast-container--${M}`,{"Toastify__toast-container--rtl":b});return ba(p)?p({position:M,rtl:b,defaultClassName:R}):pn(R,Ro(p))}function E(){u&&(f(!0),Te.play())}return K1(()=>{var M;if(u){let R=d.current.querySelectorAll('[data-in="true"]'),D=12,w=(M=r.position)==null?void 0:M.includes("top"),q=0,C=0;Array.from(R).reverse().forEach((W,$)=>{let K=W;K.classList.add("Toastify__toast--stacked"),$>0&&(K.dataset.collapsed=`${c}`),K.dataset.pos||(K.dataset.pos=w?"top":"bot");let oe=q*(c?.2:1)+(c?0:D*$);K.style.setProperty("--y",`${w?oe:oe*-1}px`),K.style.setProperty("--g",`${D}`),K.style.setProperty("--s",`${1-(c?C:0)}`),q+=K.offsetHeight,C+=.025})}},[c,y,u]),N.useEffect(()=>{function M(R){var D;let w=d.current;H(R)&&((D=w.querySelector('[tabIndex="0"]'))==null||D.focus(),f(!1),Te.pause()),R.key==="Escape"&&(document.activeElement===w||w!=null&&w.contains(document.activeElement))&&(f(!0),Te.play())}return document.addEventListener("keydown",M),()=>{document.removeEventListener("keydown",M)}},[H]),me.createElement("section",{ref:d,className:"Toastify",id:O,onMouseEnter:()=>{u&&(f(!1),Te.pause())},onMouseLeave:E,"aria-live":"polite","aria-atomic":"false","aria-relevant":"additions text","aria-label":r["aria-label"]},m((M,R)=>{let D=R.length?{...v}:{...v,pointerEvents:"none"};return me.createElement("div",{tabIndex:-1,className:U(M),"data-stacked":u,style:D,key:`c-${M}`},R.map(({content:w,props:q})=>me.createElement(tb,{...q,stacked:u,collapseAll:E,isIn:x(q.toastId,q.containerId),key:`t-${q.key}`},w)))}))}const ib=()=>{const[l,r]=N.useState(null),[u,c]=N.useState({first_name:"",last_name:"",email:"",phone:"",address:"",birth_date:"",gender:"",avatar:""}),[f,d]=N.useState({currentPassword:"",newPassword:"",confirmPassword:""}),[m,x]=N.useState(""),[y,p]=N.useState(!1),[v,b]=N.useState("profile"),O=Jt();N.useEffect(()=>{const D=localStorage.getItem("user");if(D){const w=JSON.parse(D);r(w),c({first_name:w.first_name||"",last_name:w.last_name||"",email:w.email||"",phone:w.phone||"",address:w.address||"",birth_date:w.birth_date||"",gender:w.gender||"",avatar:w.avatar||""}),x(w.avatar||"https://i.pravatar.cc/150")}},[]);const H=D=>{c({...u,[D.target.name]:D.target.value})},U=D=>{d({...f,[D.target.name]:D.target.value})},E=D=>{const w=D.target.files[0];if(w){const q=new FileReader;q.onloadend=()=>{x(q.result),c({...u,avatar:q.result})},q.readAsDataURL(w)}},M=async D=>{var w,q;D.preventDefault(),p(!0);try{const C=localStorage.getItem("token"),W=await ue.put("http://localhost:5000/api/auth/profile",u,{headers:{Authorization:`Bearer ${C}`}});localStorage.setItem("user",JSON.stringify(W.data.user)),r(W.data.user),Te.success("Cập nhật thông tin thành công!")}catch(C){Te.error(((q=(w=C.response)==null?void 0:w.data)==null?void 0:q.error)||"Cập nhật thất bại")}finally{p(!1)}},R=async D=>{var w,q;if(D.preventDefault(),f.newPassword!==f.confirmPassword){Te.error("Mật khẩu mới không khớp");return}p(!0);try{const C=localStorage.getItem("token");await ue.put("http://localhost:5000/api/auth/change-password",f,{headers:{Authorization:`Bearer ${C}`}}),Te.success("Đổi mật khẩu thành công!"),d({currentPassword:"",newPassword:"",confirmPassword:""})}catch(C){Te.error(((q=(w=C.response)==null?void 0:w.data)==null?void 0:q.error)||"Đổi mật khẩu thất bại")}finally{p(!1)}};return s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx("div",{className:"bg-[#ffd400] shadow-sm border-b",children:s.jsx("div",{className:"max-w-6xl mx-auto px-4 py-4",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsxs("button",{onClick:()=>O("/home"),className:"flex items-center gap-2 text-gray-800 hover:text-gray400",children:[s.jsx(fy,{}),s.jsx("span",{children:"Quay lại"})]}),s.jsx("h1",{className:"text-1xl font-bold text-gray-800",children:"Thông tin cá nhân"})]}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("img",{src:m,alt:"Avatar",className:"w-10 h-10 rounded-full object-cover"}),s.jsxs("span",{className:"text-sm text-gray-800",children:[l==null?void 0:l.first_name," ",l==null?void 0:l.last_name]})]})]})})}),s.jsx("div",{className:"max-w-6xl mx-auto px-4 py-8",children:s.jsxs("div",{className:"grid lg:grid-cols-4 gap-8",children:[s.jsx("div",{className:"lg:col-span-1",children:s.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[s.jsxs("div",{className:"text-center mb-6",children:[s.jsxs("div",{className:"relative inline-block",children:[s.jsx("img",{src:m,alt:"Avatar",className:"w-24 h-24 rounded-full object-cover mx-auto"}),s.jsxs("label",{className:"absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full cursor-pointer hover:bg-blue-700",children:[s.jsx(dy,{className:"w-3 h-3"}),s.jsx("input",{type:"file",accept:"image/*",onChange:E,className:"hidden"})]})]}),s.jsxs("h3",{className:"mt-4 font-semibold text-gray-800",children:[l==null?void 0:l.first_name," ",l==null?void 0:l.last_name]}),s.jsx("p",{className:"text-sm text-gray-500",children:l==null?void 0:l.email})]}),s.jsxs("nav",{className:"space-y-2",children:[s.jsxs("button",{onClick:()=>b("profile"),className:`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-colors ${v==="profile"?"bg-blue-50 text-blue-600 border border-blue-200":"text-gray-600 hover:bg-gray-50"}`,children:[s.jsx(Go,{}),s.jsx("span",{children:"Thông tin cá nhân"})]}),s.jsxs("button",{onClick:()=>b("password"),className:`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-colors ${v==="password"?"bg-blue-50 text-blue-600 border border-blue-200":"text-gray-600 hover:bg-gray-50"}`,children:[s.jsx(gy,{}),s.jsx("span",{children:"Đổi mật khẩu"})]})]})]})}),s.jsx("div",{className:"lg:col-span-3",children:s.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[v==="profile"&&s.jsxs("div",{children:[s.jsx("h2",{className:"text-xl font-semibold mb-6",children:"Cập nhật thông tin cá nhân"}),s.jsxs("form",{onSubmit:M,className:"space-y-6",children:[s.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Họ *"}),s.jsx("input",{type:"text",name:"first_name",value:u.first_name,onChange:H,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tên *"}),s.jsx("input",{type:"text",name:"last_name",value:u.last_name,onChange:H,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email *"}),s.jsx("input",{type:"email",name:"email",value:u.email,onChange:H,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]}),s.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số điện thoại"}),s.jsx("input",{type:"tel",name:"phone",value:u.phone,onChange:H,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"0123456789"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Ngày sinh"}),s.jsx("input",{type:"date",name:"birth_date",value:u.birth_date,onChange:H,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Giới tính"}),s.jsxs("select",{name:"gender",value:u.gender,onChange:H,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[s.jsx("option",{value:"",children:"Chọn giới tính"}),s.jsx("option",{value:"male",children:"Nam"}),s.jsx("option",{value:"female",children:"Nữ"}),s.jsx("option",{value:"other",children:"Khác"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Địa chỉ"}),s.jsx("textarea",{name:"address",value:u.address,onChange:H,rows:3,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Nhập địa chỉ của bạn..."})]}),s.jsx("div",{className:"flex justify-end",children:s.jsx("button",{type:"submit",disabled:y,className:"px-6 py-3 bg-[#ffd400]  text-black rounded-lg hover:bg-blue-700 disabled:bg-blue-400 transition-colors",children:y?"Đang cập nhật...":"Cập nhật thông tin"})})]})]}),v==="password"&&s.jsxs("div",{children:[s.jsx("h2",{className:"text-xl font-semibold mb-6",children:"Đổi mật khẩu"}),s.jsxs("form",{onSubmit:R,className:"space-y-6 max-w-md",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Mật khẩu hiện tại *"}),s.jsx("input",{type:"password",name:"currentPassword",value:f.currentPassword,onChange:U,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Mật khẩu mới *"}),s.jsx("input",{type:"password",name:"newPassword",value:f.newPassword,onChange:U,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,minLength:6})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Xác nhận mật khẩu mới *"}),s.jsx("input",{type:"password",name:"confirmPassword",value:f.confirmPassword,onChange:U,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,minLength:6})]}),s.jsx("div",{className:"flex justify-end",children:s.jsx("button",{type:"submit",disabled:y,className:"px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-red-400 transition-colors",children:y?"Đang đổi...":"Đổi mật khẩu"})})]})]})]})})]})}),s.jsx(sb,{position:"top-right",autoClose:3e3,hideProgressBar:!1,newestOnTop:!1,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0})]})},rb=()=>{const[l,r]=N.useState(null),[u,c]=N.useState(!1),[f,d]=N.useState(!1),[m,x]=N.useState(""),[y,p]=N.useState(null),[v,b]=N.useState([]),O=[{label:"100K",value:1e5},{label:"200K",value:2e5},{label:"500K",value:5e5},{label:"1M",value:1e6},{label:"2M",value:2e6},{label:"5M",value:5e6}],H=w=>new Intl.NumberFormat("vi-VN").format(w),U=w=>{x(w.toString())},E=async()=>{if(!m||m<=0){alert("Vui lòng nhập số tiền hợp lệ");return}try{const w=localStorage.getItem("token"),q=await ue.post("http://localhost:5000/api/wallet/deposit",{amount:parseInt(m)},{headers:{Authorization:`Bearer ${w}`}});p(q.data),alert("Yêu cầu nạp tiền đã được tạo! Vui lòng chuyển khoản theo thông tin bên dưới.")}catch(w){console.error("Lỗi nạp tiền:",w),alert("Có lỗi xảy ra khi tạo yêu cầu nạp tiền")}},M=async()=>{try{const w=localStorage.getItem("token"),q=await ue.get("http://localhost:5000/api/wallet",{headers:{Authorization:`Bearer ${w}`}});r(q.data.wallet),b(q.data.depositHistory||[])}catch(w){console.error("Lỗi lấy thông tin ví:",w)}},R=w=>{switch(w){case"approved":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},D=w=>{switch(w){case"approved":return"Đã duyệt";case"pending":return"Chờ duyệt";case"rejected":return"Từ chối";default:return"Không xác định"}};return N.useEffect(()=>{M()},[]),s.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100",children:[s.jsx(Kt,{}),s.jsxs("div",{className:"max-w-7xl mx-auto p-8",children:[s.jsx("div",{className:"text-center mb-12",children:s.jsxs("div",{className:"flex items-center justify-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg",children:s.jsx(Ym,{className:"text-2xl text-white"})}),s.jsx("h1",{className:"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Ví điện tử"})]})}),s.jsxs("div",{className:"grid lg:grid-cols-3 gap-8",children:[s.jsxs("div",{className:"lg:col-span-1 space-y-6",children:[s.jsxs("div",{className:"relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white rounded-3xl shadow-2xl",children:[s.jsx("div",{className:"absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"}),s.jsx("div",{className:"absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"}),s.jsxs("div",{className:"relative p-8",children:[s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-blue-100 text-sm font-medium",children:"Số dư khả dụng"}),s.jsx("h2",{className:"text-3xl font-bold mt-1",children:H((l==null?void 0:l.balance)||0)}),s.jsx("p",{className:"text-blue-100 text-lg",children:"VNĐ"})]}),s.jsx("div",{className:"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center",children:s.jsx(my,{className:"text-xl"})})]}),s.jsxs("div",{className:"flex items-center gap-2 text-green-300 text-sm",children:[s.jsx("div",{className:"w-2 h-2 bg-green-300 rounded-full animate-pulse"}),s.jsx("span",{children:"Tài khoản đang hoạt động"})]})]})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsx("div",{className:"bg-white rounded-2xl p-6 shadow-lg border border-gray-100",children:s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center",children:s.jsx("span",{className:"text-green-600 text-lg",children:"📈"})}),s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-500 text-xs",children:"Tổng nạp"}),s.jsx("p",{className:"font-bold text-green-600",children:H(v.filter(w=>w.status==="approved").reduce((w,q)=>w+parseInt(q.amount),0))})]})]})}),s.jsx("div",{className:"bg-white rounded-2xl p-6 shadow-lg border border-gray-100",children:s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center",children:s.jsx("span",{className:"text-blue-600 text-lg",children:"📊"})}),s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-500 text-xs",children:"Giao dịch"}),s.jsx("p",{className:"font-bold text-blue-600",children:v.length})]})]})})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("button",{onClick:()=>{c(!0),d(!1)},className:`w-full py-4 px-6 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 ${u?"bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-green-200":"bg-white hover:bg-green-50 text-green-600 border-2 border-green-200 hover:border-green-300"}`,children:s.jsxs("div",{className:"flex items-center justify-center gap-3",children:[s.jsx("span",{className:"text-xl",children:"💰"}),s.jsx("span",{children:"Nạp tiền vào ví"})]})}),s.jsx("button",{onClick:()=>{d(!0),c(!1)},className:`w-full py-4 px-6 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 ${f?"bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-orange-200":"bg-white hover:bg-orange-50 text-orange-600 border-2 border-orange-200 hover:border-orange-300"}`,children:s.jsxs("div",{className:"flex items-center justify-center gap-3",children:[s.jsx(im,{className:"text-lg"}),s.jsx("span",{children:"Lịch sử giao dịch"})]})})]})]}),s.jsxs("div",{className:"lg:col-span-2",children:[u&&s.jsxs("div",{className:"bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden",children:[s.jsx("div",{className:"bg-gradient-to-r from-green-500 to-emerald-600 p-8 text-white",children:s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx("div",{className:"w-14 h-14 bg-white/20 rounded-2xl flex items-center justify-center",children:s.jsx("span",{className:"text-2xl",children:"💳"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-2xl font-bold",children:"Nạp tiền vào ví"}),s.jsx("p",{className:"text-green-100",children:"Chọn số tiền và phương thức thanh toán"})]})]})}),s.jsxs("div",{className:"p-8",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("label",{className:"block text-sm font-semibold text-gray-700 mb-4",children:"Số tiền muốn nạp"}),s.jsxs("div",{className:"relative",children:[s.jsx("input",{type:"number",placeholder:"Nhập số tiền...",value:m,onChange:w=>x(w.target.value),className:"w-full p-6 text-2xl font-bold border-2 border-gray-200 rounded-2xl focus:outline-none focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all"}),s.jsx("span",{className:"absolute right-6 top-1/2 transform -translate-y-1/2 text-gray-400 font-semibold text-lg",children:"VNĐ"})]})]}),s.jsxs("div",{className:"mb-8",children:[s.jsx("p",{className:"text-sm font-semibold text-gray-700 mb-4",children:"Chọn nhanh"}),s.jsx("div",{className:"grid grid-cols-3 gap-4",children:O.map((w,q)=>s.jsx("button",{onClick:()=>U(w.value),className:`p-4 rounded-xl text-sm font-semibold transition-all duration-200 ${m==w.value?"bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg transform scale-105":"bg-gray-50 hover:bg-gray-100 text-gray-700 border border-gray-200"}`,children:w.label},q))})]}),s.jsxs("div",{className:"mb-8",children:[s.jsx("p",{className:"text-sm font-semibold text-gray-700 mb-4",children:"Phương thức thanh toán"}),s.jsx("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 rounded-2xl p-6",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mr-6",children:s.jsx("span",{className:"text-white text-2xl font-bold",children:"₫"})}),s.jsxs("div",{className:"flex-1",children:[s.jsx("p",{className:"font-bold text-gray-800 text-lg",children:"Chuyển khoản ngân hàng"}),s.jsx("p",{className:"text-gray-600",children:"Vietcombank • An toàn & Nhanh chóng"})]}),s.jsx("div",{className:"text-green-500",children:s.jsx("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]})})]}),s.jsx("div",{className:"bg-gray-50 rounded-2xl p-6 mb-8",children:s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-gray-700 font-semibold text-lg",children:"Tổng thanh toán"}),s.jsxs("span",{className:"text-3xl font-bold text-green-600",children:[H(m||0)," VNĐ"]})]})}),s.jsxs("div",{className:"flex gap-4",children:[s.jsx("button",{onClick:()=>c(!1),className:"flex-1 py-4 border-2 border-gray-300 rounded-2xl text-gray-700 font-semibold hover:bg-gray-50 transition-all",children:"Hủy bỏ"}),s.jsx("button",{onClick:E,className:"flex-1 py-4 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white rounded-2xl font-semibold transition-all shadow-lg hover:shadow-xl",children:"Xác nhận nạp tiền"})]}),y&&s.jsxs("div",{className:"mt-8 p-6 bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-200 rounded-2xl",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx("div",{className:"w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center",children:s.jsx("span",{className:"text-white text-lg",children:"🏦"})}),s.jsx("h4",{className:"text-xl font-bold text-gray-800",children:"Thông tin chuyển khoản"})]}),s.jsxs("div",{className:"bg-white rounded-xl p-6 shadow-sm",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Ngân hàng"}),s.jsx("p",{className:"text-lg font-bold text-gray-800",children:y.bankInfo.bank})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Số tài khoản"}),s.jsx("p",{className:"text-lg font-bold text-blue-600 font-mono",children:y.bankInfo.accountNumber})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Chủ tài khoản"}),s.jsx("p",{className:"text-lg font-bold text-gray-800",children:y.bankInfo.accountName})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Số tiền"}),s.jsxs("p",{className:"text-lg font-bold text-red-600",children:[H(m)," VNĐ"]})]})]}),s.jsxs("div",{className:"mt-6 pt-4 border-t border-gray-200",children:[s.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Nội dung chuyển khoản"}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("p",{className:"text-lg font-bold text-green-600 font-mono bg-green-50 px-4 py-2 rounded-lg flex-1",children:y.transferCode}),s.jsx("button",{onClick:()=>navigator.clipboard.writeText(y.transferCode),className:"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors",children:"Copy"})]})]})]}),s.jsx("div",{className:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-xl",children:s.jsxs("div",{className:"flex items-start gap-3",children:[s.jsx("span",{className:"text-blue-500 text-lg",children:"ℹ️"}),s.jsxs("div",{className:"text-sm text-blue-700",children:[s.jsx("p",{className:"font-semibold mb-1",children:"Lưu ý quan trọng:"}),s.jsxs("ul",{className:"list-disc list-inside space-y-1",children:[s.jsx("li",{children:"Vui lòng chuyển khoản đúng số tiền và nội dung"}),s.jsx("li",{children:"Thời gian xử lý: 5-15 phút sau khi chuyển khoản"}),s.jsx("li",{children:"Liên hệ hỗ trợ nếu sau 30 phút chưa được duyệt"})]})]})]})})]})]})]}),f&&s.jsxs("div",{className:"bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden",children:[s.jsx("div",{className:"bg-gradient-to-r from-orange-500 to-red-500 p-8 text-white",children:s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx("div",{className:"w-14 h-14 bg-white/20 rounded-2xl flex items-center justify-center",children:s.jsx(im,{className:"text-2xl"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-2xl font-bold",children:"Lịch sử giao dịch"}),s.jsx("p",{className:"text-orange-100",children:"Theo dõi tất cả giao dịch nạp tiền"})]})]})}),s.jsxs("div",{className:"p-8",children:[v.length===0?s.jsxs("div",{className:"text-center py-16",children:[s.jsx("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx("span",{className:"text-4xl"})}),s.jsx("h4",{className:"text-xl font-semibold text-gray-800 mb-2",children:"Chưa có giao dịch"}),s.jsx("p",{className:"text-gray-500",children:"Lịch sử nạp tiền sẽ hiển thị tại đây"})]}):s.jsx("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:v.map((w,q)=>{var C;return s.jsxs("div",{className:"border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all",children:[s.jsxs("div",{className:"flex justify-between items-start mb-4",children:[s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center",children:s.jsx("span",{className:"text-blue-600 text-xl",children:"💰"})}),s.jsxs("div",{children:[s.jsx("p",{className:"font-semibold text-gray-800 text-lg",children:"Nạp tiền vào ví"}),s.jsx("p",{className:"text-gray-500",children:new Date(w.created_at).toLocaleString("vi-VN")})]})]}),s.jsxs("div",{className:"text-right",children:[s.jsxs("p",{className:"text-2xl font-bold text-green-600",children:["+",H(w.amount)," VNĐ"]}),s.jsx("span",{className:`px-3 py-1 text-sm rounded-full font-medium ${R(w.status)}`,children:D(w.status)})]})]}),s.jsxs("div",{className:"bg-gray-50 rounded-xl p-4",children:[s.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-3",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600 text-sm",children:"Mã giao dịch:"}),s.jsx("p",{className:"font-mono text-blue-600 font-semibold",children:w.transfer_code})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600 text-sm",children:"Ngân hàng:"}),s.jsx("p",{className:"font-semibold",children:((C=w.bank_account)==null?void 0:C.split(" - ")[0])||"Vietcombank"})]})]}),w.status==="pending"&&s.jsx("div",{className:"bg-yellow-50 border-l-4 border-yellow-400 rounded p-3",children:s.jsx("p",{className:"text-yellow-700 text-sm",children:"⏳ Đang chờ xác nhận từ admin. Thời gian xử lý: 5-15 phút"})}),w.status==="approved"&&s.jsx("div",{className:"bg-green-50 border-l-4 border-green-400 rounded p-3",children:s.jsx("p",{className:"text-green-700 text-sm",children:"✅ Giao dịch thành công! Số dư đã được cập nhật"})}),w.status==="rejected"&&s.jsx("div",{className:"bg-red-50 border-l-4 border-red-400 rounded p-3",children:s.jsx("p",{className:"text-red-700 text-sm",children:"❌ Giao dịch bị từ chối. Vui lòng liên hệ hỗ trợ"})})]})]},q)})}),v.length>0&&s.jsx("div",{className:"mt-8 pt-6 border-t border-gray-200",children:s.jsxs("div",{className:"grid grid-cols-2 gap-4 text-center",children:[s.jsxs("div",{className:"bg-blue-50 rounded-xl p-4",children:[s.jsx("p",{className:"text-blue-600 font-semibold",children:"Tổng giao dịch"}),s.jsx("p",{className:"text-2xl font-bold text-blue-700",children:v.length})]}),s.jsxs("div",{className:"bg-green-50 rounded-xl p-4",children:[s.jsx("p",{className:"text-green-600 font-semibold",children:"Tổng đã nạp"}),s.jsxs("p",{className:"text-2xl font-bold text-green-700",children:[H(v.filter(w=>w.status==="approved").reduce((w,q)=>w+parseInt(q.amount),0))," VNĐ"]})]})]})})]})]})]})]})]}),s.jsx(jt,{})]})},cb=()=>{var U;const[l,r]=N.useState([]),[u,c]=N.useState(!0),[f,d]=N.useState(null),[m,x]=N.useState("all"),y=[{key:"all",label:"Tất cả",count:l.length},{key:"pending",label:"Chờ xác nhận",count:l.filter(E=>E.status==="pending").length},{key:"confirmed",label:"Chờ lấy hàng",count:l.filter(E=>E.status==="confirmed").length},{key:"shipping",label:"Chờ giao hàng",count:l.filter(E=>E.status==="shipping").length},{key:"completed",label:"Đã giao",count:l.filter(E=>E.status==="completed").length},{key:"cancelled",label:"Đã hủy",count:l.filter(E=>E.status==="cancelled").length}],p=m==="all"?l:l.filter(E=>E.status===m);N.useEffect(()=>{v();const E=M=>{M.key==="orderUpdate"&&(v(),localStorage.removeItem("orderUpdate"))};return window.addEventListener("storage",E),()=>window.removeEventListener("storage",E)},[]);const v=async()=>{try{const E=localStorage.getItem("token"),M=localStorage.getItem("userEmail");if(!E||!M){d("Vui lòng đăng nhập để xem lịch sử đơn hàng"),c(!1);return}const R=await ue.get(`http://localhost:5000/api/orders/user/${M}`,{headers:{Authorization:`Bearer ${E}`}});r(R.data),c(!1)}catch(E){console.error("Lỗi khi tải lịch sử đơn hàng:",E),d("Không thể tải lịch sử đơn hàng"),c(!1)}},b=E=>{switch(E){case"pending":return s.jsx(sm,{className:"text-yellow-500"});case"confirmed":return s.jsx(lm,{className:"text-blue-500"});case"shipping":return s.jsx(xy,{className:"text-purple-500"});case"completed":return s.jsx(hy,{className:"text-green-500"});case"cancelled":return s.jsx(py,{className:"text-red-500"});default:return s.jsx(sm,{className:"text-gray-500"})}},O=E=>{switch(E){case"pending":return"Chờ xác nhận";case"confirmed":return"Đã xác nhận";case"shipping":return"Đang giao hàng";case"completed":return"Đã giao hàng";case"cancelled":return"Đã hủy";default:return"Chờ xác nhận"}},H=E=>{switch(E){case"pending":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"confirmed":return"bg-blue-100 text-blue-800 border-blue-200";case"shipping":return"bg-purple-100 text-purple-800 border-purple-200";case"completed":return"bg-green-100 text-green-800 border-green-200";case"cancelled":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}};return u?s.jsxs("div",{children:[s.jsx(Kt,{}),s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"}),s.jsx("p",{className:"mt-4 text-gray-600",children:"Đang tải lịch sử đơn hàng..."})]})}),s.jsx(jt,{})]}):f?s.jsxs("div",{children:[s.jsx(Kt,{}),s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:s.jsx("div",{className:"text-center",children:s.jsx("p",{className:"text-red-600 text-lg",children:f})})}),s.jsx(jt,{})]}):s.jsxs("div",{children:[s.jsx(Kt,{}),s.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:s.jsxs("div",{className:"max-w-6xl mx-auto px-4",children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-800 mb-8",children:"📦 Lịch sử đơn hàng"}),s.jsx("div",{className:"bg-white rounded-lg shadow-sm mb-6",children:s.jsx("div",{className:"flex overflow-x-auto",children:y.map(E=>s.jsx("button",{onClick:()=>x(E.key),className:`flex-1 min-w-[120px] px-4 py-4 text-sm font-medium border-b-2 transition-colors ${m===E.key?"text-red-600 border-red-600 bg-red-50":"text-gray-600 border-transparent hover:text-gray-800 hover:bg-gray-50"}`,children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{children:E.label}),E.count>0&&s.jsxs("div",{className:`text-xs mt-1 ${m===E.key?"text-red-500":"text-gray-400"}`,children:["(",E.count,")"]})]})},E.key))})}),u?s.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-12 text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"}),s.jsx("p",{className:"mt-4 text-gray-600",children:"Đang tải lịch sử đơn hàng..."})]}):f?s.jsx("div",{className:"bg-white rounded-lg shadow-sm p-12 text-center",children:s.jsx("p",{className:"text-red-600 text-lg",children:f})}):p.length===0?s.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-12 text-center",children:[s.jsx("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(lm,{className:"text-4xl text-gray-400"})}),s.jsx("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:m==="all"?"Chưa có đơn hàng nào":`Không có đơn hàng ${(U=y.find(E=>E.key===m))==null?void 0:U.label.toLowerCase()}`}),s.jsx("p",{className:"text-gray-500",children:m==="all"?"Bạn chưa đặt đơn hàng nào. Hãy mua sắm ngay!":"Hãy tiếp tục mua sắm để có thêm đơn hàng!"})]}):s.jsx("div",{className:"space-y-6",children:p.map(E=>{var M;return s.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:s.jsxs("div",{className:"p-6",children:[s.jsxs("div",{className:"flex justify-between items-start mb-4",children:[s.jsxs("div",{children:[s.jsxs("h3",{className:"text-lg font-semibold text-gray-800",children:["Đơn hàng #",E.id]}),s.jsxs("p",{className:"text-sm text-gray-500",children:["Đặt ngày: ",new Date(E.created_at).toLocaleDateString("vi-VN")]})]}),s.jsxs("div",{className:`flex items-center gap-2 px-3 py-2 rounded-full border ${H(E.status)}`,children:[b(E.status),s.jsx("span",{className:"text-sm font-medium",children:O(E.status)})]})]}),s.jsx("div",{className:"border-t border-gray-100 pt-4",children:s.jsx("div",{className:"flex gap-4",children:s.jsxs("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:E.product_title}),s.jsxs("p",{className:"text-2xl font-bold text-red-600",children:[(M=E.product_price)==null?void 0:M.toLocaleString(),"₫"]}),s.jsxs("div",{className:"mt-3 text-sm text-gray-600",children:[s.jsxs("p",{children:[s.jsx("span",{className:"font-medium",children:"Người nhận:"})," ",E.full_name]}),s.jsxs("p",{children:[s.jsx("span",{className:"font-medium",children:"Điện thoại:"})," ",E.phone]}),s.jsxs("p",{children:[s.jsx("span",{className:"font-medium",children:"Địa chỉ:"})," ",E.address]}),s.jsxs("p",{children:[s.jsx("span",{className:"font-medium",children:"Thanh toán:"})," ",E.payment_method==="wallet"?"Ví điện tử":"COD"]})]})]})})}),s.jsxs("div",{className:"mt-6 pt-4 border-t border-gray-100",children:[s.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-500 mb-2",children:[s.jsx("span",{children:"Chờ xác nhận"}),s.jsx("span",{children:"Đã xác nhận"}),s.jsx("span",{children:"Đang giao"}),s.jsx("span",{children:"Hoàn thành"})]}),s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:`h-2 rounded-full transition-all duration-300 ${E.status==="pending"?"bg-yellow-500 w-1/4":E.status==="confirmed"?"bg-blue-500 w-2/4":E.status==="shipping"?"bg-purple-500 w-3/4":E.status==="completed"?"bg-green-500 w-full":"bg-red-500 w-1/4"}`})})]})]})},E.id)})})]})}),s.jsx(jt,{})]})};function ob(){return s.jsxs(s.Fragment,{children:[s.jsx(Yo,{}),s.jsx(cl,{}),s.jsx(Cy,{}),s.jsx(w1,{}),s.jsx(Ay,{}),s.jsx(Ry,{}),s.jsx(jt,{})]})}function ub(){const[l,r]=N.useState(!1);return s.jsxs(s.Fragment,{children:[s.jsx(Kt,{onFilterChange:r}),!l&&s.jsxs(s.Fragment,{children:[s.jsx(cl,{}),s.jsx(p0,{})]}),s.jsx(j1,{}),s.jsx(jt,{})]})}function fb(){return s.jsx(ny,{children:s.jsx(Ix,{children:s.jsxs(Zx,{children:[s.jsx(zt,{path:"/",element:s.jsx(ob,{})}),s.jsx(zt,{path:"/login",element:s.jsx(f1,{})}),s.jsx(zt,{path:"/signup",element:s.jsx(d1,{})}),s.jsx(zt,{path:"/home",element:s.jsx(ub,{})}),s.jsx(zt,{path:"/cart",element:s.jsx(y1,{})}),s.jsx(zt,{path:"/product/:id",element:s.jsx(v1,{})}),s.jsx(zt,{path:"/cartpay",element:s.jsx(N1,{})}),s.jsx(zt,{path:"/support",element:s.jsx(S1,{})}),s.jsx(zt,{path:"/profile",element:s.jsx(ib,{})}),s.jsx(zt,{path:"/admin",element:s.jsx(x1,{children:s.jsx(p1,{})})}),s.jsx(zt,{path:"/wallet",element:s.jsx(rb,{})}),s.jsx(zt,{path:"/orders",element:s.jsx(cb,{})})]})})})}const Tm=document.getElementById("root");Tm&&ex.createRoot(Tm).render(s.jsx(N.StrictMode,{children:s.jsx(fb,{})}));
