"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserOrders = exports.updateOrderStatus = exports.getAllOrders = exports.createOrder = void 0;
const database_1 = __importDefault(require("../config/database"));
/*-----------------------------------------
 Create order
  -------------------------------------------*/
const createOrder = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { fullName, email, phone, address, productId, productTitle, productPrice, quantity = 1, paymentMethod = "cod" } = req.body;
        /*-----------------------------------------
                Check db
        -------------------------------------------*/
        if (!fullName || !email || !phone || !address || !productId || !productTitle || !productPrice) {
            res.status(400).json({ error: "Thiếu thông tin đơn hàng" });
            return;
        }
        const totalAmount = productPrice * quantity;
        /*-----------------------------------------
        Xử lý thanh toán bằng ví
        -------------------------------------------*/
        if (paymentMethod === "wallet") {
            const token = (_a = req.headers.authorization) === null || _a === void 0 ? void 0 : _a.split(' ')[1];
            if (!token) {
                res.status(401).json({ error: "Cần đăng nhập để thanh toán bằng ví" });
                return;
            }
            const jwt = require('jsonwebtoken');
            const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
            const userId = decoded.userId;
            // Kiểm tra số dư ví
            const [wallets] = yield database_1.default.execute('SELECT balance FROM wallets WHERE user_id = ?', [userId]);
            const wallet = wallets[0];
            if (!wallet || wallet.balance < totalAmount) {
                res.status(400).json({ error: "Số dư ví không đủ để thanh toán" });
                return;
            }
            // Sử dụng connection cho transaction
            const connection = yield database_1.default.getConnection();
            try {
                yield connection.beginTransaction();
                // Tạo đơn hàng với trạng thái đã thanh toán
                yield connection.execute("INSERT INTO orders (full_name, email, phone, address, product_id, product_title, product_price, status, payment_method) VALUES (?, ?, ?, ?, ?, ?, ?, 'confirmed', 'wallet')", [fullName, email, phone, address, productId, productTitle, productPrice]);
                // Trừ tiền từ ví
                yield connection.execute('UPDATE wallets SET balance = balance - ? WHERE user_id = ?', [totalAmount, userId]);
                // Ghi lại lịch sử giao dịch (nếu có bảng này)
                try {
                    yield connection.execute('INSERT INTO wallet_transactions (user_id, type, amount, description) VALUES (?, "payment", ?, ?)', [userId, totalAmount, `Thanh toán đơn hàng: ${productTitle}`]);
                }
                catch (err) {
                    // Bỏ qua nếu bảng wallet_transactions chưa tồn tại
                    console.log('Bảng wallet_transactions chưa tồn tại');
                }
                yield connection.commit();
                res.status(200).json({
                    message: "Đặt hàng và thanh toán thành công",
                    paymentMethod: "wallet"
                });
            }
            catch (error) {
                yield connection.rollback();
                throw error;
            }
            finally {
                connection.release();
            }
        }
        else {
            /*-----------------------------------------
            Thanh toán khi nhận hàng (COD)
            -------------------------------------------*/
            yield database_1.default.execute("INSERT INTO orders (full_name, email, phone, address, product_id, product_title, product_price, status, payment_method) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', 'cod')", [fullName, email, phone, address, productId, productTitle, productPrice]);
            res.status(200).json({
                message: "Đặt hàng thành công",
                paymentMethod: "cod"
            });
        }
    }
    catch (error) {
        console.error(error);
        res.status(500).json({ error: "Lỗi server" });
    }
});
exports.createOrder = createOrder;
/*-----------------------------------------
    Get all product
  -------------------------------------------*/
const getAllOrders = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const [orders] = yield database_1.default.execute('SELECT * FROM orders ORDER BY created_at DESC');
        res.json(orders);
    }
    catch (error) {
        console.error('Lỗi khi lấy danh sách đơn hàng:', error);
        res.status(500).json({ error: 'Lỗi server' });
    }
});
exports.getAllOrders = getAllOrders;
/*-----------------------------------------
  Update status product
  -------------------------------------------*/
const updateOrderStatus = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const { status } = req.body;
        console.log('Yeu cau nhan:', {
            id,
            status,
            headers: req.headers,
            body: req.body
        });
        /*----------------------------------
        Check for valid status
        -----------------------------------*/
        const validStatuses = ['pending', 'confirmed', 'shipping', 'completed', 'cancelled'];
        if (!validStatuses.includes(status)) {
            res.status(400).json({ error: 'Trạng thái không hợp lệ' });
            return;
        }
        /*----------------------------------
        
            Find order by id
        -----------------------------------*/
        const [orders] = yield database_1.default.execute('SELECT id, email, product_title FROM orders WHERE id = ?', [id]);
        if (!orders || orders.length === 0) {
            res.status(404).json({ error: 'Không tìm thấy đơn hàng' });
            return;
        }
        const order = orders[0];
        /*----------------------------------
        Check for email
        -----------------------------------*/
        if (!order.email) {
            console.error('Email not found for order:', order);
            res.status(400).json({ error: 'Thiếu thông tin email trong đơn hàng' });
            return;
        }
        /*----------------------------------
        Update status
        -----------------------------------*/
        yield database_1.default.execute('UPDATE orders SET status = ? WHERE id = ?', [status, id]);
        /*----------------------------------
        Create notifications
        -----------------------------------*/
        yield database_1.default.execute('INSERT INTO notifications (user_email, title, message, is_read) VALUES (?, ?, ?, FALSE)', [
            order.email,
            `Cập nhật đơn hàng: ${order.product_title}`,
            `Đơn hàng của bạn đã được cập nhật sang trạng thái: ${status}`
        ]);
        res.json({
            success: true,
            message: 'Cập nhật trạng thái thành công',
            order: Object.assign(Object.assign({}, order), { status })
        });
    }
    catch (error) {
        console.error('Lỗi khi cập nhật trạng thái đơn hàng:', error);
        res.status(500).json({
            error: 'Lỗi server',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.updateOrderStatus = updateOrderStatus;
const getUserOrders = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email } = req.params;
        const [orders] = yield database_1.default.execute('SELECT * FROM orders WHERE email = ? ORDER BY created_at DESC', [email]);
        res.status(200).json(orders);
    }
    catch (error) {
        console.error('Lỗi khi lấy đơn hàng của user:', error);
        res.status(500).json({ error: 'Lỗi server' });
    }
});
exports.getUserOrders = getUserOrders;
