"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authController_1 = __importDefault(require("../controllers/authController"));
const auth_1 = require("../types/auth");
const router = (0, express_1.Router)();
/*----------------------------------
Register Router
-----------------------------------*/
router.post('/signup', (req, res, next) => {
    authController_1.default.signup(req, res, next).catch(next);
});
/*----------------------------------
Login Router
-----------------------------------*/
router.post('/login', (req, res, next) => {
    authController_1.default.login(req, res, next).catch(next);
});
/*----------------------------------
Profile Routes - Add GET route
-----------------------------------*/
router.get('/profile', auth_1.auth, (req, res, next) => {
    authController_1.default.getProfile(req, res, next).catch(next);
});
router.put('/profile', auth_1.auth, (req, res, next) => {
    authController_1.default.updateProfile(req, res, next).catch(next);
});
router.put('/change-password', auth_1.auth, (req, res, next) => {
    authController_1.default.changePassword(req, res, next).catch(next);
});
exports.default = router;
