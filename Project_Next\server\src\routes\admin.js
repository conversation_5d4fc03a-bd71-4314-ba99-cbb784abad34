"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const database_1 = __importDefault(require("../config/database"));
const adminAuth_1 = require("../middleware/adminAuth");
const router = (0, express_1.Router)();
// Lấy danh sách yêu cầu nạp tiền
router.get('/deposits', adminAuth_1.adminAuth, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const [rows] = yield database_1.default.execute(`
            SELECT dr.*, u.email as user_email 
            FROM deposit_requests dr 
            JOIN users u ON dr.user_id = u.id 
            ORDER BY dr.created_at DESC
        `);
        res.json(rows);
    }
    catch (error) {
        console.error('Lỗi lấy danh sách nạp tiền:', error);
        res.status(500).json({ error: 'Lỗi server' });
    }
}));
// Duyệt yêu cầu nạp tiền
router.put('/deposits/:id/approve', adminAuth_1.adminAuth, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        // Lấy thông tin deposit request
        const [deposits] = yield database_1.default.execute('SELECT * FROM deposit_requests WHERE id = ? AND status = "pending"', [id]);
        if (deposits.length === 0) {
            res.status(404).json({ error: 'Không tìm thấy yêu cầu hoặc đã được xử lý' });
            return;
        }
        const deposit = deposits[0];
        // Bắt đầu transaction
        yield database_1.default.execute('START TRANSACTION');
        try {
            // Cập nhật trạng thái deposit
            yield database_1.default.execute('UPDATE deposit_requests SET status = "approved" WHERE id = ?', [id]);
            // Tạo ví nếu chưa có
            yield database_1.default.execute('INSERT IGNORE INTO wallets (user_id, balance) VALUES (?, 0)', [deposit.user_id]);
            // Cập nhật số dư ví
            yield database_1.default.execute('UPDATE wallets SET balance = balance + ? WHERE user_id = ?', [deposit.amount, deposit.user_id]);
            // Commit transaction
            yield database_1.default.execute('COMMIT');
            console.log(`Đã duyệt nạp tiền: User ${deposit.user_id}, Amount: ${deposit.amount}`);
            res.json({ success: true });
        }
        catch (error) {
            // Rollback nếu có lỗi
            yield database_1.default.execute('ROLLBACK');
            throw error;
        }
    }
    catch (error) {
        console.error('Lỗi duyệt yêu cầu:', error);
        res.status(500).json({ error: 'Lỗi server' });
    }
}));
// Từ chối yêu cầu nạp tiền
router.put('/deposits/:id/reject', adminAuth_1.adminAuth, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        yield database_1.default.execute('UPDATE deposit_requests SET status = "rejected" WHERE id = ?', [id]);
        res.json({ success: true });
    }
    catch (error) {
        console.error('Lỗi từ chối yêu cầu:', error);
        res.status(500).json({ error: 'Lỗi server' });
    }
}));
exports.default = router;
