{"name": "client", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@google/genai": "^0.9.0", "@headlessui/react": "^2.2.1", "@heroicons/react": "^2.2.0", "axios": "^1.8.4", "client": "file:", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.487.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "tw-elements": "^2.0.0", "tw-elements-react": "^1.0.0-alpha-end"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/postcss": "^4.1.3", "@types/jsonwebtoken": "^9.0.9", "@types/react": "^19.1.0", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.5"}, "ports": ["3000:3306"]}