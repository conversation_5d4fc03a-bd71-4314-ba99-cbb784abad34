{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon src/index.ts", "start": "node dist/index.js", "build": "tsc"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@google/generative-ai": "^0.24.0", "@types/cors": "^2.8.17", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.0", "nodemon": "^3.1.9", "prisma": "^6.6.0", "server": "file:", "ts-node": "^10.9.2"}, "devDependencies": {"@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.14.1", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}