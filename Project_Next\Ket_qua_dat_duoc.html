<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kết quả đ<PERSON>t đ<PERSON> - <PERSON><PERSON> thống <PERSON> mạ<PERSON> tử <PERSON>uần áo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 60px;
            max-width: 1200px;
            width: 90%;
            animation: slideIn 1s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
        }

        .header h1 {
            font-size: 3em;
            color: #2c3e50;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2em;
            color: #7f8c8d;
            font-weight: 300;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .result-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px;
            border-left: 5px solid #667eea;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .result-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }

        .result-card h3 {
            color: #2c3e50;
            font-size: 1.4em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .result-card .icon {
            font-size: 1.5em;
            margin-right: 10px;
            color: #667eea;
        }

        .result-card ul {
            list-style: none;
            padding-left: 0;
        }

        .result-card li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
            color: #555;
        }

        .result-card li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #27ae60;
            font-weight: bold;
        }

        .tech-stack {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }

        .tech-stack h3 {
            font-size: 1.6em;
            margin-bottom: 20px;
            text-align: center;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .tech-category {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }

        .tech-category h4 {
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .tech-category p {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .metric {
            text-align: center;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .metric-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            display: block;
        }

        .metric-label {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid #eee;
        }

        .footer p {
            color: #7f8c8d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="header">
            <h1>🎯 KẾT QUẢ ĐẠT ĐƯỢC</h1>
            <p>Hệ thống Thương mại Điện tử Quần áo Hoàn chỉnh</p>
        </div>

        <div class="results-grid">
            <div class="result-card">
                <h3><span class="icon">🛍️</span>Chức năng Thương mại Điện tử</h3>
                <ul>
                    <li>Hệ thống đăng ký/đăng nhập với JWT</li>
                    <li>Quản lý sản phẩm và danh mục</li>
                    <li>Giỏ hàng và thanh toán</li>
                    <li>Lịch sử đơn hàng</li>
                    <li>Flash Sale và khuyến mãi</li>
                    <li>Tìm kiếm và lọc sản phẩm</li>
                </ul>
            </div>

            <div class="result-card">
                <h3><span class="icon">🤖</span>Tính năng AI & Chatbot</h3>
                <ul>
                    <li>Chatbot tích hợp Google Gemini AI</li>
                    <li>Tư vấn sản phẩm thông minh</li>
                    <li>Xử lý ngôn ngữ tự nhiên</li>
                    <li>Hỗ trợ khách hàng 24/7</li>
                    <li>Tìm kiếm sản phẩm bằng AI</li>
                </ul>
            </div>

            <div class="result-card">
                <h3><span class="icon">👨‍💼</span>Hệ thống Quản trị</h3>
                <ul>
                    <li>Dashboard quản lý admin</li>
                    <li>Quản lý đơn hàng</li>
                    <li>Quản lý người dùng</li>
                    <li>Hệ thống thông báo</li>
                    <li>Quản lý hỗ trợ khách hàng</li>
                    <li>Báo cáo và thống kê</li>
                </ul>
            </div>

            <div class="result-card">
                <h3><span class="icon">💳</span>Hệ thống Thanh toán</h3>
                <ul>
                    <li>Ví điện tử tích hợp</li>
                    <li>Quản lý số dư</li>
                    <li>Lịch sử giao dịch</li>
                    <li>Bảo mật thanh toán</li>
                    <li>Nhiều phương thức thanh toán</li>
                </ul>
            </div>

            <div class="result-card">
                <h3><span class="icon">📱</span>Giao diện Người dùng</h3>
                <ul>
                    <li>Responsive Design</li>
                    <li>UI/UX hiện đại với Tailwind CSS</li>
                    <li>Trang chủ với carousel</li>
                    <li>Trang chi tiết sản phẩm</li>
                    <li>Profile và cài đặt cá nhân</li>
                    <li>Thông báo real-time</li>
                </ul>
            </div>

            <div class="result-card">
                <h3><span class="icon">🔧</span>Hạ tầng Kỹ thuật</h3>
                <ul>
                    <li>Containerization với Docker</li>
                    <li>Database MySQL với phpMyAdmin</li>
                    <li>API RESTful hoàn chỉnh</li>
                    <li>TypeScript cho type safety</li>
                    <li>Middleware bảo mật</li>
                    <li>Environment configuration</li>
                </ul>
            </div>
        </div>

        <div class="tech-stack">
            <h3>🛠️ CÔNG NGHỆ SỬ DỤNG</h3>
            <div class="tech-grid">
                <div class="tech-category">
                    <h4>Frontend</h4>
                    <p>React 19, TypeScript, Tailwind CSS, Vite</p>
                </div>
                <div class="tech-category">
                    <h4>Backend</h4>
                    <p>Node.js, Express, TypeScript, JWT</p>
                </div>
                <div class="tech-category">
                    <h4>Database</h4>
                    <p>MySQL 8.0, Prisma ORM</p>
                </div>
                <div class="tech-category">
                    <h4>AI/ML</h4>
                    <p>Google Gemini AI, Natural Language Processing</p>
                </div>
                <div class="tech-category">
                    <h4>DevOps</h4>
                    <p>Docker, Docker Compose, Multi-container</p>
                </div>
                <div class="tech-category">
                    <h4>Tools</h4>
                    <p>phpMyAdmin, Nodemon, ESLint</p>
                </div>
            </div>
        </div>

        <div class="metrics">
            <div class="metric">
                <span class="metric-number">15+</span>
                <div class="metric-label">API Endpoints</div>
            </div>
            <div class="metric">
                <span class="metric-number">20+</span>
                <div class="metric-label">React Components</div>
            </div>
            <div class="metric">
                <span class="metric-number">6</span>
                <div class="metric-label">Microservices</div>
            </div>
            <div class="metric">
                <span class="metric-number">100%</span>
                <div class="metric-label">Responsive Design</div>
            </div>
        </div>

        <div class="footer">
            <p>✨ Dự án hoàn thành với đầy đủ tính năng của một hệ thống thương mại điện tử hiện đại ✨</p>
        </div>
    </div>
</body>
</html>
