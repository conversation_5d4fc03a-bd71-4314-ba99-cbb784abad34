"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const database_1 = __importDefault(require("../config/database"));
const auth_1 = require("../types/auth");
const router = (0, express_1.Router)();
// Lấy thông tin ví
router.get('/info', auth_1.auth, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.userId;
        const [rows] = yield database_1.default.execute('SELECT * FROM wallets WHERE user_id = ?', [userId]);
        if (rows.length === 0) {
            // Tạo ví mới nếu chưa có
            yield database_1.default.execute('INSERT INTO wallets (user_id, balance) VALUES (?, 0)', [userId]);
            res.json({ wallet: { balance: 0 } });
        }
        else {
            res.json({ wallet: rows[0] });
        }
    }
    catch (error) {
        console.error('Lỗi lấy thông tin ví:', error);
        res.status(500).json({ error: 'Lỗi server' });
    }
}));
// Tạo yêu cầu nạp tiền
router.post('/deposit', auth_1.auth, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { amount } = req.body;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.userId;
        if (!amount || amount <= 0) {
            res.status(400).json({ error: 'Số tiền không hợp lệ' });
            return;
        }
        const transferCode = `NAP${userId}${Date.now()}`;
        yield database_1.default.execute('INSERT INTO deposit_requests (user_id, amount, transfer_code, bank_account, status) VALUES (?, ?, ?, ?, "pending")', [userId, amount, transferCode, 'Vietcombank - ********** - CAO TRẦN TRỌNG HIẾU']);
        res.json({
            success: true,
            transferCode,
            bankInfo: {
                bank: 'Vietcombank',
                accountNumber: '**********',
                accountName: 'CAO TRẦN TRỌNG HIẾU'
            }
        });
    }
    catch (error) {
        console.error('Lỗi tạo yêu cầu nạp tiền:', error);
        res.status(500).json({ error: 'Lỗi server' });
    }
}));
// Thêm route GET /api/wallet để lấy thông tin ví
router.get('/', auth_1.auth, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.userId;
        // Tạo ví nếu chưa có
        yield database_1.default.execute('INSERT IGNORE INTO wallets (user_id, balance) VALUES (?, 0)', [userId]);
        // Lấy thông tin ví
        const [wallets] = yield database_1.default.execute('SELECT * FROM wallets WHERE user_id = ?', [userId]);
        // Lấy lịch sử nạp tiền
        const [deposits] = yield database_1.default.execute('SELECT * FROM deposit_requests WHERE user_id = ? ORDER BY created_at DESC', [userId]);
        res.json({
            wallet: wallets[0],
            depositHistory: deposits
        });
    }
    catch (error) {
        console.error('Lỗi lấy thông tin ví:', error);
        res.status(500).json({ error: 'Lỗi server' });
    }
}));
exports.default = router;
