{"ALL_COMPILER_OPTIONS_6917": "所有編譯器選項", "A_0_modifier_cannot_be_used_with_an_import_declaration_1079": "'{0}' 修飾元無法與匯入宣告並用。", "A_0_parameter_must_be_the_first_parameter_2680": "'{0}' 參數必須為第一個參數。", "A_JSDoc_template_tag_may_not_follow_a_typedef_callback_or_overload_tag_8039": "JSDoc '@template' 標籤不能接在 '@typedef'、'@callback' 或 '@overload' 標籤後面", "A_JSDoc_typedef_comment_may_not_contain_multiple_type_tags_8033": "JSDoc '@typedef' 註解不能包含多個 '@type' 標籤。", "A_bigint_literal_cannot_be_used_as_a_property_name_1539": "無法使用 'bigint' 常值做為屬性名稱。", "A_bigint_literal_cannot_use_exponential_notation_1352": "Bigint 常值不可使用指數標記法。", "A_bigint_literal_must_be_an_integer_1353": "Bigint 常值必須為整數。", "A_binding_pattern_parameter_cannot_be_optional_in_an_implementation_signature_2463": "實作簽章中不得省略繫結模式參數。", "A_break_statement_can_only_be_used_within_an_enclosing_iteration_or_switch_statement_1105": "'break' 陳述式只可在封入的反覆項目或 switch 陳述式內使用。", "A_break_statement_can_only_jump_to_a_label_of_an_enclosing_statement_1116": "'break' 陳述式只可跳至封入之陳述式的標籤。", "A_character_class_must_not_contain_a_reserved_double_punctuator_Did_you_mean_to_escape_it_with_backs_1522": "字元類別不得包含保留的雙標點符號。您是要使用反斜線將其逸出嗎?", "A_character_class_range_must_not_be_bounded_by_another_character_class_1516": "字元類別範圍不得被另一個字元類別限制。", "A_class_can_only_implement_an_identifier_Slashqualified_name_with_optional_type_arguments_2500": "類別只能實作具有選擇性型別引數的識別碼/限定名稱。", "A_class_can_only_implement_an_object_type_or_intersection_of_object_types_with_statically_known_memb_2422": "類別只能使用靜態已知成員來實作物件類型或物件類型的交集。", "A_class_cannot_extend_a_primitive_type_like_0_Classes_can_only_extend_constructable_values_2863": "無法延伸基本類型的類別，例如 '{0}'。類別只能延伸可建構值。", "A_class_cannot_implement_a_primitive_type_like_0_It_can_only_implement_other_named_object_types_2864": "無法實作基本類型的類別，例如 '{0}'。該類別只能實作其他具名物件類型。", "A_class_declaration_without_the_default_modifier_must_have_a_name_1211": "不具 'default' 修飾元的類別宣告必須要有名稱。", "A_class_member_cannot_have_the_0_keyword_1248": "類別成員不能含有 '{0}' 關鍵字。", "A_comma_expression_is_not_allowed_in_a_computed_property_name_1171": "計算的屬性名稱中不可有逗點運算式。", "A_computed_property_name_cannot_reference_a_type_parameter_from_its_containing_type_2467": "計算的屬性名稱不得參考其包含類型中的型別參數。", "A_computed_property_name_in_a_class_property_declaration_must_have_a_simple_literal_type_or_a_unique_1166": "類別屬性宣告中的已計算屬性名稱必須具有簡單常值型別或 'unique symbol' 型別。", "A_computed_property_name_in_a_method_overload_must_refer_to_an_expression_whose_type_is_a_literal_ty_1168": "方法多載中的計算屬性名稱必須參考型別為常值型別或 'unique symbol' 型別的運算式。", "A_computed_property_name_in_a_type_literal_must_refer_to_an_expression_whose_type_is_a_literal_type__1170": "常值型別中的計算屬性名稱，必須參考類型為常值型別或 'unique symbol' 類型的運算式。", "A_computed_property_name_in_an_ambient_context_must_refer_to_an_expression_whose_type_is_a_literal_t_1165": "環境內容中的計算屬性名稱必須參考型別為常值型別或 'unique symbol' 型別的運算式。", "A_computed_property_name_in_an_interface_must_refer_to_an_expression_whose_type_is_a_literal_type_or_1169": "介面中的計算屬性名稱必須參考型別為常值型別或 'unique symbol' 型別的運算式。", "A_computed_property_name_must_be_of_type_string_number_symbol_or_any_2464": "計算的屬性名稱必須是 'string'、'number'、'symbol' 或 'any' 類型。", "A_const_assertions_can_only_be_applied_to_references_to_enum_members_or_string_number_boolean_array__1355": "'const' 判斷提示只可套用至列舉成員、字串、數字、布林值、陣列或物件常值的參考。", "A_const_enum_member_can_only_be_accessed_using_a_string_literal_2476": "若要存取常數列舉成員，必須透過字串常值。", "A_const_initializer_in_an_ambient_context_must_be_a_string_or_numeric_literal_or_literal_enum_refere_1254": "環境內容中的 'const' 初始設定式必須為字串、數字常值或常值列舉參考。", "A_constructor_cannot_contain_a_super_call_when_its_class_extends_null_17005": "當建構函式的類別擴充為 'null' 時，不得包含 'super' 呼叫。", "A_constructor_cannot_have_a_this_parameter_2681": "建構函式不能含有 'this' 參數。", "A_continue_statement_can_only_be_used_within_an_enclosing_iteration_statement_1104": "'continue' 陳述式只可在封入的反覆項目陳述式內使用。", "A_continue_statement_can_only_jump_to_a_label_of_an_enclosing_iteration_statement_1115": "'continue' 陳述式只可跳至封入之反覆項目陳述式的標籤。", "A_declaration_file_cannot_be_imported_without_import_type_Did_you_mean_to_import_an_implementation_f_2846": "沒有 'import type' 就無法匯入宣告檔案。您是否想要改為匯入實作檔案 '{0}'？", "A_declare_modifier_cannot_be_used_in_an_already_ambient_context_1038": "不得在現有環境內容中使用 'declare' 修飾元。", "A_decorator_can_only_decorate_a_method_implementation_not_an_overload_1249": "一個裝飾項目只能裝飾一項方法實作，而不能多載。", "A_default_clause_cannot_appear_more_than_once_in_a_switch_statement_1113": "'default' 子句在 'switch' 陳述式中不得出現一次以上。", "A_default_export_can_only_be_used_in_an_ECMAScript_style_module_1319": "預設匯出只能在 ECMAScript 樣式的模組中使用。", "A_default_export_must_be_at_the_top_level_of_a_file_or_module_declaration_1258": "預設匯出必須位於檔案或模組宣告的最上層。", "A_definite_assignment_assertion_is_not_permitted_in_this_context_1255": "此內容不允許明確的指派判斷提示 '!'。", "A_destructuring_declaration_must_have_an_initializer_1182": "解構宣告中必須包含初始設定式。", "A_dynamic_import_call_in_ES5_requires_the_Promise_constructor_Make_sure_you_have_a_declaration_for_t_2712": "ES5 中的動態匯入呼叫需要 'Promise' 建構函式。請確認您有 'Promise' 建構函式的宣告，或在 '--lib' 選項中包括 'ES2015'。", "A_dynamic_import_call_returns_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_include_ES20_2711": "動態匯入呼叫傳回 'Promise'。請確認您有 'Promise' 的宣告，或在 '--lib' 選項中包括 'ES2015'。", "A_file_cannot_have_a_reference_to_itself_1006": "檔案不得參考自己。", "A_function_returning_never_cannot_have_a_reachable_end_point_2534": "會傳回 'never' 的功能不得具有可聯繫的端點。", "A_function_that_is_called_with_the_new_keyword_cannot_have_a_this_type_that_is_void_2679": "透過 'new' 關鍵字呼叫的函式不能含有為 'viod' 的 'this' 類型。", "A_function_whose_declared_type_is_neither_undefined_void_nor_any_must_return_a_value_2355": "若函式的宣告類型既不是 'undefined'、'void' 也不是 'any'，則必須傳回值。", "A_generator_cannot_have_a_void_type_annotation_2505": "產生器不得有 'void' 類型註釋。", "A_get_accessor_cannot_have_parameters_1054": "'get' 存取子不得有參數。", "A_get_accessor_must_be_at_least_as_accessible_as_the_setter_2808": "get 存取子必須至少要跟 setter 一樣可供存取", "A_get_accessor_must_return_a_value_2378": "'get' 存取子必須傳回值。", "A_label_is_not_allowed_here_1344": "此處不允許標籤。", "A_labeled_tuple_element_is_declared_as_optional_with_a_question_mark_after_the_name_and_before_the_c_5086": "標記的元組元素已宣告為選用，並在名稱之後、冒號之前加上問號，而非加在類型之後。", "A_labeled_tuple_element_is_declared_as_rest_with_a_before_the_name_rather_than_before_the_type_5087": "標記的元組元素已宣告為待用，並在名稱之前加上「...」，而非加在類型之前。", "A_mapped_type_may_not_declare_properties_or_methods_7061": "對應型別不能宣告屬性或方法。", "A_member_initializer_in_a_enum_declaration_cannot_reference_members_declared_after_it_including_memb_2651": "列舉宣告中的成員初始設定式，不得參考在它之後宣告的成員，包括在其他列舉中所定義的成員。", "A_mixin_class_must_have_a_constructor_with_a_single_rest_parameter_of_type_any_2545": "mixin 類別必須具備建構函式，且該建構函式必須指定一個類型為 'any[]' 的 rest 參數。", "A_mixin_class_that_extends_from_a_type_variable_containing_an_abstract_construct_signature_must_also_2797": "從包含抽象建構簽章之類型變數所延伸的 mixin 類別也必須宣告為 'abstract'。", "A_module_cannot_have_multiple_default_exports_2528": "一個模組不得有多個預設匯出。", "A_namespace_declaration_cannot_be_in_a_different_file_from_a_class_or_function_with_which_it_is_merg_2433": "命名空間宣告的所在檔案位置，不得與其要合併的類別或函式不同。", "A_namespace_declaration_cannot_be_located_prior_to_a_class_or_function_with_which_it_is_merged_2434": "命名空間宣告的位置不得先於其要合併的類別或函式。", "A_namespace_declaration_is_only_allowed_at_the_top_level_of_a_namespace_or_module_1235": "命名空間宣告只允許在命名空間或模組的頂層。", "A_namespace_declaration_should_not_be_declared_using_the_module_keyword_Please_use_the_namespace_key_1540": "不應該使用 'module' 關鍵字宣告 'namespace' 宣告。請改為使用 'namespace' 關鍵字。", "A_non_dry_build_would_build_project_0_6357": "非 -dry 組建會建置專案 '{0}'", "A_non_dry_build_would_delete_the_following_files_Colon_0_6356": "非 -dry 組建會刪除下列檔案: {0}", "A_non_dry_build_would_update_timestamps_for_output_of_project_0_6374": "非 DRY 組建將會更新專案 '{0}' 輸出的時間戳記", "A_parameter_initializer_is_only_allowed_in_a_function_or_constructor_implementation_2371": "只有函式或建構函式實作才可使用參數初始設定式。", "A_parameter_property_cannot_be_declared_using_a_rest_parameter_1317": "無法使用剩餘參數宣告參數屬性。", "A_parameter_property_is_only_allowed_in_a_constructor_implementation_2369": "建構函式實作中只可有一個參數屬性。", "A_parameter_property_may_not_be_declared_using_a_binding_pattern_1187": "無法使用繫結模式宣告參數屬性。", "A_promise_must_have_a_then_method_1059": "Promise 必須有 'then' 方法。", "A_property_of_a_class_whose_type_is_a_unique_symbol_type_must_be_both_static_and_readonly_1331": "類型為 'unique symbol' 類型的類別屬性，必須為 'static' 和 'readonly'。", "A_property_of_an_interface_or_type_literal_whose_type_is_a_unique_symbol_type_must_be_readonly_1330": "類型為 'unique symbol' 類型之介面或常值型別的屬性，必須是 'readonly'。", "A_required_element_cannot_follow_an_optional_element_1257": "必要項目不可接在選擇性項目後面。", "A_required_parameter_cannot_follow_an_optional_parameter_1016": "必要參數不得接在選擇性參數之後。", "A_rest_element_cannot_contain_a_binding_pattern_2501": "剩餘項目不得包含繫結模式。", "A_rest_element_cannot_follow_another_rest_element_1265": "REST 元素不能跟在另一個 REST 元素之後。", "A_rest_element_cannot_have_a_property_name_2566": "REST 元素不得有屬性名稱。", "A_rest_element_cannot_have_an_initializer_1186": "剩餘項目不得有初始設定式。", "A_rest_element_must_be_last_in_a_destructuring_pattern_2462": "Rest 項目必須保持在解構模式。", "A_rest_element_type_must_be_an_array_type_2574": "其餘項目類型必須為陣列類型。", "A_rest_parameter_cannot_be_optional_1047": "剩餘參數不得為選擇性參數。", "A_rest_parameter_cannot_have_an_initializer_1048": "剩餘參數不得有初始設定式。", "A_rest_parameter_must_be_last_in_a_parameter_list_1014": "剩餘參數必須是參數清單中的最後一個參數。", "A_rest_parameter_must_be_of_an_array_type_2370": "剩餘參數必須為陣列類型。", "A_rest_parameter_or_binding_pattern_may_not_have_a_trailing_comma_1013": "REST 參數或繫結模式的結尾不得為逗點。", "A_return_statement_can_only_be_used_within_a_function_body_1108": "'return' 陳述式只可在函式主體內使用。", "A_return_statement_cannot_be_used_inside_a_class_static_block_18041": "'return' 陳述式無法在類別靜態區塊內使用。", "A_series_of_entries_which_re_map_imports_to_lookup_locations_relative_to_the_baseUrl_6167": "一系列由重新對應匯入到 'baseUrl' 之相對查詢位置的項目。", "A_set_accessor_cannot_have_a_return_type_annotation_1095": "'set' 存取子不得有傳回型別註解。", "A_set_accessor_cannot_have_an_optional_parameter_1051": "'set' 存取子不得有選擇性參數。", "A_set_accessor_cannot_have_rest_parameter_1053": "'set' 存取子不得有剩餘參數。", "A_set_accessor_must_have_exactly_one_parameter_1049": "'set' 存取子只可有一個參數。", "A_set_accessor_parameter_cannot_have_an_initializer_1052": "'set' 存取子參數不得有初始設定式。", "A_spread_argument_must_either_have_a_tuple_type_or_be_passed_to_a_rest_parameter_2556": "擴張引數必須具有元組類型或傳遞給 REST 參數。", "A_super_call_must_be_a_root_level_statement_within_a_constructor_of_a_derived_class_that_contains_in_2401": "'super' 呼叫必須是衍生類別 (包含初始化屬性、參數屬性或私人識別碼) 建構函式內的根等級陳述式。", "A_super_call_must_be_the_first_statement_in_the_constructor_to_refer_to_super_or_this_when_a_derived_2376": "當衍生類別包含已初始化的屬性、參數屬性或私人識別碼時，'super' 呼叫必須為建構函式中第一個參照 'super' 或 'this' 的陳述式。", "A_this_based_type_guard_is_not_compatible_with_a_parameter_based_type_guard_2518": "以 'this' 為基礎的類型成立條件，和以參數為基礎的類型成立條件不相容。", "A_this_type_is_available_only_in_a_non_static_member_of_a_class_or_interface_2526": "'this' 類型只適用於類別或介面的非靜態成員。", "A_top_level_export_modifier_cannot_be_used_on_value_declarations_in_a_CommonJS_module_when_verbatimM_1287": "啟用 'verbatimModuleSyntax' 時，CommonJS 模組中的值宣告不得使用最上層 'export' 修飾元。", "A_tsconfig_json_file_is_already_defined_at_Colon_0_5054": "'tsconfig.json' 檔案已定義於: '{0}'。", "A_tuple_member_cannot_be_both_optional_and_rest_5085": "元組成員不能同時為選用及待用。", "A_tuple_type_cannot_be_indexed_with_a_negative_value_2514": "元組類型無法以負值編製索引。", "A_type_assertion_expression_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_expression_Con_17007": "乘冪運算式左邊不允許類型宣告運算式。請考慮以括弧括住運算式。", "A_type_literal_property_cannot_have_an_initializer_1247": "類型常值屬性不得有初始設定式。", "A_type_only_import_can_specify_a_default_import_or_named_bindings_but_not_both_1363": "僅限類型的匯入可以指定預設匯入或具名繫結，但不能同時指定兩者。", "A_type_predicate_cannot_reference_a_rest_parameter_1229": "型別述詞不得參考剩餘參數。", "A_type_predicate_cannot_reference_element_0_in_a_binding_pattern_1230": "型別述詞不得參考繫結模式的項目 '{0}'。", "A_type_predicate_is_only_allowed_in_return_type_position_for_functions_and_methods_1228": "只有函式及方法的傳回型別位置才允許型別述詞。", "A_type_predicate_s_type_must_be_assignable_to_its_parameter_s_type_2677": "類型述詞的類型必須可指派給其參數的類型。", "A_type_referenced_in_a_decorated_signature_must_be_imported_with_import_type_or_a_namespace_import_w_1272": "啟用 'isolatedModules' 和 'emitDecoratorMetadata' 時，修飾簽章中參考的類型必須以 'import type' 或命名空間匯入來匯入。", "A_variable_whose_type_is_a_unique_symbol_type_must_be_const_1332": "型別為 'unique symbol' 型別的變數必須是 'const'。", "A_yield_expression_is_only_allowed_in_a_generator_body_1163": "只有產生器主體才允許 'yield' 運算式。", "Abstract_method_0_in_class_1_cannot_be_accessed_via_super_expression_2513": "無法透過 super 運算式存取類別 '{1}' 中的抽象方法 '{0}'。", "Abstract_methods_can_only_appear_within_an_abstract_class_1244": "抽象方法只可出現在抽象類別中。", "Abstract_properties_can_only_appear_within_an_abstract_class_1253": "抽象方法只可出現在抽象屬性中。", "Abstract_property_0_in_class_1_cannot_be_accessed_in_the_constructor_2715": "無法從建構函式存取類別 '{1}' 中的抽象屬性 '{0}'。", "Accessibility_modifier_already_seen_1028": "已有存取範圍修飾元。", "Accessors_are_only_available_when_targeting_ECMAScript_5_and_higher_1056": "只有當目標為 ECMAScript 5 及更高版本時，才可使用存取子。", "Accessors_must_both_be_abstract_or_non_abstract_2676": "存取子必須兩者均為抽象或非抽象。", "Add_0_to_unresolved_variable_90008": "對未解析的變數新增 '{0}.'", "Add_a_return_statement_95111": "新增 return 陳述式", "Add_a_return_type_to_the_function_declaration_9031": "新增傳回類型到函數宣告。", "Add_a_return_type_to_the_function_expression_9030": "新增傳回類型到函數運算式。", "Add_a_return_type_to_the_get_accessor_declaration_9032": "新增傳回類型至 get 存取子宣告。", "Add_a_return_type_to_the_method_9034": "新增傳回類型至方法", "Add_a_type_annotation_to_the_parameter_0_9028": "新增類型註釋至參數 {0}。", "Add_a_type_annotation_to_the_property_0_9029": "新增類型註釋至屬性 {0}。", "Add_a_type_annotation_to_the_variable_0_9027": "新增類型註釋至變數 {0}。", "Add_a_type_to_parameter_of_the_set_accessor_declaration_9033": "將類型新增至集合存取子宣告的參數。", "Add_all_missing_async_modifiers_95041": "新增缺少的所有 'async' 修飾元", "Add_all_missing_attributes_95168": "新增所有遺失的屬性", "Add_all_missing_call_parentheses_95068": "新增所有缺少的呼叫括號", "Add_all_missing_function_declarations_95157": "新增所有缺少的函式宣告", "Add_all_missing_imports_95064": "新增所有缺少的匯入", "Add_all_missing_members_95022": "新增遺漏的所有成員", "Add_all_missing_override_modifiers_95162": "新增所有缺少的 'override' 修飾元", "Add_all_missing_parameters_95190": "新增所有遺失的參數", "Add_all_missing_properties_95166": "新增所有遺失的屬性", "Add_all_missing_return_statement_95114": "新增所有遺漏的 return 陳述式", "Add_all_missing_super_calls_95039": "新增缺少的所有 super 呼叫", "Add_all_missing_type_annotations_90067": "新增所有遺失的類型註釋", "Add_all_optional_parameters_95193": "新增所有選用參數", "Add_annotation_of_type_0_90062": "新增類型 '{0}' 的註釋", "Add_async_modifier_to_containing_function_90029": "將 async 修飾元新增至包含的函式", "Add_await_95083": "新增 'await'", "Add_await_to_initializer_for_0_95084": "將 'await' 新增至 '{0}' 的初始設定式", "Add_await_to_initializers_95089": "將 'await' 新增至初始設定式", "Add_braces_to_arrow_function_95059": "將大括號新增至箭號函式", "Add_const_to_all_unresolved_variables_95082": "將 'const' 新增至所有未解析的變數", "Add_const_to_unresolved_variable_95081": "將 'const' 新增至未解析的變數", "Add_definite_assignment_assertion_to_property_0_95020": "將明確指派判斷提示新增至屬性 '{0}'", "Add_definite_assignment_assertions_to_all_uninitialized_properties_95028": "為所有未初始化的屬性新增明確的指派判斷提示", "Add_export_to_make_this_file_into_a_module_95097": "新增 'export {}' 以將此檔案轉為模組", "Add_extends_constraint_2211": "新增 'extends' 限制式。", "Add_extends_constraint_to_all_type_parameters_2212": "將 'extends' 限制式新增至所有類型參數", "Add_import_from_0_90057": "從 \"{0}\" 新增匯入", "Add_index_signature_for_property_0_90017": "為屬性 '{0}' 新增索引簽章", "Add_initializer_to_property_0_95019": "將初始設定式新增至屬性 '{0}'", "Add_initializers_to_all_uninitialized_properties_95027": "為所有未初始化的屬性新增初始設定式", "Add_missing_attributes_95167": "新增遺失的屬性", "Add_missing_call_parentheses_95067": "新增缺少的呼叫括號", "Add_missing_comma_for_object_member_completion_0_95187": "為物件成員完成 '{0}' 新增遺漏的逗號。", "Add_missing_enum_member_0_95063": "新增缺少的列舉成員 '{0}'", "Add_missing_function_declaration_0_95156": "新增缺少的函式宣告 '{0}'", "Add_missing_new_operator_to_all_calls_95072": "將缺少的 'new' 運算子新增至所有呼叫", "Add_missing_new_operator_to_call_95071": "缺少的 'new' 運算子新增至呼叫", "Add_missing_parameter_to_0_95188": "新增遺失的參數至 '{0}'", "Add_missing_parameters_to_0_95189": "新增遺失的參數至 '{0}'", "Add_missing_properties_95165": "新增遺失的屬性", "Add_missing_super_call_90001": "新增遺漏的 'super()' 呼叫", "Add_missing_typeof_95052": "新增遺漏的 'typeof'", "Add_names_to_all_parameters_without_names_95073": "將名稱新增至所有沒有名稱的參數", "Add_optional_parameter_to_0_95191": "新增選用參數至 '{0}'", "Add_optional_parameters_to_0_95192": "將選用參數新增至 '{0}'", "Add_or_remove_braces_in_an_arrow_function_95058": "在箭號函式中新增或移除大括號", "Add_override_modifier_95160": "新增 'override' 修飾元", "Add_parameter_name_90034": "新增參數名稱", "Add_qualifier_to_all_unresolved_variables_matching_a_member_name_95037": "對所有比對成員名稱的未解析變數新增限定詞", "Add_resolution_mode_import_attribute_95196": "新增 'resolution-mode' 匯入屬性", "Add_resolution_mode_import_attribute_to_all_type_only_imports_that_need_it_95197": "將 'resolution-mode' 匯入屬性新增至所有需要該屬性的僅限類型匯入", "Add_return_type_0_90063": "新增傳回類型 '{0}'", "Add_satisfies_and_a_type_assertion_to_this_expression_satisfies_T_as_T_to_make_the_type_explicit_9035": "在此運算式中新增 satisfies 和類型判斷提示 (將 T 視為 T)，使類型明確顯示。", "Add_satisfies_and_an_inline_type_assertion_with_0_90068": "新增 satisfies 和具有 '{0}' 的內嵌類型判斷提示", "Add_to_all_uncalled_decorators_95044": "為所有未呼叫的裝飾項目新增 '()'", "Add_ts_ignore_to_all_error_messages_95042": "為所有錯誤訊息新增 '@ts-ignore'", "Add_undefined_to_a_type_when_accessed_using_an_index_6674": "使用索引進行存取時，將 'undefined' 新增至類型。", "Add_undefined_to_optional_property_type_95169": "將 'undefined' 新增至選擇性屬性類型", "Add_undefined_type_to_all_uninitialized_properties_95029": "為所有未初始化的屬性新增未定義的類型", "Add_undefined_type_to_property_0_95018": "將 'undefined' 類型新增至屬性 '{0}'", "Add_unknown_conversion_for_non_overlapping_types_95069": "新增非重疊類型的 'unknown' 轉換", "Add_unknown_to_all_conversions_of_non_overlapping_types_95070": "將 'unknown' 新增至非重疊類型的所有轉換", "Add_void_to_Promise_resolved_without_a_value_95143": "為已經解析但不具值的 Promise 新增 'void'", "Add_void_to_all_Promises_resolved_without_a_value_95144": "為已經解析但不具值的所有 Promise 新增 'void'", "Adding_a_tsconfig_json_file_will_help_organize_projects_that_contain_both_TypeScript_and_JavaScript__5068": "新增 tsconfig.json 檔案有助於組織同時包含 TypeScript 及 JavaScript 檔案的專案。若要深入了解，請前往 https://aka.ms/tsconfig。", "All_declarations_of_0_must_have_identical_constraints_2838": "'{0}' 的所有宣告都必須有相同的限制式。", "All_declarations_of_0_must_have_identical_modifiers_2687": "'{0}' 的所有宣告都必須有相同修飾元。", "All_declarations_of_0_must_have_identical_type_parameters_2428": "'{0}' 的所有宣告都必須具有相同的類型參數。", "All_declarations_of_an_abstract_method_must_be_consecutive_2516": "抽象方法的所有宣告必須連續。", "All_destructured_elements_are_unused_6198": "不會使用所有未經結構化的項目。", "All_imports_in_import_declaration_are_unused_6192": "匯入宣告中的所有匯入皆未使用。", "All_type_parameters_are_unused_6205": "未使用任何型別參數。", "All_variables_are_unused_6199": "所有變數都未使用。", "Allow_JavaScript_files_to_be_a_part_of_your_program_Use_the_checkJS_option_to_get_errors_from_these__6600": "允許 JavaScript 檔案成為您程式的一部分。使用 'checkJS' 選項可從這些檔案取得錯誤。", "Allow_accessing_UMD_globals_from_modules_6602": "允許從模組存取 UMD 全域。", "Allow_default_imports_from_modules_with_no_default_export_This_does_not_affect_code_emit_just_typech_6011": "允許從沒有預設匯出的模組進行預設匯入。這不會影響程式碼發出，僅為類型檢查。", "Allow_import_x_from_y_when_a_module_doesn_t_have_a_default_export_6601": "當模組沒有預設匯出時，允許 'import x from y'。", "Allow_importing_helper_functions_from_tslib_once_per_project_instead_of_including_them_per_file_6639": "允許每個專案只從 tslib 匯入協助程式函式，而不是每個檔案都包含這些函式。", "Allow_imports_to_include_TypeScript_file_extensions_Requires_moduleResolution_bundler_and_either_noE_6407": "允許匯入包含 TypeScript 延伸模組。需要設定 '--moduleResolution bundler' 和 '--noEmit' 或 '--emitDeclarationOnly'。", "Allow_javascript_files_to_be_compiled_6102": "允許編譯 JavaScript 檔案。", "Allow_multiple_folders_to_be_treated_as_one_when_resolving_modules_6691": "在解析模組時，允許將多個資料夾視為一個資料夾。", "Already_included_file_name_0_differs_from_file_name_1_only_in_casing_1261": "已包含的檔案名稱 '{0}' 與檔案名稱 '{1}' 僅大小寫不同。", "Ambient_module_declaration_cannot_specify_relative_module_name_2436": "環境模組宣告不可指定相對模組名稱。", "Ambient_modules_cannot_be_nested_in_other_modules_or_namespaces_2435": "環境模組不得以巢狀方式置於其他模組或命名空間中。", "An_AMD_module_cannot_have_multiple_name_assignments_2458": "AMD 模組不能有多個名稱指派。", "An_abstract_accessor_cannot_have_an_implementation_1318": "抽象存取子無法實作。", "An_accessibility_modifier_cannot_be_used_with_a_private_identifier_18010": "協助工具修飾元不可搭配私人識別碼使用。", "An_accessor_cannot_have_type_parameters_1094": "存取子不得有類型參數。", "An_accessor_property_cannot_be_declared_optional_1276": "無法將 'accessor' 屬性宣告為選擇性。", "An_ambient_module_declaration_is_only_allowed_at_the_top_level_in_a_file_1234": "環境模組宣告只可出現在檔案的最上層。", "An_argument_for_0_was_not_provided_6210": "未提供 '{0}' 的引數。", "An_argument_matching_this_binding_pattern_was_not_provided_6211": "未提供符合此繫結模式的引數。", "An_arithmetic_operand_must_be_of_type_any_number_bigint_or_an_enum_type_2356": "算術運算元必須屬於 'any'、'number'、'bigint' 或列舉類型。", "An_arrow_function_cannot_have_a_this_parameter_2730": "箭號函式不可具有 'this' 參數。", "An_async_function_or_method_in_ES5_requires_the_Promise_constructor_Make_sure_you_have_a_declaration_2705": "ES5 中的非同步函式或方法需要 'Promise' 建構函式。請確認您有 'Promise' 建構函式的宣告，或在 '--lib' 選項中包括 'ES2015'。", "An_async_function_or_method_must_return_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_in_2697": "非同步函式或方法必須傳回 'Promise'。請確定您有 'Promise' 的宣告或在 '--lib' 選項中包括 'ES2015'。", "An_async_iterator_must_have_a_next_method_2519": "非同步迭代器必須有 'next()' 方法。", "An_element_access_expression_should_take_an_argument_1011": "項目存取運算式應接受一個引數。", "An_enum_member_cannot_be_named_with_a_private_identifier_18024": "列舉成員不能以私人識別碼命名。", "An_enum_member_cannot_have_a_numeric_name_2452": "列舉成員不得有數值名稱。", "An_enum_member_name_must_be_followed_by_a_or_1357": "列舉成員名稱必須尾隨 ','、'=' 或 '}'。", "An_expanded_version_of_this_information_showing_all_possible_compiler_options_6928": "此資訊展開的版本，顯示所有可能的編譯器選項", "An_export_assignment_cannot_be_used_in_a_module_with_other_exported_elements_2309": "匯出指派不得用於具有其他匯出項目的模組中。", "An_export_assignment_cannot_be_used_in_a_namespace_1063": "命名空間中不可使用匯出指派。", "An_export_assignment_cannot_have_modifiers_1120": "匯出指派不得有修飾元。", "An_export_assignment_must_be_at_the_top_level_of_a_file_or_module_declaration_1231": "匯出指派必須位於檔案或模組宣告的最上層。", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_module_1474": "匯出宣告只能在模組的頂層使用。", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1233": "匯出宣告只能在命名空間或模組的頂層使用。", "An_export_declaration_cannot_have_modifiers_1193": "匯出宣告不得有修飾元。", "An_export_declaration_must_reference_a_real_value_when_verbatimModuleSyntax_is_enabled_but_0_resolve_1283": "啟用 'verbatimModuleSyntax' 時，'export =' 宣告必須參考真實的值，但 '{0}' 解析僅限類型的宣告。", "An_export_declaration_must_reference_a_value_when_verbatimModuleSyntax_is_enabled_but_0_only_refers__1282": "啟用 'verbatimModuleSyntax' 時，'export =' 宣告必須參考值，但 '{0}' 僅參考類型。", "An_export_default_must_reference_a_real_value_when_verbatimModuleSyntax_is_enabled_but_0_resolves_to_1285": "啟用 'verbatimModuleSyntax' 時，'export default' 必須參考真實的值，但 '{0}' 解析僅限類型的宣告。", "An_export_default_must_reference_a_value_when_verbatimModuleSyntax_is_enabled_but_0_only_refers_to_a_1284": "啟用 'verbatimModuleSyntax' 時，'export default' 宣告必須參考值，但 '{0}' 僅參考類型。", "An_expression_of_type_void_cannot_be_tested_for_truthiness_1345": "無法對 'void' 類型的運算式測試真實性。", "An_extended_Unicode_escape_value_must_be_between_0x0_and_0x10FFFF_inclusive_1198": "擴充的 Unicode 逸出值必須介於 0x0 與 0x10FFFF (不含) 之間。", "An_identifier_or_keyword_cannot_immediately_follow_a_numeric_literal_1351": "識別碼或關鍵字不可直接接在數字常值後面。", "An_implementation_cannot_be_declared_in_ambient_contexts_1183": "不得在環境內容中宣告實作。", "An_import_alias_cannot_reference_a_declaration_that_was_exported_using_export_type_1379": "匯入別名不能參考使用 'export type' 匯出的宣告。", "An_import_alias_cannot_reference_a_declaration_that_was_imported_using_import_type_1380": "匯入別名不能參考使用 'import type' 匯入的宣告。", "An_import_alias_cannot_resolve_to_a_type_or_type_only_declaration_when_verbatimModuleSyntax_is_enabl_1288": "啟用 'verbatimModuleSyntax' 時，無法解析匯入別名為類型或僅類型的宣告。", "An_import_alias_cannot_use_import_type_1392": "匯入別名不能使用 'import type'", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_module_1473": "匯入宣告只能在模組的頂層使用。", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1232": "匯入宣告只能在命名空間或模組的頂層使用。", "An_import_declaration_cannot_have_modifiers_1191": "匯入宣告不得有修飾元。", "An_import_path_can_only_end_with_a_0_extension_when_allowImportingTsExtensions_is_enabled_5097": "當 'allowImportingTsExtensions' 啟用時，匯入路徑只能以 '{0}' 延伸模組結尾。", "An_index_signature_cannot_have_a_rest_parameter_1017": "索引簽章不得有剩餘參數。", "An_index_signature_cannot_have_a_trailing_comma_1025": "索引簽章結尾不可有逗號。", "An_index_signature_must_have_a_type_annotation_1021": "索引簽章必須有類型註釋。", "An_index_signature_must_have_exactly_one_parameter_1096": "索引簽章只可有一個參數。", "An_index_signature_parameter_cannot_have_a_question_mark_1019": "索引簽章參數不得有問號。", "An_index_signature_parameter_cannot_have_an_accessibility_modifier_1018": "索引簽章參數不得有存取範圍修飾元。", "An_index_signature_parameter_cannot_have_an_initializer_1020": "索引簽章參數不得有初始設定式。", "An_index_signature_parameter_must_have_a_type_annotation_1022": "索引簽章參數必須有類型註釋。", "An_index_signature_parameter_type_cannot_be_a_literal_type_or_generic_type_Consider_using_a_mapped_o_1337": "索引簽章參數類型不能是常值型別或泛型型別。請考慮改用對應的物件類型。", "An_index_signature_parameter_type_must_be_string_number_symbol_or_a_template_literal_type_1268": "索引簽章參數類型必須是 'string'、'number'、'symbol' 或範本文字類型。", "An_instantiation_expression_cannot_be_followed_by_a_property_access_1477": "具現化運算式後面不能接著屬性存取。", "An_interface_can_only_extend_an_identifier_Slashqualified_name_with_optional_type_arguments_2499": "介面只能擴充具有選擇性型別引數的識別碼/限定名稱。", "An_interface_can_only_extend_an_object_type_or_intersection_of_object_types_with_statically_known_me_2312": "介面只能延伸物件類型或具有靜態已知成員的物件類型交集。", "An_interface_cannot_extend_a_primitive_type_like_0_It_can_only_extend_other_named_object_types_2840": "無法延伸基本類型的介面，例如 '{0}'。其只能延伸其他具名物件類型。", "An_interface_property_cannot_have_an_initializer_1246": "介面屬性不得有初始設定式。", "An_iterator_must_have_a_next_method_2489": "迭代器必須要有 'next()' 方法。", "An_jsxFrag_pragma_is_required_when_using_an_jsx_pragma_with_JSX_fragments_17017": "在 JSX 片段使用 @jsx pragma 時，必須有 @jsxFrag pragma。", "An_object_literal_cannot_have_multiple_get_Slashset_accessors_with_the_same_name_1118": "物件常值不得有多個同名的 get/set 存取子。", "An_object_literal_cannot_have_multiple_properties_with_the_same_name_1117": "物件常值不能有多個相同名稱的屬性。", "An_object_literal_cannot_have_property_and_accessor_with_the_same_name_1119": "物件常值不得有同名的屬性與存取子。", "An_object_member_cannot_be_declared_optional_1162": "不得將物件成員宣告為選擇性。", "An_object_s_Symbol_hasInstance_method_must_return_a_boolean_value_for_it_to_be_used_on_the_right_han_2861": "物件的 '[Symbol.hasInstance]' 方法必須傳回布林值，才能用於 『instanceof』 運算式的右側。", "An_optional_chain_cannot_contain_private_identifiers_18030": "選擇性鏈結不能包含私人識別碼。", "An_optional_element_cannot_follow_a_rest_element_1266": "選擇性元素不能跟在 REST 元素之後。", "An_outer_value_of_this_is_shadowed_by_this_container_2738": "此容器已陰影 'this' 的外部值。", "An_overload_signature_cannot_be_declared_as_a_generator_1222": "不可將多載簽章宣告為產生器。", "An_unary_expression_with_the_0_operator_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_ex_17006": "乘冪運算式左邊不允許具 '{0}' 運算子的一元運算式。請考慮以括弧括住運算式。", "Annotate_everything_with_types_from_JSDoc_95043": "標註具備 JSDoc 之類型的所有項目 ", "Annotate_types_of_properties_expando_function_in_a_namespace_90071": "為命名空間中的屬性 expando 函式類型加上標註", "Annotate_with_type_from_JSDoc_95009": "為來自 JSDoc 的類型標註", "Another_export_default_is_here_2753": "其他匯出預設位於此處。", "Any_Unicode_property_that_would_possibly_match_more_than_a_single_character_is_only_available_when_t_1528": "只有在設定 Unicode 設定 (v) 旗標時，才能使用任何可能比對超過一個字元的 Unicode 屬性。", "Anything_that_would_possibly_match_more_than_a_single_character_is_invalid_inside_a_negated_characte_1518": "在否定字元類別中，任何可能比對超過一個字元的專案都無效。", "Are_you_missing_a_semicolon_2734": "缺少分號嗎?", "Argument_expression_expected_1135": "必須是引數運算式。", "Argument_for_0_option_must_be_Colon_1_6046": "'{0}' 選項的引數必須是: {1}。", "Argument_of_dynamic_import_cannot_be_spread_element_1325": "動態匯入的引數不能是擴張元素。", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_2345": "類型 '{0}' 的引數不可指派給類型 '{1}' 的參數。", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_with_exactOptionalPropertyTypes_Colon_tr_2379": "類型 '{0}' 的引數無法指派給類型為具有 'exactOptionalPropertyTypes: true' 的類型 '{1}' 的參數。請考慮將 'undefined' 新增到目標屬性的類型。", "Arguments_for_the_rest_parameter_0_were_not_provided_6236": "未提供其餘參數 '{0}' 的引數。", "Array_element_destructuring_pattern_expected_1181": "必須是陣列項目解構模式。", "Arrays_with_spread_elements_can_t_inferred_with_isolatedDeclarations_9018": "具有擴張元素的陣列無法使用 --isolatedDeclarations 推斷。", "Assertions_require_every_name_in_the_call_target_to_be_declared_with_an_explicit_type_annotation_2775": "判斷提示要求必須以明確的型別註解宣告呼叫目標中的每個名稱。", "Assertions_require_the_call_target_to_be_an_identifier_or_qualified_name_2776": "判斷提示要求呼叫目標必須為識別碼或限定名稱。", "Assigning_properties_to_functions_without_declaring_them_is_not_supported_with_isolatedDeclarations__9023": "在未宣告的情況下，--isolatedDeclarations 不支援指派屬性給函式。新增指派給此函式之屬性的明確宣告。", "Asterisk_Slash_expected_1010": "必須是 '*/'。", "At_least_one_accessor_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9009": "至少一個存取子必須有具備 --isolatedDeclarations 的明確型別註釋。", "Augmentations_for_the_global_scope_can_only_be_directly_nested_in_external_modules_or_ambient_module_2669": "全域範圍的增強指定只能在外部模組宣告或環境模組宣告直接巢狀。", "Augmentations_for_the_global_scope_should_have_declare_modifier_unless_they_appear_in_already_ambien_2670": "除非全域範圍的增強指定已顯示在環境內容中，否則應含有 'declare' 修飾元。", "Auto_discovery_for_typings_is_enabled_in_project_0_Running_extra_resolution_pass_for_module_1_using__6140": "專案 '{0}' 中已啟用鍵入的自動探索。正在使用快取位置 '{2}' 執行模組 '{1}' 的額外解析傳遞。", "BUILD_OPTIONS_6919": "建置選項", "Backwards_Compatibility_6253": "回溯相容性", "Base_class_expressions_cannot_reference_class_type_parameters_2562": "基底類別運算式無法參考類別型別參數。", "Base_constructor_return_type_0_is_not_an_object_type_or_intersection_of_object_types_with_statically_2509": "基底建構函式傳回型別 '{0}' 不是物件類型或具有靜態已知成員的物件類型交集。", "Base_constructors_must_all_have_the_same_return_type_2510": "基底建構函式的傳回型別必須全部相同。", "Base_directory_to_resolve_non_absolute_module_names_6083": "要解析非絕對模組名稱的基底目錄。", "BigInt_literals_are_not_available_when_targeting_lower_than_ES2020_2737": "當目標低於 ES2020 時，無法使用 BigInt 常值。", "Binary_digit_expected_1177": "必須是二進位數字。", "Binding_element_0_implicitly_has_an_1_type_7031": "繫結元素 '{0}' 隱含擁有 '{1}' 類型。", "Binding_elements_can_t_be_exported_directly_with_isolatedDeclarations_9019": "無法使用 --isolatedDeclarations 直接匯出繫結元素。", "Block_scoped_variable_0_used_before_its_declaration_2448": "已在其宣告之前使用區塊範圍變數 '{0}'。", "Build_a_composite_project_in_the_working_directory_6925": "在工作目錄中建置複合專案。", "Build_all_projects_including_those_that_appear_to_be_up_to_date_6636": "建置包括似乎已是最新狀態的所有專案。", "Build_one_or_more_projects_and_their_dependencies_if_out_of_date_6364": "若已過期，則建置一或多個專案及其相依性", "Build_option_0_requires_a_value_of_type_1_5073": "組建選項 '{0}' 需要 {1} 類型的值。", "Building_project_0_6358": "正在建置專案 '{0}'...", "Built_in_iterators_are_instantiated_with_a_TReturn_type_of_undefined_instead_of_any_6720": "內建 iterators 會以 'undefined' 而非 'any' 的 'TReturn' 類型具現化。", "COMMAND_LINE_FLAGS_6921": "命令列旗標", "COMMON_COMMANDS_6916": "一般命令", "COMMON_COMPILER_OPTIONS_6920": "一般編譯器選項", "Call_decorator_expression_90028": "呼叫裝飾項目運算式", "Call_signature_return_types_0_and_1_are_incompatible_2202": "呼叫簽章傳回型別 '{0}' 與 '{1}' 不相容。", "Call_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7020": "缺少傳回型別註解的呼叫簽章隱含了 'any' 傳回型別。", "Call_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2204": "無引數呼叫簽章的傳回型別 '{0}' 與 '{1}' 不相容。", "Can_only_convert_logical_AND_access_chains_95142": "只可轉換邏輯 AND 存取鏈結", "Can_only_convert_named_export_95164": "只能轉換具名匯出", "Can_only_convert_property_with_modifier_95137": "只能轉換具有修飾元的屬性", "Can_only_convert_string_concatenations_and_string_literals_95154": "只能轉換字串串連和字串常值", "Cannot_access_0_1_because_0_is_a_type_but_not_a_namespace_Did_you_mean_to_retrieve_the_type_of_the_p_2713": "因為 '{0}' 是類型而非命名空間，所以無法存取 '{0}.{1}'。您要在 '{0}' 中使用 '{0}[\"{1}\"]' 擷取屬性 '{1}' 的類型嗎?", "Cannot_access_0_from_another_file_without_qualification_when_1_is_enabled_Use_2_instead_1281": "啟用 '{1}' 時，無法從另一個不具備資格的檔案存取 '{0}'。請改用 '{2}'。", "Cannot_access_ambient_const_enums_when_0_is_enabled_2748": "啟用 '{0}' 時，無法存取環境連續列舉。", "Cannot_assign_a_0_constructor_type_to_a_1_constructor_type_2672": "無法將 '{0}' 建構函式類型指派至 '{1}' 建構函式類型。", "Cannot_assign_an_abstract_constructor_type_to_a_non_abstract_constructor_type_2517": "無法將抽象建構函式類型指派給非抽象建構函式類型。", "Cannot_assign_to_0_because_it_is_a_class_2629": "無法指派至 '{0}'，因為其為類別。", "Cannot_assign_to_0_because_it_is_a_constant_2588": "因為 '{0}' 為常數，所以無法指派至 '{0}'。", "Cannot_assign_to_0_because_it_is_a_function_2630": "無法指派至 '{0}'，因為其為函式。", "Cannot_assign_to_0_because_it_is_a_namespace_2631": "無法指派至 '{0}'，因為其為命名空間。", "Cannot_assign_to_0_because_it_is_a_read_only_property_2540": "因為 '{0}' 為唯讀屬性，所以無法指派至 '{0}'。", "Cannot_assign_to_0_because_it_is_an_enum_2628": "無法指派至 '{0}'，因為其為列舉。", "Cannot_assign_to_0_because_it_is_an_import_2632": "無法指派至 '{0}'，因為其為匯入。", "Cannot_assign_to_0_because_it_is_not_a_variable_2539": "無法指派至 '{0}'，因為它不是變數。", "Cannot_assign_to_private_method_0_Private_methods_are_not_writable_2803": "無法指派給私人方法 '{0}'。私人方法無法寫入。", "Cannot_augment_module_0_because_it_resolves_to_a_non_module_entity_2671": "因為模組 '{0}' 會解析為非模組實體，所以無法加以增強。", "Cannot_augment_module_0_with_value_exports_because_it_resolves_to_a_non_module_entity_2649": "無法使用值匯出擴充模組 '{0}'，因為其會解析為非模組實體。", "Cannot_compile_modules_using_option_0_unless_the_module_flag_is_amd_or_system_6131": "除非 '--module' 旗標為 'amd' 或 'system'，否則無法使用選項 '{0}' 編譯模組。", "Cannot_create_an_instance_of_an_abstract_class_2511": "無法建立抽象類別的執行個體。", "Cannot_delegate_iteration_to_value_because_the_next_method_of_its_iterator_expects_type_1_but_the_co_2766": "因為值的迭代器 'next' 方法需要類型 '{1}'，但所包含的產生器永遠會傳送 '{0}'，所以無法將反覆項目委派給值。", "Cannot_export_0_Only_local_declarations_can_be_exported_from_a_module_2661": "無法匯出 '{0}'。只有區域宣告可以從模組匯出。", "Cannot_extend_a_class_0_Class_constructor_is_marked_as_private_2675": "無法延伸類別 '{0}'。類別建構函式已標記為私用。", "Cannot_extend_an_interface_0_Did_you_mean_implements_2689": "無法擴充介面 '{0}'，您意指「實作」嗎?", "Cannot_find_a_tsconfig_json_file_at_the_current_directory_Colon_0_5081": "在目前的目錄中找不到 tsconfig.json 檔案: {0}。", "Cannot_find_a_tsconfig_json_file_at_the_specified_directory_Colon_0_5057": "在指定的目錄 '{0}' 中找不到 tsconfig.json 檔案。", "Cannot_find_global_type_0_2318": "找不到全域類型 '{0}'。", "Cannot_find_global_value_0_2468": "找不到全域值 '{0}'。", "Cannot_find_lib_definition_for_0_2726": "找不到 '{0}' 的程式庫定義。", "Cannot_find_lib_definition_for_0_Did_you_mean_1_2727": "找不到 '{0}' 的程式庫定義。您是指 '{1}' 嗎?", "Cannot_find_module_0_Consider_using_resolveJsonModule_to_import_module_with_json_extension_2732": "找不到模組 '{0}'。建議使用 '--resolveJsonModule'，匯入副檔名為 '.json' 的模組。", "Cannot_find_module_0_Did_you_mean_to_set_the_moduleResolution_option_to_nodenext_or_to_add_aliases_t_2792": "找不到模組 '{0}'。您是要將 'moduleResolution' 選項設為 'nodenext'，或是要將別名新增至 'paths' 選項?", "Cannot_find_module_0_or_its_corresponding_type_declarations_2307": "找不到模組 '{0}' 或其對應的型別宣告。", "Cannot_find_name_0_2304": "找不到名稱 '{0}'。", "Cannot_find_name_0_Did_you_mean_1_2552": "找不到名稱 '{0}'。您指的是 '{1}' 嗎?", "Cannot_find_name_0_Did_you_mean_the_instance_member_this_0_2663": "找不到名稱 '{0}'。您要找的是此執行個體成員 'this.{0}' 嗎?", "Cannot_find_name_0_Did_you_mean_the_static_member_1_0_2662": "找不到名稱 '{0}'。您要找的是此靜態成員 '{1}.{0}' 嗎?", "Cannot_find_name_0_Did_you_mean_to_write_this_in_an_async_function_2311": "找不到名稱 '{0}'。您是否想要在非同步函數中寫入此專案?", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2583": "找不到名稱「{0}」。要變更您的目標程式庫嗎? 請嘗試將 'lib' 編譯器選項變更為「{1}」或更新版本。", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2584": "找不到名稱「{0}」。要變更您的目標程式庫嗎? 請嘗試將 'lib' 編譯器選項變更為包含 'dom'。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_Bun_Try_npm_i_save_dev_types_Slashbun_2867": "找不到名稱 '{0}'。需要為 Bun 安裝類型定義嗎?請嘗試 `npm i --save-dev @types/bun`。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_Bun_Try_npm_i_save_dev_types_Slashbun_2868": "找不到名稱 '{0}'。需要為 Bun 安裝類型定義嗎?請嘗試 `npm i --save-dev @types/bun`，然後將 'bun' 新增至 tsconfig 中的類型欄位。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2582": "找不到名稱 '{0}'。需要安裝測試執行器的型別定義嗎? 請嘗試 `npm i --save-dev @types/jest` 或 `npm i --save-dev @types/mocha`。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2593": "找不到名稱「{0}」。需要為測試執行器安裝類型定義嗎? 請嘗試 `npm i --save-dev @types/jest` 或 `npm i --save-dev @types/mocha`，然後將 `jest` 或 `mocha` 新增至 tsconfig 中的類型欄位。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2581": "找不到名稱 '{0}'。需要安裝 jQuery 的型別定義嗎? 請嘗試 `npm i --save-dev @types/jquery`。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2592": "找不到名稱「{0}」。需要為 jQuery 安裝類型定義嗎? 請嘗試 `npm i --save-dev @types/jquery`，然後將 `jquery` 新增至 tsconfig 中的類型欄位。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2580": "找不到名稱 '{0}'。需要安裝節點的型別定義嗎? 請嘗試 `npm i --save-dev @types/node`。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2591": "找不到名稱「{0}」。需要為節點安裝類型定義嗎? 請嘗試 `npm i --save-dev @types/node`，然後將 `node` 新增至 tsconfig 中的類型欄位。", "Cannot_find_namespace_0_2503": "找不到命名空間 '{0}'。", "Cannot_find_namespace_0_Did_you_mean_1_2833": "找不到命名空間 '{0}'。您是不是指 '{1}'?", "Cannot_find_parameter_0_1225": "找不到參數 '{0}'。", "Cannot_find_the_common_subdirectory_path_for_the_input_files_5009": "找不到輸入檔的一般子目錄路徑。", "Cannot_find_type_definition_file_for_0_2688": "找不到 '{0}' 的類型定義檔案。", "Cannot_import_type_declaration_files_Consider_importing_0_instead_of_1_6137": "無法匯入型別宣告檔案。請考慮匯入 '{0}' 而不是 '{1}'。", "Cannot_initialize_outer_scoped_variable_0_in_the_same_scope_as_block_scoped_declaration_1_2481": "無法初始化區塊範圍宣告 '{1}' 之同一範圍中的外部範圍變數 '{0}'。", "Cannot_invoke_an_object_which_is_possibly_null_2721": "無法叫用可能為 'null' 的物件。", "Cannot_invoke_an_object_which_is_possibly_null_or_undefined_2723": "無法叫用可能為 'null' 或 'undefined' 的物件。", "Cannot_invoke_an_object_which_is_possibly_undefined_2722": "無法叫用可能為 'undefined' 的物件。", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_destructuring__2765": "因為值的迭代器 'next' 方法需要類型 '{1}'，但陣列解構永遠會傳送 '{0}'，所以無法逐一查看值。", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_spread_will_al_2764": "因為值的迭代器 'next' 方法需要類型 '{1}'，但陣列擴張永遠會傳送 '{0}'，所以無法逐一查看值。", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_for_of_will_always_s_2763": "因為值的迭代器 'next' 方法需要類型 '{1}'，但 for-of 永遠會傳送 '{0}'，所以無法逐一查看值。", "Cannot_move_statements_to_the_selected_file_95183": "無法將陳述式移動至選取的檔案", "Cannot_move_to_file_selected_file_is_invalid_95179": "無法移動至檔案，選取的檔案無效", "Cannot_read_file_0_5083": "無法讀取檔案 '{0}'。", "Cannot_read_file_0_Colon_1_5012": "無法讀取檔案 '{0}': {1}。", "Cannot_redeclare_block_scoped_variable_0_2451": "無法重新宣告區塊範圍變數 '{0}'。", "Cannot_redeclare_exported_variable_0_2323": "無法重新宣告匯出的變數 '{0}'。", "Cannot_redeclare_identifier_0_in_catch_clause_2492": "無法在 Catch 子句中重新宣告識別碼 '{0}'。", "Cannot_start_a_function_call_in_a_type_annotation_1441": "無法在類型註釋中啟動函式呼叫。", "Cannot_use_JSX_unless_the_jsx_flag_is_provided_17004": "除非有提供 '--jsx' 旗標，否則無法使用 JSX。", "Cannot_use_export_import_on_a_type_or_type_only_namespace_when_0_is_enabled_1269": "啟用 '{0}' 時，無法在類型或僅類型命名空間上使用 'export import'。", "Cannot_use_imports_exports_or_module_augmentations_when_module_is_none_1148": "當 '--module' 為 'none' 時，無法使用匯入、匯出或模組增強指定。", "Cannot_use_namespace_0_as_a_type_2709": "不得使用命名空間 '{0}' 作為類型。", "Cannot_use_namespace_0_as_a_value_2708": "不得使用命名空間 '{0}' 作為值。", "Cannot_use_this_in_a_static_property_initializer_of_a_decorated_class_2816": "在修飾類別的靜態屬性初始化運算式中，不能使用 'this'。", "Cannot_write_file_0_because_it_will_overwrite_tsbuildinfo_file_generated_by_referenced_project_1_6377": "因為檔案 '{0}' 會覆寫所參考專案 '{1}' 產生的 '.tsbuildinfo' 檔案，所以無法寫入該檔案", "Cannot_write_file_0_because_it_would_be_overwritten_by_multiple_input_files_5056": "無法寫入檔案 '{0}'，原因是其會由多個輸入檔覆寫。", "Cannot_write_file_0_because_it_would_overwrite_input_file_5055": "無法寫入檔案 '{0}'，原因是其會覆寫輸入檔。", "Catch_clause_variable_cannot_have_an_initializer_1197": "Catch 子句變數不得有初始設定式。", "Catch_clause_variable_type_annotation_must_be_any_or_unknown_if_specified_1196": "Catch 子句變數型別註解必須為 'any' 或 'unknown' (如有指定)。", "Change_0_to_1_90014": "將 '{0}' 變更為 '{1}'", "Change_all_extended_interfaces_to_implements_95038": "將所有延伸介面變更為 'implements'", "Change_all_jsdoc_style_types_to_TypeScript_95030": "將所有 jsdoc 樣式的類型變更為 TypeScript", "Change_all_jsdoc_style_types_to_TypeScript_and_add_undefined_to_nullable_types_95031": "將所有 jsdoc 樣式的類型變更為 TypeScript (並為 null 類型新增 '| undefined')", "Change_extends_to_implements_90003": "將 [延伸] 變更至 [實作]5D;", "Change_spelling_to_0_90022": "將拼字變更為 '{0}'", "Check_for_class_properties_that_are_declared_but_not_set_in_the_constructor_6700": "檢查是否已宣告但未在建構函式中設定的類別屬性。", "Check_side_effect_imports_6806": "檢查副作用匯入。", "Check_that_the_arguments_for_bind_call_and_apply_methods_match_the_original_function_6697": "檢查 'bind'、'call' 和 'apply' 方法的引數是否與原始函式相符。", "Checking_if_0_is_the_longest_matching_prefix_for_1_2_6104": "檢查 '{0}' 是否為 '{1}' - '{2}' 的最長相符前置詞。", "Circular_definition_of_import_alias_0_2303": "匯入別名 '{0}' 的循環定義。", "Circularity_detected_while_resolving_configuration_Colon_0_18000": "解析組態時偵測到循環性: {0}", "Circularity_originates_in_type_at_this_location_2751": "循環源於此位置的類型。", "Class_0_defines_instance_member_accessor_1_but_extended_class_2_defines_it_as_instance_member_functi_2426": "類別 '{0}' 已定義執行個體成員存取子 '{1}'，但是擴充類別 '{2}' 卻將其定義為執行個體成員函式。", "Class_0_defines_instance_member_function_1_but_extended_class_2_defines_it_as_instance_member_access_2423": "類別 '{0}' 已定義執行個體成員函式 '{1}'，但是擴充類別 '{2}' 卻將其定義為執行個體成員存取子。", "Class_0_defines_instance_member_property_1_but_extended_class_2_defines_it_as_instance_member_functi_2425": "類別 '{0}' 已定義執行個體成員屬性 '{1}'，但是擴充類別 '{2}' 卻將其定義為執行個體成員函式。", "Class_0_incorrectly_extends_base_class_1_2415": "類別 '{0}' 不正確地擴充基底類別 '{1}'。", "Class_0_incorrectly_implements_class_1_Did_you_mean_to_extend_1_and_inherit_its_members_as_a_subclas_2720": "類別 '{0}' 不當實作類別 '{1}'。您是否要擴充 '{1}'，並繼承其成員以成為子類別?", "Class_0_incorrectly_implements_interface_1_2420": "類別 '{0}' 不正確地實作介面 '{1}'。", "Class_0_used_before_its_declaration_2449": "類別 '{0}' 的位置在其宣告之前。", "Class_constructor_may_not_be_a_generator_1368": "類別建構函式可能不是產生器。", "Class_constructor_may_not_be_an_accessor_1341": "類別建構函式可能不是存取子。", "Class_declaration_cannot_implement_overload_list_for_0_2813": "類別宣告無法為 '{0}' 實作多載清單。", "Class_declarations_cannot_have_more_than_one_augments_or_extends_tag_8025": "類別宣告不能有一個以上的 `@augments` 或 `@extends` 標籤。", "Class_decorators_can_t_be_used_with_static_private_identifier_Consider_removing_the_experimental_dec_18036": "類別裝飾項目無法與靜態私人識別碼一起使用。請考慮移除實驗性裝飾項目。", "Class_field_0_defined_by_the_parent_class_is_not_accessible_in_the_child_class_via_super_2855": "父類別所定義的類別欄位 '{0}' 無法透過超級在子類別中存取。", "Class_name_cannot_be_0_2414": "類別名稱不得為 '{0}'。", "Class_name_cannot_be_Object_when_targeting_ES5_with_module_0_2725": "當目標為具有模組 {0} 的 ES5 時，類別名稱不可為 'Object'。", "Class_static_side_0_incorrectly_extends_base_class_static_side_1_2417": "類別靜態端 '{0}' 不正確地擴充基底類別靜態端 '{1}'。", "Classes_can_only_extend_a_single_class_1174": "類別只能擴充一個類別。", "Classes_may_not_have_a_field_named_constructor_18006": "類別不能具有名為 'constructor' 的欄位。", "Code_contained_in_a_class_is_evaluated_in_JavaScript_s_strict_mode_which_does_not_allow_this_use_of__1210": "會使用 JavaScript 的嚴格模式，評估包含在類別中的程式碼，其中不允許使用 '{0}'。如需詳細資訊，請參閱 https://developer.mozilla.org/zh-TW/docs/Web/JavaScript/Reference/Strict_mode。", "Command_line_Options_6171": "命令列選項", "Compile_the_project_given_the_path_to_its_configuration_file_or_to_a_folder_with_a_tsconfig_json_6020": "當路徑為專案組態檔或為 'tsconfig.json' 所在的資料夾時編譯專案。", "Compiler_Diagnostics_6251": "編譯器診斷", "Compiler_option_0_cannot_be_given_an_empty_string_18051": "編譯器選項 '{0}' 無法指定空字串。", "Compiler_option_0_expects_an_argument_6044": "編譯器選項 '{0}' 必須要有一個引數。", "Compiler_option_0_may_not_be_used_with_build_5094": "編譯器選項 '--{0}' 不能與 '--build' 一起使用。", "Compiler_option_0_may_only_be_used_with_build_5093": "編譯器選項 '--{0}' 只能與 '--build' 一起使用。", "Compiler_option_0_of_value_1_is_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_w_4124": "'{1}' 值的編譯器選項 '{0}'不穩定。使用夜間 TypeScript 將此錯誤設為靜音。請嘗試使用 'npm install -D typescript@next' 更新。", "Compiler_option_0_requires_a_value_of_type_1_5024": "編譯器選項 '{0}' 需要類型 {1} 的值。", "Compiler_reserves_name_0_when_emitting_private_identifier_downlevel_18027": "降級發出私人識別碼時，編譯器會保留名稱 '{0}'。", "Compiles_the_TypeScript_project_located_at_the_specified_path_6927": "編譯位於指定路徑的 TypeScript 專案。", "Compiles_the_current_project_tsconfig_json_in_the_working_directory_6923": "編譯目前的專案 (工作目錄中的 tsconfig.json)。", "Compiles_the_current_project_with_additional_settings_6929": "使用其他設定編譯目前的專案。", "Completeness_6257": "完整性", "Composite_projects_may_not_disable_declaration_emit_6304": "複合式專案可能未停用宣告發出。", "Composite_projects_may_not_disable_incremental_compilation_6379": "複合專案可能不會停用累加編譯。", "Computed_from_the_list_of_input_files_6911": "從輸入檔案清單計算", "Computed_properties_must_be_number_or_string_literals_variables_or_dotted_expressions_with_isolatedD_9014": "計算屬性必須是具有 --isolatedDeclarations 的數字或字串常值、變數或點狀運算式。", "Computed_property_names_are_not_allowed_in_enums_1164": "列舉中不能有計算的屬性名稱。", "Computed_property_names_on_class_or_object_literals_cannot_be_inferred_with_isolatedDeclarations_9038": "無法使用 --isolatedDeclarations 推斷類別或物件常值上的計算屬性名稱。", "Computed_values_are_not_permitted_in_an_enum_with_string_valued_members_2553": "具有字串值成員的列舉中不允許計算值。", "Concatenate_and_emit_output_to_single_file_6001": "串連並發出輸出至單一檔案。", "Conditions_to_set_in_addition_to_the_resolver_specific_defaults_when_resolving_imports_6410": "解析匯入時，除了解析程式特定預設值之外，還需要設定的條件。", "Conflicts_are_in_this_file_6201": "此檔案中有衝突。", "Consider_adding_a_declare_modifier_to_this_class_6506": "請考慮將 'declare' 修飾元加入此類別。", "Construct_signature_return_types_0_and_1_are_incompatible_2203": "建構簽章傳回型別 '{0}' 與 '{1}' 不相容。", "Construct_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7013": "缺少傳回型別註解的建構簽章，隱含有 'any' 傳回型別。", "Construct_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2205": "無引數建構簽章的傳回型別 '{0}' 與 '{1}' 不相容。", "Constructor_implementation_is_missing_2390": "缺少建構函式實作。", "Constructor_of_class_0_is_private_and_only_accessible_within_the_class_declaration_2673": "類別 '{0}' 的建構函式為私用，並且只能在類別宣告內存取。", "Constructor_of_class_0_is_protected_and_only_accessible_within_the_class_declaration_2674": "類別 '{0}' 的建構函式受到保護，並且只能在類別宣告內存取。", "Constructor_type_notation_must_be_parenthesized_when_used_in_a_union_type_1386": "在等位型別中使用建構函式類型標記法時，必須括以括弧。", "Constructor_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1388": "在交集型別中使用建構函式類型標記法時，必須括以括弧。", "Constructors_for_derived_classes_must_contain_a_super_call_2377": "衍生類別的建構函式必須包含 'super' 呼叫。", "Containing_file_is_not_specified_and_root_directory_cannot_be_determined_skipping_lookup_in_node_mod_6126": "未指定包含檔案，因此無法決定根目錄，而將略過 'node_modules' 中的查閱。", "Containing_function_is_not_an_arrow_function_95128": "內含函式不是箭頭函式", "Control_what_method_is_used_to_detect_module_format_JS_files_1475": "控制用來偵測模組格式 JS 檔案的方法。", "Conversion_of_type_0_to_type_1_may_be_a_mistake_because_neither_type_sufficiently_overlaps_with_the__2352": "將類型 '{0}' 轉換為類型 '{1}' 可能會發生錯誤，原因是這兩個類型彼此並未充分重疊。如果是故意轉換的，請先將運算式轉換為 'unknown'。", "Convert_0_to_1_in_0_95003": "將 '{0}' 轉換成 '{1} in {0}'", "Convert_0_to_mapped_object_type_95055": "將 '{0}' 轉換為對應的物件類型", "Convert_all_const_to_let_95102": "將所有 'const' 轉換為 'let'", "Convert_all_constructor_functions_to_classes_95045": "將所有建構函式轉換為類別", "Convert_all_invalid_characters_to_HTML_entity_code_95101": "將所有無效字元轉換為 HTML 實體代碼", "Convert_all_re_exported_types_to_type_only_exports_1365": "將所有重新匯出的類型轉換為僅限類型的匯出", "Convert_all_require_to_import_95048": "將所有 'require' 轉換至 'import'", "Convert_all_to_async_functions_95066": "全部轉換為非同步函式", "Convert_all_to_bigint_numeric_literals_95092": "全部轉換為 Bigint 數字常值", "Convert_all_to_default_imports_95035": "全部轉換為預設匯入", "Convert_all_type_literals_to_mapped_type_95021": "將所有類型常值轉換成相對應的類型", "Convert_all_typedef_to_TypeScript_types_95177": "轉換所有 typedef 為 TypeScript 類型。", "Convert_arrow_function_or_function_expression_95122": "轉換箭頭函式或函式運算式", "Convert_const_to_let_95093": "將 'const' 轉換為 'let'", "Convert_default_export_to_named_export_95061": "將預設匯出轉換為具名匯出", "Convert_function_declaration_0_to_arrow_function_95106": "將函式宣告 '{0}' 轉換為箭號函式", "Convert_function_expression_0_to_arrow_function_95105": "將函式運算式 '{0}' 轉換為箭號函式", "Convert_function_to_an_ES2015_class_95001": "將函式轉換為 ES2015 類別", "Convert_invalid_character_to_its_html_entity_code_95100": "將無效字元轉換為其 HTML 實體代碼", "Convert_named_export_to_default_export_95062": "將具名匯出轉換為預設匯出", "Convert_named_imports_to_default_import_95170": "將具名匯入轉換為預設匯入", "Convert_named_imports_to_namespace_import_95057": "將具名匯入轉換為命名空間匯入", "Convert_namespace_import_to_named_imports_95056": "將命名空間匯入轉換為具名匯入", "Convert_overload_list_to_single_signature_95118": "將多載清單轉換成單一特徵標記", "Convert_parameters_to_destructured_object_95075": "將參數轉換為解構的物件", "Convert_require_to_import_95047": "將 'require' 轉換至 'import'", "Convert_to_ES_module_95017": "轉換為 ES 模組", "Convert_to_a_bigint_numeric_literal_95091": "轉換為 Bigint 數字常值", "Convert_to_anonymous_function_95123": "轉換為匿名函式", "Convert_to_arrow_function_95125": "轉換為箭頭函式", "Convert_to_async_function_95065": "轉換為非同步函式", "Convert_to_default_import_95013": "轉換為預設匯入", "Convert_to_named_function_95124": "轉換為具名函式", "Convert_to_optional_chain_expression_95139": "轉換為選擇性鏈結運算式", "Convert_to_template_string_95096": "轉換為範本字串", "Convert_to_type_only_export_1364": "轉換為僅限類型的匯出", "Convert_typedef_to_TypeScript_type_95176": "轉換 typedef 為 TypeScript 類型。", "Corrupted_locale_file_0_6051": "地區設定檔 {0} 已損毀。", "Could_not_convert_to_anonymous_function_95153": "無法轉換成匿名函式", "Could_not_convert_to_arrow_function_95151": "無法轉換成箭頭函式", "Could_not_convert_to_named_function_95152": "無法轉換成具名函式", "Could_not_determine_function_return_type_95150": "無法判斷函式傳回型別", "Could_not_find_a_containing_arrow_function_95127": "找不到內含箭頭函式", "Could_not_find_a_declaration_file_for_module_0_1_implicitly_has_an_any_type_7016": "找不到模組 '{0}' 的宣告檔案。'{1}' 隱含具有 'any' 類型。", "Could_not_find_convertible_access_expression_95140": "找不到可轉換的存取運算式", "Could_not_find_export_statement_95129": "找不到匯出陳述式", "Could_not_find_import_clause_95131": "找不到匯入子句", "Could_not_find_matching_access_expressions_95141": "找不到相符的存取運算式", "Could_not_find_name_0_Did_you_mean_1_2570": "找不到名稱 '{0}'。您是不是指 '{1}'?", "Could_not_find_namespace_import_or_named_imports_95132": "找不到命名空間匯入或具名匯入", "Could_not_find_property_for_which_to_generate_accessor_95135": "找不到要為其產生存取子的屬性", "Could_not_find_variable_to_inline_95185": "找不到要內嵌的變數。", "Could_not_resolve_the_path_0_with_the_extensions_Colon_1_6231": "無法解析具有下列延伸模組的路徑 '{0}': {1}。", "Could_not_write_file_0_Colon_1_5033": "無法編寫檔案 '{0}': {1}。", "Create_source_map_files_for_emitted_JavaScript_files_6694": "建立發出 JavaScript 檔案的來源對應檔。", "Create_sourcemaps_for_d_ts_files_6614": "為 d.ts 檔案建立 sourcemap。", "Creates_a_tsconfig_json_with_the_recommended_settings_in_the_working_directory_6926": "使用建議設定在工作目錄中建立 tsconfig.json。", "DIRECTORY_6038": "目錄", "Decimal_escape_sequences_and_backreferences_are_not_allowed_in_a_character_class_1537": "字元類別中不允許小數點逸出序列和反向參考。", "Decimals_with_leading_zeros_are_not_allowed_1489": "不允許前置為零的小數點。", "Declaration_augments_declaration_in_another_file_This_cannot_be_serialized_6232": "宣告會讓另一個檔案中的宣告增加。這無法序列化。", "Declaration_emit_for_this_file_requires_preserving_this_import_for_augmentations_This_is_not_support_9026": "此檔案發出的宣告需要保留此匯入，以進行增強。該情況不受 --isolatedDeclarations 支援。", "Declaration_emit_for_this_file_requires_using_private_name_0_An_explicit_type_annotation_may_unblock_9005": "此檔案的宣告發出必須使用私人名稱 '{0}'。明確的型別註解可能會解除封鎖宣告發出。", "Declaration_emit_for_this_file_requires_using_private_name_0_from_module_1_An_explicit_type_annotati_9006": "此檔案的宣告發出必須使用來自模組 '{1}' 的私人名稱 '{0}'。明確的型別註解可能會解除封鎖宣告發出。", "Declaration_emit_for_this_parameter_requires_implicitly_adding_undefined_to_its_type_This_is_not_sup_9025": "此參數發出的宣告需要隱含地新增未定義值至其類型。此情況不受 --isolatedDeclarations 支援。", "Declaration_expected_1146": "必須是宣告。", "Declaration_name_conflicts_with_built_in_global_identifier_0_2397": "宣告名稱與內建全域識別碼 '{0}' 衝突。", "Declaration_or_statement_expected_1128": "必須是宣告或陳述式。", "Declaration_or_statement_expected_This_follows_a_block_of_statements_so_if_you_intended_to_write_a_d_2809": "必須是宣告或陳述式。這個 '=' 會跟著陳述式區塊，因此如果您想要撰寫解構指派，就可能需要在整個指派的前後加上括弧。", "Declarations_with_definite_assignment_assertions_must_also_have_type_annotations_1264": "包含明確指派判斷提示的宣告也必須包含類型註釋。", "Declarations_with_initializers_cannot_also_have_definite_assignment_assertions_1263": "包含初始設定式的宣告不得同時包含明確指派判斷提示。", "Declare_a_private_field_named_0_90053": "宣告名為 '{0}' 的私人欄位。", "Declare_method_0_90023": "宣告方法 '{0}'", "Declare_private_method_0_90038": "宣告私人方法 '{0}'", "Declare_private_property_0_90035": "宣告私人屬性 '{0}'", "Declare_property_0_90016": "宣告屬性 '{0}'", "Declare_static_method_0_90024": "宣告靜態方法 '{0}'", "Declare_static_property_0_90027": "宣告靜態屬性 '{0}'", "Decorator_function_return_type_0_is_not_assignable_to_type_1_1270": "裝飾項目函式傳回類型 '{0}' 無法指派給類型 '{1}'。", "Decorator_function_return_type_is_0_but_is_expected_to_be_void_or_any_1271": "裝飾項目函式傳回類型是 '{0}'，但必須是 'void' 或 'any'。", "Decorator_used_before_export_here_1486": "在此處「匯出」之前使用的裝飾項目。", "Decorators_are_not_valid_here_1206": "裝飾項目在此處無效。", "Decorators_cannot_be_applied_to_multiple_get_Slashset_accessors_of_the_same_name_1207": "無法將裝飾項目套用至多個同名的 get/set 存取子。", "Decorators_may_not_appear_after_export_or_export_default_if_they_also_appear_before_export_8038": "如果裝飾項目也出現在 'export' 之前，就不能出現在 'export' 或 'export default' 後。", "Decorators_must_precede_the_name_and_all_keywords_of_property_declarations_1436": "裝飾項目必須在屬性宣告的名稱和所有關鍵詞之前。", "Default_catch_clause_variables_as_unknown_instead_of_any_6803": "預設 catch 子句變數為 'unknown' 而非 'any'。", "Default_export_of_the_module_has_or_is_using_private_name_0_4082": "模組的預設匯出具有或正在使用私用名稱 '{0}'。", "Default_exports_can_t_be_inferred_with_isolatedDeclarations_9037": "無法使用 --isolatedDeclarations 推斷預設匯出。", "Default_library_1424": "預設程式庫", "Default_library_for_target_0_1425": "目標 '{0}' 的預設程式庫", "Definitions_of_the_following_identifiers_conflict_with_those_in_another_file_Colon_0_6200": "下列識別碼的定義與其他檔案中的定義衝突: {0}", "Delete_all_unused_declarations_95024": "刪除所有未使用的宣告", "Delete_all_unused_imports_95147": "刪除所有未使用的匯入", "Delete_all_unused_param_tags_95172": "刪除所有未使用的 '@param' 標籤", "Delete_the_outputs_of_all_projects_6365": "刪除所有專案的輸出。", "Delete_unused_param_tag_0_95171": "刪除未使用的 '@param' 標記 '{0}'", "Deprecated_Use_jsxFactory_instead_Specify_the_object_invoked_for_createElement_when_targeting_react__6084": "[即將淘汰] 請改用 '--jsxFactory'。當目標為 'react' JSX 發出時，為 createElement 指定所叫用的物件", "Deprecated_Use_outFile_instead_Concatenate_and_emit_output_to_single_file_6170": "[即將淘汰] 請改用 '--outFile'。 串連輸出並將其發出到單一檔案", "Deprecated_Use_skipLibCheck_instead_Skip_type_checking_of_default_library_declaration_files_6160": "[即將淘汰] 請改用 '--skipLibCheck'。跳過預設程式庫宣告檔案的類型檢查。", "Deprecated_setting_Use_outFile_instead_6677": "已淘汰的設定值。請改用 'outFile'。", "Did_you_forget_to_use_await_2773": "忘了要使用 'await' 嗎?", "Did_you_mean_0_1369": "您指 '{0}' 嗎?", "Did_you_mean_for_0_to_be_constrained_to_type_new_args_Colon_any_1_2735": "您是要將 '{0}' 限制為類型 'new (...args: any[]) => {1}' 嗎?", "Did_you_mean_to_call_this_expression_6212": "您是要呼叫此運算式嗎?", "Did_you_mean_to_mark_this_function_as_async_1356": "您是要將此函式標記為 'async' 嗎?", "Did_you_mean_to_use_a_Colon_An_can_only_follow_a_property_name_when_the_containing_object_literal_is_1312": "要使用 ':' 嗎? 當包含的物件常值是解構模式的一部分時，'=' 就只能位於屬性名稱後面。", "Did_you_mean_to_use_new_with_this_expression_6213": "您是要對此運算式使用 'new' 嗎?", "Digit_expected_1124": "必須是數字。", "Directory_0_does_not_exist_skipping_all_lookups_in_it_6148": "目錄 '{0}' 不存在，將會跳過其中所有查閱。", "Directory_0_has_no_containing_package_json_scope_Imports_will_not_resolve_6270": "目錄 '{0}' 不包含在 package.js 範圍內。將不會解析匯入。", "Disable_adding_use_strict_directives_in_emitted_JavaScript_files_6669": "停用在發出的 JavaScript 檔案中新增 'use strict' 指示詞。", "Disable_checking_for_this_file_90018": "停用此檔案的檢查", "Disable_emitting_comments_6688": "停用發出註解。", "Disable_emitting_declarations_that_have_internal_in_their_JSDoc_comments_6701": "停用其 JSDoc 註解中具有 '@internal' 的發出宣告。", "Disable_emitting_files_from_a_compilation_6660": "停用從編譯發出檔案。", "Disable_emitting_files_if_any_type_checking_errors_are_reported_6662": "如果報告任何型別檢查錯誤，則停用發出的檔案。", "Disable_erasing_const_enum_declarations_in_generated_code_6682": "停用在產生的程式碼中抹除 'const enum' 宣告。", "Disable_error_reporting_for_unreachable_code_6603": "停用無法執行到的程式碼錯誤報告。", "Disable_error_reporting_for_unused_labels_6604": "停用未使用標籤的錯誤報表。", "Disable_full_type_checking_only_critical_parse_and_emit_errors_will_be_reported_6805": "停用完整類型檢查 (只回報重要剖析和發出錯誤訊息)。", "Disable_generating_custom_helper_functions_like_extends_in_compiled_output_6661": "停用在編譯輸出中產生自訂的協助程式函式，例如 '__extends'。", "Disable_including_any_library_files_including_the_default_lib_d_ts_6670": "停用包括任何程式庫檔案，包括預設的 lib.d.ts。", "Disable_loading_referenced_projects_6235": "停用載入參考的專案。", "Disable_preferring_source_files_instead_of_declaration_files_when_referencing_composite_projects_6620": "參考複合專案時，停用偏好的來源檔案而不是宣告檔案。", "Disable_reporting_of_excess_property_errors_during_the_creation_of_object_literals_6702": "在建立物件常值期間停用多餘屬性錯誤報告。", "Disable_resolving_symlinks_to_their_realpath_This_correlates_to_the_same_flag_in_node_6683": "停用將 symlinks 解析為其 realpath。這與節點中的相同旗標有關。", "Disable_size_limitations_on_JavaScript_projects_6162": "停用 JavaScript 專案的大小限制。", "Disable_solution_searching_for_this_project_6224": "停用此專案的解決方案搜尋。", "Disable_strict_checking_of_generic_signatures_in_function_types_6673": "停用函式類型中一般簽章的 Strict 檢查。", "Disable_the_type_acquisition_for_JavaScript_projects_6625": "停用 JavaScript 專案的類型取得", "Disable_truncating_types_in_error_messages_6663": "停用錯誤訊息中的截斷類型。", "Disable_use_of_source_files_instead_of_declaration_files_from_referenced_projects_6221": "停用來源檔案，而非所參考專案中的宣告檔案。", "Disable_wiping_the_console_in_watch_mode_6684": "停用在監看模式中抹除主控台。", "Disables_inference_for_type_acquisition_by_looking_at_filenames_in_a_project_6616": "透過查看專案中的檔案名，停用型別推斷的取得。", "Disallow_import_s_require_s_or_reference_s_from_expanding_the_number_of_files_TypeScript_should_add__6672": "不允許 import'、'require' 或 '<reference>' 擴充 TypeScript 應該加入專案的檔案數目。", "Disallow_inconsistently_cased_references_to_the_same_file_6078": "不允許相同檔案大小寫不一致的參考。", "Do_not_add_triple_slash_references_or_imported_modules_to_the_list_of_compiled_files_6159": "不要在編譯後的檔案清單中新增三道斜線的參考或匯入的模組。", "Do_not_allow_runtime_constructs_that_are_not_part_of_ECMAScript_6721": "不允許不屬於ECMAScript一部分的運行時間建構。", "Do_not_emit_comments_to_output_6009": "請勿將註解發出到輸出。", "Do_not_emit_declarations_for_code_that_has_an_internal_annotation_6056": "請勿發出包含 '@internal' 註釋的程式碼宣告。", "Do_not_emit_outputs_6010": "請勿發出輸出。", "Do_not_emit_outputs_if_any_errors_were_reported_6008": "如果回報了任何錯誤，即不發出輸出。", "Do_not_emit_use_strict_directives_in_module_output_6112": "請勿在模組輸出中發出 'use strict' 指示詞。", "Do_not_erase_const_enum_declarations_in_generated_code_6007": "請勿清除產生之程式碼中的 const 列舉宣告。", "Do_not_generate_custom_helper_functions_like_extends_in_compiled_output_6157": "不要在編譯後的輸出中產生自訂的協助程式函式，例如 '__extends'。", "Do_not_include_the_default_library_file_lib_d_ts_6158": "不要包含預設程式庫檔案 (lib.d.ts)。", "Do_not_report_errors_on_unreachable_code_6077": "請勿回報無法執行到之程式碼的錯誤。", "Do_not_report_errors_on_unused_labels_6074": "請勿回報未使用之標籤的錯誤。", "Do_not_resolve_the_real_path_of_symlinks_6013": "請勿解析符號連結的真實路徑。", "Do_not_transform_or_elide_any_imports_or_exports_not_marked_as_type_only_ensuring_they_are_written_i_6804": "請勿轉換或省略任何未標示為僅限類型的匯入或匯出，請確保其是以輸出檔案的格式並根據 'module' 設定所撰寫。", "Do_not_truncate_error_messages_6165": "不要截斷錯誤訊息。", "Duplicate_function_implementation_2393": "函式實作重複。", "Duplicate_identifier_0_2300": "識別碼 '{0}' 重複。", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_2441": "識別碼 '{0}' 重複。編譯器會將名稱 '{1}' 保留在模組的最上層範圍中。", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_containing_async_func_2529": "識別碼 '{0}' 重複。編譯器會將名稱 '{1}' 保留在含有非同步函式模組的最上層範圍中。", "Duplicate_identifier_0_Compiler_reserves_name_1_when_emitting_super_references_in_static_initializer_2818": "識別碼 '{0}' 重複。在靜態初始設定式中發出 'super' 參考時，編譯器會保留名稱 '{1}'。", "Duplicate_identifier_0_Compiler_uses_declaration_1_to_support_async_functions_2520": "識別碼 '{0}' 重複。編譯器會使用宣告 '{1}' 支援非同步函式。", "Duplicate_identifier_0_Static_and_instance_elements_cannot_share_the_same_private_name_2804": "重複的識別碼 '{0}'。靜態與執行個體元素不得共用相同的私人名稱。", "Duplicate_identifier_arguments_Compiler_uses_arguments_to_initialize_rest_parameters_2396": "識別碼 'arguments'' 重複。編譯器會使用 'arguments' 來初始化剩餘參數。", "Duplicate_identifier_newTarget_Compiler_uses_variable_declaration_newTarget_to_capture_new_target_me_2543": "識別碼 '_newTarget' 重複。編譯器使用變數宣告 '_newTarget' 擷取 'new.target' 中繼屬性參考。", "Duplicate_identifier_this_Compiler_uses_variable_declaration_this_to_capture_this_reference_2399": "識別碼 '_this' 重複。編譯器會使用變數宣告 '_this' 來擷取 'this' 參考。", "Duplicate_index_signature_for_type_0_2374": "類型 '{0}' 的索引簽章重複。", "Duplicate_label_0_1114": "標籤 '{0}' 重複。", "Duplicate_property_0_2718": "屬性 '{0}' 重複。", "Duplicate_regular_expression_flag_1500": "重複的規則運算式旗標。", "Dynamic_import_s_specifier_must_be_of_type_string_but_here_has_type_0_7036": "動態匯入的指定名稱必須屬於類型 'string'，但這裡的類型為 '{0}'。", "Dynamic_imports_are_only_supported_when_the_module_flag_is_set_to_es2020_es2022_esnext_commonjs_amd__1323": "只有在 '--module' 旗標設定為 'es2020'、'es2022'、'esnext'、'commonjs'、'amd'、'system'、'umd'、'node16'、'node18' 或 'nodenext'，才支援動態匯入。", "Dynamic_imports_can_only_accept_a_module_specifier_and_an_optional_set_of_attributes_as_arguments_1450": "動態匯入只接受模組指定名稱和一系列選擇性屬性來做為引數", "Dynamic_imports_only_support_a_second_argument_when_the_module_option_is_set_to_esnext_node16_node18_1324": "當 '--module' 選項設定為 'esnext'、'node16'、'node18'、'nodenext' 或 'preserve' 時，動態匯入只支援第二個引數。", "ESM_syntax_is_not_allowed_in_a_CommonJS_module_when_module_is_set_to_preserve_1293": "當 'module' 設定為 'preserve' 時，CommonJS 模組中不允許 ESM 語法。", "ESM_syntax_is_not_allowed_in_a_CommonJS_module_when_verbatimModuleSyntax_is_enabled_1286": "啟用 'verbatimModuleSyntax' 時，CommonJS 模組中不允許 ESM 語法。", "Each_declaration_of_0_1_differs_in_its_value_where_2_was_expected_but_3_was_given_4125": "'{0}.{1}' 的每個宣告值不同，其中應該要有 '{2}'，但只包含 '{3}'。", "Each_member_of_the_union_type_0_has_construct_signatures_but_none_of_those_signatures_are_compatible_2762": "等位型別 '{0}' 的每個成員都有建構簽章，但這些簽章都互不相容。", "Each_member_of_the_union_type_0_has_signatures_but_none_of_those_signatures_are_compatible_with_each_2758": "等位型別 '{0}' 的每個成員都有簽章，但這些簽章都互不相容。", "Editor_Support_6249": "編輯器支援", "Element_implicitly_has_an_any_type_because_expression_of_type_0_can_t_be_used_to_index_type_1_7053": "因為 '{0}' 類型的運算式無法用於索引類型 '{1}'，所以項目隱含 'any' 類型。", "Element_implicitly_has_an_any_type_because_index_expression_is_not_of_type_number_7015": "因為索引運算式不屬於類型 'number'，所以元素具有隱含 'any' 類型。", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_7017": "元素隱含地擁有 'any' 類型，因為類型 '{0}' 不具索引簽章。", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_Did_you_mean_to_call_1_7052": "因為類型 '{0}' 沒有索引簽章，所以項目隱含 'any' 類型。您是要呼叫 '{1}' 嗎?", "Emit_6246": "發出", "Emit_ECMAScript_standard_compliant_class_fields_6712": "發出 ECMAScript 標準相容類別欄位。", "Emit_a_UTF_8_Byte_Order_Mark_BOM_in_the_beginning_of_output_files_6622": "在輸出檔的開頭發出 UTF-8 位元組順序標記 (BOM)。", "Emit_a_single_file_with_source_maps_instead_of_having_a_separate_file_6151": "發出單一檔案包含來源對應，而不要使用個別的檔案。", "Emit_a_v8_CPU_profile_of_the_compiler_run_for_debugging_6638": "發出用於偵錯的編譯器執行 v8 CPU 設定檔。", "Emit_additional_JavaScript_to_ease_support_for_importing_CommonJS_modules_This_enables_allowSyntheti_6626": "發出其他 JavaScript，以輕鬆支援匯入 CommonJS 模組。這會啟用 'allowSyntheticDefaultImports' 進行類型相容性。", "Emit_class_fields_with_Define_instead_of_Set_6222": "使用 Define 而非 Set 發出類別欄位。", "Emit_design_type_metadata_for_decorated_declarations_in_source_files_6624": "發出來源檔案中修飾式宣告的設計型別中繼資料。", "Emit_more_compliant_but_verbose_and_less_performant_JavaScript_for_iteration_6621": "針對反覆項目發出更符合規範，但具詳細資訊及較低效能 JavaScript。", "Emit_the_source_alongside_the_sourcemaps_within_a_single_file_requires_inlineSourceMap_or_sourceMap__6152": "使用單一檔案發出來源與來源對應。必須設定 '--inlineSourceMap' 或 '--sourceMap'。", "Enable_all_strict_type_checking_options_6180": "啟用所有 Strict 類型檢查選項。", "Enable_color_and_formatting_in_TypeScript_s_output_to_make_compiler_errors_easier_to_read_6685": "在 TypeScript 的輸出中啟用色彩及格式化，讓編譯器錯誤更容易閱讀。", "Enable_constraints_that_allow_a_TypeScript_project_to_be_used_with_project_references_6611": "啟用允許 TypeScript 專案搭配專案參考使用的條件約束。", "Enable_error_reporting_for_codepaths_that_do_not_explicitly_return_in_a_function_6667": "啟用未在函式中明確傳回之 codepaths 的錯誤報吿。", "Enable_error_reporting_for_expressions_and_declarations_with_an_implied_any_type_6665": "啟用具有隱含 'any' 類型的運算式及宣告之錯誤報告。", "Enable_error_reporting_for_fallthrough_cases_in_switch_statements_6664": "啟用 switch 陳述式中 fallthrough 案例的錯誤報表。", "Enable_error_reporting_in_type_checked_JavaScript_files_6609": "在已完成型別檢查的 JavaScript 檔案中啟用錯誤報表。", "Enable_error_reporting_when_local_variables_aren_t_read_6675": "當未讀取區域變數時，啟用錯誤報吿。", "Enable_error_reporting_when_this_is_given_the_type_any_6668": "為 'this' 指定類型 'any' 時，啟用錯誤報表。", "Enable_experimental_support_for_legacy_experimental_decorators_6630": "啟用舊版實驗性裝飾項目的實驗性支援。", "Enable_importing_files_with_any_extension_provided_a_declaration_file_is_present_6264": "啟用匯入具有任何延伸模組的檔案，並存在宣告檔案即可。", "Enable_importing_json_files_6689": "啟用匯入 json 檔案。", "Enable_lib_replacement_6808": "啟用連結庫取代。", "Enable_project_compilation_6302": "啟用專案編譯", "Enable_strict_bind_call_and_apply_methods_on_functions_6214": "對函式啟用嚴格的 'bind'、'call' 及 'apply' 方法。", "Enable_strict_checking_of_function_types_6186": "啟用嚴格檢查函式類型。", "Enable_strict_checking_of_property_initialization_in_classes_6187": "啟用類別中屬性初始化的 strict 檢查。", "Enable_strict_null_checks_6113": "啟用嚴格 null 檢查。", "Enable_the_experimentalDecorators_option_in_your_configuration_file_95074": "在您的組態檔中啟用 'experimentalDecorators' 選項", "Enable_the_jsx_flag_in_your_configuration_file_95088": "在您的組態檔中啟用 '--jsx' 旗標", "Enable_tracing_of_the_name_resolution_process_6085": "啟用名稱解析流程的追蹤。", "Enable_verbose_logging_6713": "啟用詳細資訊記錄。", "Enables_emit_interoperability_between_CommonJS_and_ES_Modules_via_creation_of_namespace_objects_for__7037": "透過為所有匯入建立命名空間物件，讓 CommonJS 和 ES 模組之間的產出有互通性。意指 'allowSyntheticDefaultImports'。", "Enables_experimental_support_for_ES7_decorators_6065": "啟用 ES7 裝飾項目的實驗支援。", "Enables_experimental_support_for_emitting_type_metadata_for_decorators_6066": "啟用實驗支援以發出裝飾項目類型的中繼資料。", "Enforces_using_indexed_accessors_for_keys_declared_using_an_indexed_type_6671": "對使用索引型別宣告的索引鍵強制使用索引存取子。", "Ensure_overriding_members_in_derived_classes_are_marked_with_an_override_modifier_6666": "請確認衍生類別中的覆寫成員已標上 override 修飾元。", "Ensure_that_casing_is_correct_in_imports_6637": "請確認 Imports 中的大小寫正確。", "Ensure_that_each_file_can_be_safely_transpiled_without_relying_on_other_imports_6645": "確保每個檔案都可安全地轉譯，而不依賴其他匯入。", "Ensure_use_strict_is_always_emitted_6605": "確保永遠發出 'use strict'。", "Entering_conditional_exports_6413": "正在進入條件式匯出。", "Entry_point_for_implicit_type_library_0_1420": "隱含型別程式庫 '{0}' 的進入點", "Entry_point_for_implicit_type_library_0_with_packageId_1_1421": "具有 packageId '{1}' 的隱含型別程式庫 '{0}' 進入點", "Entry_point_of_type_library_0_specified_in_compilerOptions_1417": "在 CompilerOptions 中指定的型別程式庫 '{0}' 進入點", "Entry_point_of_type_library_0_specified_in_compilerOptions_with_packageId_1_1418": "具有 packageId '{1}' 且在 CompilerOptions 中指定的型別程式庫 '{0}' 進入點", "Enum_0_used_before_its_declaration_2450": "列舉 '{0}' 的位置在其宣告之前。", "Enum_declarations_can_only_merge_with_namespace_or_other_enum_declarations_2567": "列舉宣告只能與命名空間或其他列舉宣告合併。", "Enum_declarations_must_all_be_const_or_non_const_2473": "列舉宣告必須都是 const 或非 const。", "Enum_member_expected_1132": "必須是列舉成員。", "Enum_member_following_a_non_literal_numeric_member_must_have_an_initializer_when_isolatedModules_is__18056": "啟用 'isolatedModules' 時，跟隨非常值數值成員的列舉成員必須有初始設定式。", "Enum_member_initializers_must_be_computable_without_references_to_external_symbols_with_isolatedDecl_9020": "列舉成員初始設定式必須是可計算，且不參考具有 --isolatedDeclarations 的外部符號。", "Enum_member_must_have_initializer_1061": "列舉成員必須有初始設定式。", "Enum_name_cannot_be_0_2431": "列舉名稱不得為 '{0}'。", "Errors_Files_6041": "錯誤檔案", "Escape_sequence_0_is_not_allowed_1488": "不允許逸出序列 '{0}'。", "Examples_Colon_0_6026": "範例: {0}", "Excessive_complexity_comparing_types_0_and_1_2859": "過度複雜性比較類型 '{0}' 和 '{1}'。", "Excessive_stack_depth_comparing_types_0_and_1_2321": "比較類型 '{0}' 與 '{1}' 的堆疊深度過深。", "Exiting_conditional_exports_6416": "正在退出條件式匯出。", "Expected_0_1_type_arguments_provide_these_with_an_extends_tag_8027": "必須是 {0}-{1} 型別引數; 請提供有 '@ extends' 標記的這類型引數。", "Expected_0_arguments_but_got_1_2554": "應有 {0} 個引數，但得到 {1} 個。", "Expected_0_arguments_but_got_1_Did_you_forget_to_include_void_in_your_type_argument_to_Promise_2794": "應為 {0} 個引數，但現有 {1} 個。是否忘記將型別引數中的 'void' 納入 'Promise' 中?", "Expected_0_type_arguments_but_got_1_2558": "應有 {0} 個型別引數，但得到 {1} 個。", "Expected_0_type_arguments_provide_these_with_an_extends_tag_8026": "必須是 {0} 型別引數; 請提供有 '@ extends' 標記的這類引數。", "Expected_1_argument_but_got_0_new_Promise_needs_a_JSDoc_hint_to_produce_a_resolve_that_can_be_called_2810": "需要 1 個引數，但得到 0。'new Promise()' 需要 JSDoc 提示，才能產生可以呼叫而不含引數的 'resolve'。", "Expected_a_Unicode_property_name_1523": "必須是 Unicode 屬性名稱。", "Expected_a_Unicode_property_name_or_value_1527": "必須是 Unicode 屬性名稱或值。", "Expected_a_Unicode_property_value_1525": "必須是 Unicode 屬性值。", "Expected_a_capturing_group_name_1514": "必須是擷取群組名稱。", "Expected_a_class_set_operand_1520": "必須是類別集運算元。", "Expected_at_least_0_arguments_but_got_1_2555": "至少應有 {0} 個引數，但得到 {1} 個。", "Expected_corresponding_JSX_closing_tag_for_0_17002": "'{0}' 需要對應的 JSX 結尾標記。", "Expected_corresponding_closing_tag_for_JSX_fragment_17015": "JSX 片段必須有對應的結尾標記。", "Expected_for_property_initializer_1442": "屬性初始設定式必須是 '='。", "Expected_type_of_0_field_in_package_json_to_be_1_got_2_6105": "在 'package.json' 中 '{0}' 欄位的類型必須為 '{1}'，但取得 '{2}'。", "Explicitly_specified_module_resolution_kind_Colon_0_6087": "明確指定的模組解析種類: '{0}'。", "Exponentiation_cannot_be_performed_on_bigint_values_unless_the_target_option_is_set_to_es2016_or_lat_2791": "'target' 選項必須設定為 'es2016' 或更新版本，才可以對 'bigint' 值執行乘冪運算。", "Export_0_from_module_1_90059": "從模組 '{1}' 匯出 '{0}'", "Export_all_referenced_locals_90060": "匯出所有參考的本機", "Export_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_export_default_or__1203": "當目標為 ECMAScript 模組時，無法使用匯出指派。請考慮改用 'export default' 或其他模組格式。", "Export_assignment_is_not_supported_when_module_flag_is_system_1218": "當 '--module' 旗標為 'system' 時，不支援匯出指派。", "Export_declaration_conflicts_with_exported_declaration_of_0_2484": "匯出宣告與匯出的 '{0}' 宣告相衝突。", "Export_declarations_are_not_permitted_in_a_namespace_1194": "在命名空間中不可使用匯出宣告。", "Export_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6276": "在路徑 '{0}' 的 package.js 範圍中不存在匯出指定名稱 '{1}'。", "Exported_type_alias_0_has_or_is_using_private_name_1_4081": "匯出的類型別名 '{0}' 具有或使用私用名稱 '{1}'。", "Exported_type_alias_0_has_or_is_using_private_name_1_from_module_2_4084": "匯出的類型別名 '{0}' 具有或使用模組 {2} 中的私人名稱 '{1}'。", "Exported_variable_0_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4023": "匯出的變數 '{0}' 具有或使用外部模組 {2} 中的名稱 '{1}'，但無法命名。", "Exported_variable_0_has_or_is_using_name_1_from_private_module_2_4024": "匯出的變數 '{0}' 具有或使用私用模組 {2} 中的名稱 '{1}'。", "Exported_variable_0_has_or_is_using_private_name_1_4025": "匯出的變數 '{0}' 具有或使用私用名稱 '{1}'。", "Exports_and_export_assignments_are_not_permitted_in_module_augmentations_2666": "模組增強指定中不允許匯出及匯出指派。", "Expression_expected_1109": "必須是運算式。", "Expression_must_be_enclosed_in_parentheses_to_be_used_as_a_decorator_1497": "表達式必須包含在括弧內，才能做為裝飾項目使用。", "Expression_or_comma_expected_1137": "必須是運算式或逗號。", "Expression_produces_a_tuple_type_that_is_too_large_to_represent_2800": "運算式產生的元組類型太大而無法表示。", "Expression_produces_a_union_type_that_is_too_complex_to_represent_2590": "運算式產生的等位型別過於複雜而無法表示。", "Expression_resolves_to_super_that_compiler_uses_to_capture_base_class_reference_2402": "運算式會解析成 '_super'，而編譯器會使用其來擷取基底類別參考。", "Expression_resolves_to_variable_declaration_newTarget_that_compiler_uses_to_capture_new_target_meta__2544": "運算式將解析成變數宣告 '_newTarget'，而供編譯器用來擷取 'new.target' 中繼屬性參考。", "Expression_resolves_to_variable_declaration_this_that_compiler_uses_to_capture_this_reference_2400": "運算式會解析成變數宣告 '_this'，而編譯器會使用此宣告來擷取 'this' 參考 。", "Expression_type_can_t_be_inferred_with_isolatedDeclarations_9013": "無法使用 --isolatedDeclarations 推斷運算式類型。", "Extends_clause_can_t_contain_an_expression_with_isolatedDeclarations_9021": "擴充子句不能包含具有 --isolatedDeclarations 的運算式。", "Extends_clause_for_inferred_type_0_has_or_is_using_private_name_1_4085": "推斷類型 '{0}' 的 'Extends' 子句包含或使用了私人名稱 '{1}'。", "Extract_base_class_to_variable_90064": "擷取基底類別至變數", "Extract_binding_expressions_to_variable_90066": "將繫結運算式擷取至變數", "Extract_constant_95006": "擷取常數", "Extract_default_export_to_variable_90065": "擷取預設匯出至變數", "Extract_function_95005": "擷取函式", "Extract_to_0_in_1_95004": "擷取至 {1} 中的 {0}", "Extract_to_0_in_1_scope_95008": "擷取至 {1} 範圍中的 {0}", "Extract_to_0_in_enclosing_scope_95007": "擷取至封閉式範圍中的 {0}", "Extract_to_interface_95090": "擷取至介面", "Extract_to_type_alias_95078": "擷取至類型別名", "Extract_to_typedef_95079": "擷取至 typedef", "Extract_to_variable_and_replace_with_0_as_typeof_0_90069": "擷取至變數，並以 '{0} as typeof {0}' 取代", "Extract_type_95077": "擷取類型", "FILE_6035": "檔案", "FILE_OR_DIRECTORY_6040": "檔案或目錄", "Failed_to_find_peerDependency_0_6283": "找不到 peerDependency '{0}'。", "Failed_to_resolve_under_condition_0_6415": "在條件 '{0}' 下解析失敗。", "Fallthrough_case_in_switch_7029": "參數中的 fallthrough 案例。", "File_0_does_not_exist_6096": "檔案 '{0}' 不存在。", "File_0_does_not_exist_according_to_earlier_cached_lookups_6240": "根據之前快取的查閱，檔案 '{0}' 不存在。", "File_0_exists_according_to_earlier_cached_lookups_6239": "根據之前快取的查閱，檔案 '{0}' 存在。", "File_0_exists_use_it_as_a_name_resolution_result_6097": "檔案 '{0}' 存在 - 將其做為名稱解析結果使用。", "File_0_has_an_unsupported_extension_The_only_supported_extensions_are_1_6054": "不支援檔案 '{0}' 的副檔名。支援的副檔名只有 {1}。", "File_0_is_a_JavaScript_file_Did_you_mean_to_enable_the_allowJs_option_6504": "檔案 '{0}' 為 JavaScript 檔案。您是要啟用 'allowJs' 選項嗎?", "File_0_is_not_a_module_2306": "檔案 '{0}' 不是模組。", "File_0_is_not_listed_within_the_file_list_of_project_1_Projects_must_list_all_files_or_use_an_includ_6307": "檔案 '{0}' 未列於專案 '{1}' 的檔案清單內。專案必須列出所有檔案，或使用 'include' 模式。", "File_0_is_not_under_rootDir_1_rootDir_is_expected_to_contain_all_source_files_6059": "檔案 '{0}' 不在 'rootDir' '{1}' 之下。'rootDir' 必須包含所有原始程式檔。", "File_0_not_found_6053": "找不到檔案 '{0}'。", "File_Management_6245": "檔案管理", "File_appears_to_be_binary_1490": "檔案顯示為二進位。", "File_change_detected_Starting_incremental_compilation_6032": "偵測到檔案變更。正在啟動累加編譯...", "File_is_CommonJS_module_because_0_does_not_have_field_type_1460": "檔案是 CommonJS 模組，因為 '{0}' 沒有 \"type\" 欄位", "File_is_CommonJS_module_because_0_has_field_type_whose_value_is_not_module_1459": "檔案是 CommonJS 模組，因為 '{0}' 具有值不是 \"module\" 的 \"type\" 欄位", "File_is_CommonJS_module_because_package_json_was_not_found_1461": "檔案是 CommonJS 模組，因為找不到 'package.json'", "File_is_ECMAScript_module_because_0_has_field_type_with_value_module_1458": "檔案是 ECMAScript 模組，因為 '{0}' 具有值不是 \"module\" 的 \"type\" 欄位", "File_is_a_CommonJS_module_it_may_be_converted_to_an_ES_module_80001": "檔案為 CommonJS 模組; 其可轉換為 ES 模組。", "File_is_default_library_for_target_specified_here_1426": "檔案是此處指定目標的預設程式庫。", "File_is_entry_point_of_type_library_specified_here_1419": "檔案是此處指定型別程式庫的進入點。", "File_is_included_via_import_here_1399": "檔案透過匯入加入此處。", "File_is_included_via_library_reference_here_1406": "檔案透過程式庫參考加入此處。", "File_is_included_via_reference_here_1401": "檔案透過參考加入此處。", "File_is_included_via_type_library_reference_here_1404": "檔案透過型別程式庫參考加入此處。", "File_is_library_specified_here_1423": "檔案是此處指定的程式庫。", "File_is_matched_by_files_list_specified_here_1410": "檔案會依此處指定的 'files' 清單比對。", "File_is_matched_by_include_pattern_specified_here_1408": "檔案會依此處指定的包含模式比對。", "File_is_output_from_referenced_project_specified_here_1413": "檔案是此處指定的參考專案輸出。", "File_is_output_of_project_reference_source_0_1428": "檔案是專案參考來源 '{0}' 的輸出", "File_is_source_from_referenced_project_specified_here_1416": "檔案是此處指定參考專案的來源。", "File_name_0_differs_from_already_included_file_name_1_only_in_casing_1149": "檔案名稱 '{0}' 與包含的檔案名稱 '{1}' 只差在大小寫。", "File_name_0_has_a_1_extension_looking_up_2_instead_6262": "檔案名稱 '{0}' 具有 '{1}' 延伸模組 - 改為查詢 '{2}'。", "File_name_0_has_a_1_extension_stripping_it_6132": "檔案名稱 '{0}' 的副檔名為 '{1}'。正予以移除。", "File_redirects_to_file_0_1429": "檔案會重新導向檔案 '{0}'", "File_specification_cannot_contain_a_parent_directory_that_appears_after_a_recursive_directory_wildca_5065": "檔案規格不得包含出現在遞迴目錄萬用字元 ('**') 之後的父目錄 ('..'): '{0}'。", "File_specification_cannot_end_in_a_recursive_directory_wildcard_Asterisk_Asterisk_Colon_0_5010": "檔案規格不能以遞迴目錄萬用字元 ('**') 結尾: '{0}'。", "Filters_results_from_the_include_option_6627": "從 `include` 選項篩選結果。", "Fix_all_detected_spelling_errors_95026": "修正偵測到的所有拼字錯誤", "Fix_all_expressions_possibly_missing_await_95085": "修正所有可能缺少 'await' 的運算式", "Fix_all_implicit_this_errors_95107": "修正所有隱含 'this' 的錯誤", "Fix_all_incorrect_return_type_of_an_async_functions_90037": "修正非同步函式所有不正確的傳回型別", "Fix_all_with_type_only_imports_95182": "使用僅限類型匯入來修正所有問題", "Found_0_errors_6217": "找到 {0} 個錯誤。", "Found_0_errors_Watching_for_file_changes_6194": "找到 {0} 個錯誤。正在監看檔案變更。", "Found_0_errors_in_1_files_6261": "在 {1} 檔案中發現 {0} 個錯誤。", "Found_0_errors_in_the_same_file_starting_at_Colon_1_6260": "在同一個檔案中發現 {0} 個錯誤，開始位置: {1}", "Found_1_error_6216": "找到 1 個錯誤。", "Found_1_error_Watching_for_file_changes_6193": "找到 1 個錯誤。正在監看檔案變更。", "Found_1_error_in_0_6259": "在 {0} 找到 1 個錯誤", "Found_package_json_at_0_6099": "在 '{0}' 找到 'package.json'。", "Found_peerDependency_0_with_1_version_6282": "找到版本為 '{1}' 的 peerDependency '{0}'。", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_1250": "以 'ES5' 為目標時，strict 模式下的區塊中不允許函式宣告。", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_Class_definiti_1251": "以 'ES5' 為目標時，strict 模式下的區塊中不允許函式宣告。類別定義會自動進入 strict 模式。", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_Modules_are_au_1252": "以 'ES5' 為目標時，strict 模式下的區塊中不允許函式宣告。模組會自動進入 strict 模式。", "Function_expression_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7011": "缺少傳回型別註解的函式運算式隱含了 '{0}' 傳回型別。", "Function_implementation_is_missing_or_not_immediately_following_the_declaration_2391": "遺漏函式實作，或函式實作未緊接在宣告之後。", "Function_implementation_name_must_be_0_2389": "函式實作名稱必須是 '{0}'。", "Function_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_ref_7024": "函式因為沒有傳回型別註解，並在其中一個傳回運算式中直接或間接參考了自己，所以隱含了傳回型別 'any'。", "Function_lacks_ending_return_statement_and_return_type_does_not_include_undefined_2366": "函式缺少結束 return 陳述式，且傳回類型不包括 'undefined'。", "Function_must_have_an_explicit_return_type_annotation_with_isolatedDeclarations_9007": "函式必須有具備 --isolatedDeclarations 的明確傳回型別註解。", "Function_not_implemented_95159": "未實作函式。", "Function_overload_must_be_static_2387": "函式多載必須為靜態。", "Function_overload_must_not_be_static_2388": "函式多載不可為靜態。", "Function_type_notation_must_be_parenthesized_when_used_in_a_union_type_1385": "在等位型別中使用函式類型標記法時，必須括以括弧。", "Function_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1387": "在交集型別中使用函式類型標記法時，必須括以括弧。", "Function_type_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7014": "缺少傳回型別註解的函式類型隱含 '{0}' 傳回型別。", "Function_with_bodies_can_only_merge_with_classes_that_are_ambient_2814": "包含主體的函式只能與屬於環境的類別合併。", "Generate_d_ts_files_from_TypeScript_and_JavaScript_files_in_your_project_6612": "從專案中的 TypeScript 和 JavaScript 檔案產生 .d.ts 檔案。", "Generate_get_and_set_accessors_95046": "產生 'get' 與 'set' 存取子", "Generate_get_and_set_accessors_for_all_overriding_properties_95119": "為所有覆寫屬性產生 'get' 和 'set' 存取子", "Generates_a_CPU_profile_6223": "產生 CPU 設定檔。", "Generates_a_sourcemap_for_each_corresponding_d_ts_file_6000": "為每個相對應的 '.d.ts' 檔案產生 sourcemap。", "Generates_an_event_trace_and_a_list_of_types_6237": "產生事件追蹤與類型清單。", "Generates_corresponding_d_ts_file_6002": "產生對應的 '.d.ts' 檔案。", "Generates_corresponding_map_file_6043": "產生對應的 '.map' 檔案。", "Generator_implicitly_has_yield_type_0_Consider_supplying_a_return_type_annotation_7025": "產生器隱含的 yield 類型為 '{0}'。請考慮提供傳回型別註解。", "Generators_are_not_allowed_in_an_ambient_context_1221": "環境內容中不允許產生器。", "Generic_type_0_requires_1_type_argument_s_2314": "泛型類型 '{0}' 需要 {1} 個型別引數。", "Generic_type_0_requires_between_1_and_2_type_arguments_2707": "泛型型別 '{0}' 需要介於 {1} 和 {2} 之間的型別引數。", "Global_module_exports_may_only_appear_at_top_level_1316": "全域模組匯出只能顯示在最上層。", "Global_module_exports_may_only_appear_in_declaration_files_1315": "全域模組匯出只能顯示在宣告檔案中。", "Global_module_exports_may_only_appear_in_module_files_1314": "全域模組匯出只能顯示在模組檔案中。", "Global_type_0_must_be_a_class_or_interface_type_2316": "全域類型 '{0}' 必須是類別或介面類型。", "Global_type_0_must_have_1_type_parameter_s_2317": "全域類型 '{0}' 必須要有 {1} 個型別參數。", "Have_recompiles_in_incremental_and_watch_assume_that_changes_within_a_file_will_only_affect_files_di_6384": "於 '--incremental' 與 '--watch' 中重新編譯時，會假設檔案中的變更只會影響直接相依於重新編譯的檔案。", "Have_recompiles_in_projects_that_use_incremental_and_watch_mode_assume_that_changes_within_a_file_wi_6606": "在使用 'incremental' 與 'watch' 模式的專案中重新編譯，會假設檔案中的變更只會影響直接相依於重新編譯的檔案。", "Hexadecimal_digit_expected_1125": "必須適十六進位數字。", "Identifier_expected_0_is_a_reserved_word_at_the_top_level_of_a_module_1262": "需要識別碼。'{0}' 是模組的頂層保留字。", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_1212": "必須是識別碼。'{0}' 在 strict 模式中為保留字。", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Class_definitions_are_automatically_in_stric_1213": "必須是識別碼。'{0}' 是 strict 模式中的保留字。類別定義會自動採用 strict 模式。", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Modules_are_automatically_in_strict_mode_1214": "需要識別碼。'{0}' 是 strict 模式中的保留字。模組會自動採用 strict 模式。", "Identifier_expected_0_is_a_reserved_word_that_cannot_be_used_here_1359": "必須為識別碼。'{0}' 為保留字，此處不可使用。", "Identifier_expected_1003": "必須是識別碼。", "Identifier_expected_esModule_is_reserved_as_an_exported_marker_when_transforming_ECMAScript_modules_1216": "必須有識別碼。'__esModule' 已保留為轉換 ECMAScript 模組時匯出的標記。", "Identifier_or_string_literal_expected_1478": "需要識別碼或字串常值。", "Identifier_string_literal_or_number_literal_expected_1496": "必須是識別碼、字串常值或數字常值。", "If_the_0_package_actually_exposes_this_module_consider_sending_a_pull_request_to_amend_https_Colon_S_7040": "如果「{0}」套件實際上公開了此模組，請考慮傳送提取要求以修改 「https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/{1}」", "If_the_0_package_actually_exposes_this_module_try_adding_a_new_declaration_d_ts_file_containing_decl_7058": "如果 '{0}' 套件的確公開了此模組，請嘗試新增包含 `declare module '{1}';` 的宣告 (.d.ts) 檔案。", "Ignore_this_error_message_90019": "略過此錯誤訊息", "Ignoring_tsconfig_json_compiles_the_specified_files_with_default_compiler_options_6924": "忽略 tsconfig.json，使用預設編譯器選項編譯指定的檔案。", "Implement_all_inherited_abstract_classes_95040": "實作所有繼承的抽象類別", "Implement_all_unimplemented_interfaces_95032": "實作所有未實作的介面", "Implement_inherited_abstract_class_90007": "實作已繼承的抽象類別", "Implement_interface_0_90006": "實作介面 '{0}'", "Implements_clause_of_exported_class_0_has_or_is_using_private_name_1_4019": "匯出類別 '{0}' 的 Implements 子句具有或使用私用名稱 '{1}'。", "Implicit_conversion_of_a_symbol_to_a_string_will_fail_at_runtime_Consider_wrapping_this_expression_i_2731": "在執行階段中無法將 'symbol' 隱含轉換為 'string'。請考慮將此運算式包裝在 'String(...)' 中。", "Import_0_conflicts_with_global_value_used_in_this_file_so_must_be_declared_with_a_type_only_import_w_2866": "匯入 '{0}' 會與此檔案中使用的全域值發生衝突，因此在啟用 'isolatedModules' 時，必須使用僅限類型的匯入宣告。", "Import_0_conflicts_with_local_value_so_must_be_declared_with_a_type_only_import_when_isolatedModules_2865": "匯入 '{0}' 會與本機值發生衝突，因此在啟用 'isolatedModules' 時，必須使用僅限類型的匯入宣告。", "Import_0_from_1_90013": "從 \"{1}\" 匯入 '{0}'", "Import_assertion_values_must_be_string_literal_expressions_2837": "匯入判斷提示值必須是字串常值運算式。", "Import_assertions_are_not_allowed_on_statements_that_compile_to_CommonJS_require_calls_2836": "編譯為 CommonJS 'require' 呼叫的陳述式上不允許匯入判斷提示。", "Import_assertions_are_only_supported_when_the_module_option_is_set_to_esnext_node18_nodenext_or_pres_2821": "只有當 '--module' 選項設定為 'esnext'、'node18'、'nodenext' 或 'preserve' 時，才支援匯入判斷提示。", "Import_assertions_cannot_be_used_with_type_only_imports_or_exports_2822": "匯入判斷提示不能與僅限類型的匯入或匯出搭配使用。", "Import_assertions_have_been_replaced_by_import_attributes_Use_with_instead_of_assert_2880": "匯入宣告已由匯入屬性取代。使用 『with』 而非 'assert'。", "Import_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_import_Asterisk_as_1202": "當目標為 ECMAScript 模組時，無法使用匯入指派。請考慮改用 'import * as ns from \"mod\"'、'import {a} from \"mod\"'、'import d from \"mod\"' 或其他模組格式。", "Import_attribute_values_must_be_string_literal_expressions_2858": "匯入屬性值必須是字串常值運算式。", "Import_attributes_are_not_allowed_on_statements_that_compile_to_CommonJS_require_calls_2856": "編譯為 CommonJS 'require' 呼叫的陳述式上不允許匯入屬性。", "Import_attributes_are_only_supported_when_the_module_option_is_set_to_esnext_node18_nodenext_or_pres_2823": "只有當 '--module' 選項設定為 'esnext'、'node18'、'nodenext' 或 'preserve’ 時，才支援匯入屬性。", "Import_attributes_cannot_be_used_with_type_only_imports_or_exports_2857": "匯入屬性不能與僅限類型的匯入或匯出搭配使用。", "Import_declaration_0_is_using_private_name_1_4000": "匯入宣告 '{0}' 使用私用名稱 '{1}'。", "Import_declaration_conflicts_with_local_declaration_of_0_2440": "匯入宣告與 '{0}' 的區域宣告衝突。", "Import_declarations_in_a_namespace_cannot_reference_a_module_1147": "命名空間中的匯入宣告不得參考模組。", "Import_emit_helpers_from_tslib_6139": "從 'tslib' 匯入發出協助程式。", "Import_may_be_converted_to_a_default_import_80003": "匯入可轉換成預設匯入。", "Import_name_cannot_be_0_2438": "匯入名稱不得為 '{0}'。", "Import_or_export_declaration_in_an_ambient_module_declaration_cannot_reference_module_through_relati_2439": "環境模組宣告中的匯入或匯出宣告，不得透過相對模組名稱參考模組。", "Import_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6271": "在路徑 '{0}' 的 package.js 範圍中不存在匯入指定名稱 '{1}'。", "Imported_via_0_from_file_1_1393": "透過 {0} 從檔案 '{1}' 匯入", "Imported_via_0_from_file_1_to_import_importHelpers_as_specified_in_compilerOptions_1395": "透過 {0} 從檔案 '{1}' 匯入，以 CompilerOptions 指定的方式匯入 'ImportHelpers'", "Imported_via_0_from_file_1_to_import_jsx_and_jsxs_factory_functions_1397": "透過 {0} 從檔案 '{1}' 匯入，匯入 'jsx' 和 'jsxs' 處理站函式", "Imported_via_0_from_file_1_with_packageId_2_1394": "透過 {0} 從檔案 '{1}' (packageId 為 '{2}') 匯入", "Imported_via_0_from_file_1_with_packageId_2_to_import_importHelpers_as_specified_in_compilerOptions_1396": "透過 {0} 從檔案 '{1}' (packageId 為 '{2}') 匯入，以 CompilerOptions 指定的方式匯入 'ImportHelpers'", "Imported_via_0_from_file_1_with_packageId_2_to_import_jsx_and_jsxs_factory_functions_1398": "透過 {0} 從檔案 '{1}' (packageId 為 '{2}') 匯入，匯入 'jsx' 和 'jsxs' 處理站函式", "Importing_a_JSON_file_into_an_ECMAScript_module_requires_a_type_Colon_json_import_attribute_when_mod_1543": "當 'module' 設定為 '{0}' 時，匯入 JSON 檔案至 ECMAScript 模組需要 'type: \"json\"' 匯入屬性。", "Imports_are_not_permitted_in_module_augmentations_Consider_moving_them_to_the_enclosing_external_mod_2667": "模組增強指定中不允許匯入。請考慮將其移至封入外部模組。", "In_ambient_enum_declarations_member_initializer_must_be_constant_expression_1066": "在環境列舉宣告中，成員初始設定式必須是常數運算式。", "In_an_enum_with_multiple_declarations_only_one_declaration_can_omit_an_initializer_for_its_first_enu_2432": "在具有多個宣告的列舉中，只有一個宣告可以在其第一個列舉項目中省略初始設定式。", "Include_a_list_of_files_This_does_not_support_glob_patterns_as_opposed_to_include_6635": "包含檔案的清單。這不支援 Glob 模式，與 `include` 相反。", "Include_modules_imported_with_json_extension_6197": "包含匯入有 '.json' 延伸模組的模組", "Include_source_code_in_the_sourcemaps_inside_the_emitted_JavaScript_6644": "在發出 JavaScript 內的 sourcemap 中包含原始程式碼。", "Include_sourcemap_files_inside_the_emitted_JavaScript_6643": "在發出的 JavaScript 中包含 sourcemap 檔案。", "Includes_imports_of_types_referenced_by_0_90054": "包括 '{0}' 參考的類型匯入", "Including_watch_w_will_start_watching_the_current_project_for_the_file_changes_Once_set_you_can_conf_6914": "包括 --watch、-w 會開始監看目前專案是否有檔案變更。設定之後，您便可以使用以下來設定監看式模式:", "Incomplete_quantifier_Digit_expected_1505": "不完整的數量詞。必須是數字。", "Index_signature_for_type_0_is_missing_in_type_1_2329": "類型 '{0}' 的索引簽章在類型 '{1}' 中遺失。", "Index_signature_in_type_0_only_permits_reading_2542": "類型 '{0}' 中的索引簽章只允許讀取。", "Individual_declarations_in_merged_declaration_0_must_be_all_exported_or_all_local_2395": "合併宣告 '{0}' 中的個別宣告必須全部匯出或全在本機上。", "Infer_all_types_from_usage_95023": "從用法推斷所有類型", "Infer_function_return_type_95148": "推斷函式傳回型別", "Infer_parameter_types_from_usage_95012": "從使用方式推斷參數類型", "Infer_this_type_of_0_from_usage_95080": "從使用方式推斷 '{0}' 的 'this' 類型", "Infer_type_of_0_from_usage_95011": "從使用方式推斷 '{0}' 的類型", "Inference_from_class_expressions_is_not_supported_with_isolatedDeclarations_9022": "--isolatedDeclarations 不支援來自類別運算式的推斷。", "Initialize_property_0_in_the_constructor_90020": "將建構函式中的屬性 '{0}' 初始化", "Initialize_static_property_0_90021": "將靜態屬性 '{0}' 初始化", "Initializer_for_property_0_2811": "屬性 '{0}' 的初始設定式", "Initializer_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2301": "執行個體成員變數 '{0}' 的初始設定式不得參考建構函式中所宣告的識別碼 '{1}'。", "Initializers_are_not_allowed_in_ambient_contexts_1039": "環境內容中不得有初始設定式。", "Initializes_a_TypeScript_project_and_creates_a_tsconfig_json_file_6070": "初始化 TypeScript 專案並建立 tsconfig.json 檔案。", "Inline_variable_95184": "內嵌變數", "Insert_command_line_options_and_files_from_a_file_6030": "從檔案插入命令列選項與檔案。", "Install_0_95014": "安裝 '{0}'", "Install_all_missing_types_packages_95033": "安裝缺少的所有類型套件", "Interface_0_cannot_simultaneously_extend_types_1_and_2_2320": "介面 '{0}' 不能同時擴充類型 '{1}' 和 '{2}'。", "Interface_0_incorrectly_extends_interface_1_2430": "介面 '{0}' 不正確地擴充介面 '{1}'。", "Interface_declaration_cannot_have_implements_clause_1176": "介面宣告不能有 'implements' 子句。", "Interface_must_be_given_a_name_1438": "必須為介面指定名稱。", "Interface_name_cannot_be_0_2427": "介面名稱不得為 '{0}'。", "Interop_Constraints_6252": "Interop 條件約束", "Interpret_optional_property_types_as_written_rather_than_adding_undefined_6243": "將選用屬性類型解譯為寫入，而非新增 'undefined'。", "Invalid_character_1127": "無效的字元。", "Invalid_import_specifier_0_has_no_possible_resolutions_6272": "無效的匯入指定名稱 '{0}' 沒有可能的解決方法。", "Invalid_module_name_in_augmentation_Module_0_resolves_to_an_untyped_module_at_1_which_cannot_be_augm_2665": "增強中的模組名稱無效。模組 '{0}' 於 '{1}' 解析至不具類型的模組，其無法擴增。", "Invalid_module_name_in_augmentation_module_0_cannot_be_found_2664": "增強指定中的模組名稱無效，找不到模組 '{0}'。", "Invalid_optional_chain_from_new_expression_Did_you_mean_to_call_0_1209": "新運算式的選擇性鏈結無效。您想要呼叫 '{0}()' 嗎?", "Invalid_reference_directive_syntax_1084": "無效的 'reference' 指示詞語法。", "Invalid_syntax_in_decorator_1498": "裝飾項目中無效的語法。", "Invalid_use_of_0_It_cannot_be_used_inside_a_class_static_block_18039": "'{0}' 的使用無效。不能在類別靜態區塊內使用。", "Invalid_use_of_0_Modules_are_automatically_in_strict_mode_1215": "'{0}' 的用法無效。模組會自動採用 strict 模式。", "Invalid_use_of_0_in_strict_mode_1100": "在 strict 模式中使用 '{0}' 無效。", "Invalid_value_for_ignoreDeprecations_5103": "'--ignoreDeprecations' 的值無效。", "Invalid_value_for_jsxFactory_0_is_not_a_valid_identifier_or_qualified_name_5067": "'jsxFactory' 的值無效。'{0}' 不是有效的識別碼或限定名稱。", "Invalid_value_for_jsxFragmentFactory_0_is_not_a_valid_identifier_or_qualified_name_18035": "'jsxFragmentFactory' 的值無效。'{0}' 不是有效的識別碼或限定名稱。", "Invalid_value_for_reactNamespace_0_is_not_a_valid_identifier_5059": "'--reactNamespace' 的值無效。'{0}' 不是有效的識別碼。", "It_is_likely_that_you_are_missing_a_comma_to_separate_these_two_template_expressions_They_form_a_tag_2796": "可能是未使用逗號分隔這兩個範本運算式，因而形成了附加標籤的範本運算式，導致無法叫用。", "Its_element_type_0_is_not_a_valid_JSX_element_2789": "其元素類型 '{0}' 不是有效的 JSX 元素。", "Its_instance_type_0_is_not_a_valid_JSX_element_2788": "其執行個體類型 '{0}' 不是有效的 JSX 元素。", "Its_return_type_0_is_not_a_valid_JSX_element_2787": "其傳回型別 '{0}' 不是有效的 JSX 元素。", "Its_type_0_is_not_a_valid_JSX_element_type_18053": "其類型 '{0}' 不是有效的 JSX 元素類型。", "JSDoc_0_1_does_not_match_the_extends_2_clause_8023": "JSDoc '@{0} {1}' 不符合 'extends {2}' 子句。", "JSDoc_0_is_not_attached_to_a_class_8022": "JSDoc ''@{0}' 未連結到類別。", "JSDoc_may_only_appear_in_the_last_parameter_of_a_signature_8028": "JSDoc '...' 只能出現在特徵標記的最後一個參數中。", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_8024": "JSDoc '@param' 標記的名稱為 '{0}'，但沒有為該名稱的參數。", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_It_would_match_arguments_if_it_h_8029": "JSDoc '@param' 標籤的名稱為 '{0}'，但沒有任何參數使用該名稱。如有陣列類型，則會與 'arguments' 相符。", "JSDoc_typedef_may_be_converted_to_TypeScript_type_80009": "JSDoc typedef 可以轉換成 TypeScript 類型。", "JSDoc_typedef_tag_should_either_have_a_type_annotation_or_be_followed_by_property_or_member_tags_8021": "JSDoc '@typedef' 標記應具有類型註解，或者其後接著 '@property' 或 '@member' 標記。", "JSDoc_typedefs_may_be_converted_to_TypeScript_types_80010": "JSDoc typedefs 可以轉換成 TypeScript 類型。", "JSDoc_types_can_only_be_used_inside_documentation_comments_8020": "JSDoc 類型只能在文件註解中使用。", "JSDoc_types_may_be_moved_to_TypeScript_types_80004": "JSDoc 類型可移為 TypeScript 類型。", "JSX_attributes_must_only_be_assigned_a_non_empty_expression_17000": "只能將非空白的 'expression' 指派給 JSX 屬性。", "JSX_element_0_has_no_corresponding_closing_tag_17008": "JSX 元素 '{0}' 沒有對應的結尾標記。", "JSX_element_class_does_not_support_attributes_because_it_does_not_have_a_0_property_2607": "因為 JSX 項目類別沒有 '{0}' 屬性 (property)，所以不支援屬性 (attribute)。", "JSX_element_implicitly_has_type_any_because_no_interface_JSX_0_exists_7026": "因為沒有介面 'JSX.{0}'，表示 JSX 項目隱含了類型 'any'。", "JSX_element_implicitly_has_type_any_because_the_global_type_JSX_Element_does_not_exist_2602": "因為全域類型 'JSX.Element' 不存在，所以 JSX 項目隱含有類型 'any'。", "JSX_element_type_0_does_not_have_any_construct_or_call_signatures_2604": "JSX 項目類型 '{0}' 沒有任何建構或呼叫簽章。", "JSX_elements_cannot_have_multiple_attributes_with_the_same_name_17001": "JSX 項目不得有多個同名的屬性。", "JSX_expressions_may_not_use_the_comma_operator_Did_you_mean_to_write_an_array_18007": "JSX 運算式不可使用逗號運算子。您是要寫入陣列嗎?", "JSX_expressions_must_have_one_parent_element_2657": "JSX 運算式必須具有一個父元素。", "JSX_fragment_has_no_corresponding_closing_tag_17014": "JSX 片段沒有對應的結尾標記。", "JSX_property_access_expressions_cannot_include_JSX_namespace_names_2633": "JSX 屬性存取運算式不能包含 JSX 命名空間名稱", "JSX_spread_child_must_be_an_array_type_2609": "JSX 擴張子系必須為陣列類型。", "JavaScript_Support_6247": "JavaScript 支援", "Jump_target_cannot_cross_function_boundary_1107": "跳躍目標不得跨越函式界限。", "KIND_6034": "類型", "Keywords_cannot_contain_escape_characters_1260": "關鍵字不可包含逸出字元。", "LOCATION_6037": "位置", "Language_and_Environment_6254": "語言及環境", "Left_side_of_comma_operator_is_unused_and_has_no_side_effects_2695": "逗號運算子左側未使用，而且沒有任何不良影響。", "Library_0_specified_in_compilerOptions_1422": "CompilerOptions 中指定的程式庫 '{0}'", "Library_referenced_via_0_from_file_1_1405": "透過 '{0}' 從檔案 '{1}' 參考的程式庫", "Line_break_not_permitted_here_1142": "這裡不可使用分行符號。", "Line_terminator_not_permitted_before_arrow_1200": "箭號前不得有行結束字元。", "List_of_file_name_suffixes_to_search_when_resolving_a_module_6931": "解析模組時要搜尋的檔案名尾碼清單。", "List_of_folders_to_include_type_definitions_from_6161": "要包含之類型定義所屬資料夾的清單。", "List_of_root_folders_whose_combined_content_represents_the_structure_of_the_project_at_runtime_6168": "資料夾的清單。這些資料夾內所含的合併內容代表了專案在執行階段時的結果。", "Loading_0_from_the_root_dir_1_candidate_location_2_6109": "正在從根目錄 '{1}'，候選位置 '{2}' 載入 '{0}'。", "Loading_module_0_from_node_modules_folder_target_file_types_Colon_1_6098": "正在從 'node_modules' 資料夾載入模組 '{0}'，目標檔案類型: {1}。", "Loading_module_as_file_Slash_folder_candidate_module_location_0_target_file_types_Colon_1_6095": "正在將模組載入為檔案/資料夾，候選模組位置 '{0}'，目標檔案類型: {1}。", "Locale_must_be_of_the_form_language_or_language_territory_For_example_0_or_1_6048": "地區設定的格式必須是 <語言> 或 <語言>-<國家/地區>。例如 '{0}' 或 '{1}'。", "Log_paths_used_during_the_moduleResolution_process_6706": "在 'moduleResolution' 處理序期間使用的記錄檔路徑。", "Longest_matching_prefix_for_0_is_1_6108": "符合 '{0}' 的前置詞最長為 '{1}'。", "Looking_up_in_node_modules_folder_initial_location_0_6125": "目前正在 'node_modules' 資料夾中查詢，初始位置為 '{0}'。", "Make_all_super_calls_the_first_statement_in_their_constructor_95036": "在其建構函式的第一個陳述式中呼叫所有的 'super()'", "Make_keyof_only_return_strings_instead_of_string_numbers_or_symbols_Legacy_option_6650": "僅讓 keyof 傳回字串，而不是單一字串、數字或符號。舊版選項。", "Make_super_call_the_first_statement_in_the_constructor_90002": "使 'super()' 呼叫成為建構函式中的第一個陳述式", "Mapped_object_type_implicitly_has_an_any_template_type_7039": "對應的物件類型隱含具有 'any' 範本類型。", "Mark_array_literal_as_const_90070": "標記陣列常值為常數", "Matched_0_condition_1_6403": "符合 '{0}' 條件 '{1}'。", "Matched_by_default_include_pattern_Asterisk_Asterisk_Slash_Asterisk_1457": "依預設比對包含模式 '**/*'", "Matched_by_include_pattern_0_in_1_1407": "依 '{1}' 中的包含模式 '{0}' 比對", "Member_0_implicitly_has_an_1_type_7008": "成員 '{0}' 隱含了 '{1}' 類型。", "Member_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7045": "成員 '{0}' 隱含 '{1}' 類型，但可從使用方式推斷更適合的類型。", "Merge_conflict_marker_encountered_1185": "偵測到合併衝突標記。", "Merged_declaration_0_cannot_include_a_default_export_declaration_Consider_adding_a_separate_export_d_2652": "合併宣告 '{0}' 不得包含預設匯出宣告。請考慮改為加入獨立型 'export default {0}' 宣告。", "Meta_property_0_is_only_allowed_in_the_body_of_a_function_declaration_function_expression_or_constru_17013": "只有函式宣告、函式運算式或建構函式的主體中允許中繼屬性 '{0}'。", "Method_0_cannot_have_an_implementation_because_it_is_marked_abstract_1245": "因為方法 '{0}' 已標記為抽象，所以不可具有實作。", "Method_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4101": "匯出介面的方法 '{0}' 具有或使用私用模組 '{2}' 的名稱 '{1}'。", "Method_0_of_exported_interface_has_or_is_using_private_name_1_4102": "匯出介面的方法 '{0}' 具有或使用私用名稱 '{1}'。", "Method_must_have_an_explicit_return_type_annotation_with_isolatedDeclarations_9008": "方法必須有具備 --isolatedDeclarations 的明確傳回型別註解。", "Method_not_implemented_95158": "未實作方法。", "Modifiers_cannot_appear_here_1184": "此處不得出現修飾元。", "Module_0_can_only_be_default_imported_using_the_1_flag_1259": "模組 '{0}' 只能依預設使用 '{1}' 旗標匯入", "Module_0_cannot_be_imported_using_this_construct_The_specifier_only_resolves_to_an_ES_module_which_c_1471": "無法使用此建構匯入模組 '{0}'。指定名稱只能解析成無法以 'require' 匯入的 ES 模組。請改為使用 ECMAScript 匯入。", "Module_0_declares_1_locally_but_it_is_exported_as_2_2460": "模組 '{0}' 區域性地宣告 '{1}'，但卻將該模組匯出為 '{2}'。", "Module_0_declares_1_locally_but_it_is_not_exported_2459": "模組 '{0}' 區域性地宣告 '{1}'，但並未匯出該模組。", "Module_0_does_not_refer_to_a_type_but_is_used_as_a_type_here_Did_you_mean_typeof_import_0_1340": "模組 '{0}' 不是類型，但在此處卻作為類型使用。您是指 'typeof import('{0}')' 嗎?", "Module_0_does_not_refer_to_a_value_but_is_used_as_a_value_here_1339": "模組 '{0}' 未參考任何值，但在此用為值。", "Module_0_has_already_exported_a_member_named_1_Consider_explicitly_re_exporting_to_resolve_the_ambig_2308": "模組 {0} 已匯出名為 '{1}' 的成員。請考慮明確重新匯出項目以解決模稜兩可的情形。", "Module_0_has_no_default_export_1192": "模組 '{0}' 沒有預設匯出。", "Module_0_has_no_default_export_Did_you_mean_to_use_import_1_from_0_instead_2613": "模組 '{0}' 沒有預設匯出。您是要改用 'import { {1} } from {0}' 嗎?", "Module_0_has_no_exported_member_1_2305": "模組 '{0}' 沒有匯出的成員 '{1}'。", "Module_0_has_no_exported_member_1_Did_you_mean_to_use_import_1_from_0_instead_2614": "模組 '{0}' 沒有匯出的成員 '{1}'。您是要改用 'import {1} from {0}' 嗎?", "Module_0_is_hidden_by_a_local_declaration_with_the_same_name_2437": "同名的區域宣告隱藏了模組 '{0}'。", "Module_0_uses_export_and_cannot_be_used_with_export_Asterisk_2498": "模組 '{0}' 使用 'export ='，因而無法以 'export *' 的方式使用。", "Module_0_was_resolved_as_locally_declared_ambient_module_in_file_1_6144": "模組 '{0}' 在檔案 '{1}' 中已解析為本機宣告的環境模組。", "Module_0_was_resolved_to_1_but_allowArbitraryExtensions_is_not_set_6263": "模組 '{0}' 已解析為 '{1}'，但是尚未設定 '--allowArbitraryExtensions'。", "Module_0_was_resolved_to_1_but_jsx_is_not_set_6142": "模組 '{0}' 已解析為 '{1}'，但未設定 '--jsx'。", "Module_0_was_resolved_to_1_but_resolveJsonModule_is_not_used_7042": "模組 '{0}' 已解析為 '{1}'，但未使用 '--resolveJsonModule'。", "Module_declaration_names_may_only_use_or_quoted_strings_1443": "模組宣告名稱只能使用 ' 或 \" 引用的字串。", "Module_name_0_matched_pattern_1_6092": "模組名稱 '{0}'，符合的模式 '{1}'。", "Module_name_0_was_not_resolved_6090": "======== 模組名稱 '{0}' 未解析。========", "Module_name_0_was_successfully_resolved_to_1_6089": "======== 模組名稱 '{0}' 已成功解析為 '{1}'。========", "Module_name_0_was_successfully_resolved_to_1_with_Package_ID_2_6218": "======== 模組名稱 '{0}' 已成功解析為 '{1}'，套件識別碼為 '{2}'。========", "Module_resolution_kind_is_not_specified_using_0_6088": "未指定模組解析種類，將使用 '{0}'。", "Module_resolution_using_rootDirs_has_failed_6111": "使用 'rootDirs' 解析模組失敗。", "Modules_6244": "模組", "Move_labeled_tuple_element_modifiers_to_labels_95117": "將標記的元組元素修飾元移至標籤", "Move_the_expression_in_default_export_to_a_variable_and_add_a_type_annotation_to_it_9036": "將預設匯出中的運算式移動到變數，並在其中新增類型註釋。", "Move_to_a_new_file_95049": "移至新檔", "Move_to_file_95178": "移動至檔案", "Multiple_consecutive_numeric_separators_are_not_permitted_6189": "不允許多個連續的數字分隔符號。", "Multiple_constructor_implementations_are_not_allowed_2392": "不允許多個建構函式實作。", "NEWLINE_6061": "新行", "Name_is_not_valid_95136": "名稱無效", "Named_capturing_groups_are_only_available_when_targeting_ES2018_or_later_1503": "只有以 'ES2018' 或更新版本為目標時，才可以使用具名擷取群組。", "Named_capturing_groups_with_the_same_name_must_be_mutually_exclusive_to_each_other_1515": "具有相同名稱的命名擷取群組必須互相排除。", "Named_imports_from_a_JSON_file_into_an_ECMAScript_module_are_not_allowed_when_module_is_set_to_0_1544": "當 'module' 設定為 '{0}' 時，不允許從 JSON 檔案具名匯入 ECMAScript 模組。", "Named_property_0_of_types_1_and_2_are_not_identical_2319": "類型 '{1}' 及 '{2}' 的具名屬性 '{0}' 不一致。", "Namespace_0_has_no_exported_member_1_2694": "命名空間 '{0}' 沒有匯出的成員 '{1}'。", "Namespace_must_be_given_a_name_1437": "必須為命名空間指定名稱。", "Namespace_name_cannot_be_0_2819": "命名空間名稱不得為 '{0}'。", "Namespaces_are_not_allowed_in_global_script_files_when_0_is_enabled_If_this_file_is_not_intended_to__1280": "啟用 '{0}' 時，在全域指令碼檔案中不允許命名空間。如果此檔案不是預定為全域指令碼，將 'moduleDetection' 設定為 'force'，或新增空白的 'export {}' 陳述式。", "Neither_decorators_nor_modifiers_may_be_applied_to_this_parameters_1433": "裝飾項目和修飾元都無法套用至 'this' 參數。", "No_base_constructor_has_the_specified_number_of_type_arguments_2508": "沒有任何基底建構函式具有指定的類型引數數量。", "No_constituent_of_type_0_is_callable_2755": "無法呼叫 '{0}' 類型的任何構件。", "No_constituent_of_type_0_is_constructable_2759": "無法建構 '{0}' 類型的任何構件。", "No_index_signature_with_a_parameter_of_type_0_was_found_on_type_1_7054": "在類型 '{1}' 中找不到任何具有 '{0}' 類型之參數的索引簽章。", "No_inputs_were_found_in_config_file_0_Specified_include_paths_were_1_and_exclude_paths_were_2_18003": "在設定檔 '{0}' 中找不到任何輸入。指定的 'include' 路徑為 '{1}'，'exclude' 路徑為 '{2}'。", "No_longer_supported_In_early_versions_manually_set_the_text_encoding_for_reading_files_6608": "不再支援。在早期版本中，手動設定讀取檔案的文字編碼方式。", "No_overload_expects_0_arguments_but_overloads_do_exist_that_expect_either_1_or_2_arguments_2575": "沒有任何多載需要 {0} 引數，但有多載需要 {1} 或 {2} 引數。", "No_overload_expects_0_type_arguments_but_overloads_do_exist_that_expect_either_1_or_2_type_arguments_2743": "沒有任何多載需要 {0} 類型引數，但有多載需要 {1} 或 {2} 類型引數。", "No_overload_matches_this_call_2769": "沒有任何多載符合此呼叫。", "No_type_could_be_extracted_from_this_type_node_95134": "無法從此類型節點擷取任何類型", "No_value_exists_in_scope_for_the_shorthand_property_0_Either_declare_one_or_provide_an_initializer_18004": "速記屬性 '{0}' 的範圍中不存在任何值。請宣告一個值或提供初始設定式。", "Non_abstract_class_0_does_not_implement_inherited_abstract_member_1_from_class_2_2515": "非抽象類別 '{0}' 未實作從類別 '{2}' 繼承而來的抽象成員 '{1}'。", "Non_abstract_class_0_is_missing_implementations_for_the_following_members_of_1_Colon_2_2654": "下列 '{1}' 成員缺少非抽象類別 '{0}' 的實作: {2}。", "Non_abstract_class_0_is_missing_implementations_for_the_following_members_of_1_Colon_2_and_3_more_2655": "下列 '{1}' 成員缺少非抽象類別 '{0}' 的實作: {2} 和 {3} 等。", "Non_abstract_class_expression_does_not_implement_inherited_abstract_member_0_from_class_1_2653": "非抽象類別運算式未實作從類別 '{1}' 繼承而來的抽象成員 '{0}'。", "Non_abstract_class_expression_is_missing_implementations_for_the_following_members_of_0_Colon_1_2656": "下列 '{0}' 成員缺少非抽象類別運算式的實作: {1}。", "Non_abstract_class_expression_is_missing_implementations_for_the_following_members_of_0_Colon_1_and__2650": "下列 '{0}' 成員缺少非抽象類別運算式的實作: {1} 和 {2} 等。", "Non_null_assertions_can_only_be_used_in_TypeScript_files_8013": "非 Null 的判斷提示只可用於 TypeScript 檔案中。", "Non_relative_paths_are_not_allowed_when_baseUrl_is_not_set_Did_you_forget_a_leading_Slash_5090": "未設定 'baseUrl' 時，不得使用非相對路徑。是否忘記使用前置 './'?", "Non_simple_parameter_declared_here_1348": "非簡易參數已宣告於此處。", "Not_all_code_paths_return_a_value_7030": "部分程式碼路徑並未傳回值。", "Not_all_constituents_of_type_0_are_callable_2756": "'{0}' 類型的構件並非都可呼叫。", "Not_all_constituents_of_type_0_are_constructable_2760": "'{0}' 類型的構件並非都可構建。", "Numbers_out_of_order_in_quantifier_1506": "數字在數量詞中失序。", "Numeric_literals_with_absolute_values_equal_to_2_53_or_greater_are_too_large_to_be_represented_accur_80008": "絕對值等於或大於 2^53 的數字常值過大，無法準確地表示為整數。", "Numeric_separators_are_not_allowed_here_6188": "這裡不允許數字分隔符號。", "Object_is_of_type_unknown_2571": "物件的類型為 '未知'。", "Object_is_possibly_null_2531": "物件可能為「null」。", "Object_is_possibly_null_or_undefined_2533": "物件可能為「null」或「未定義」。", "Object_is_possibly_undefined_2532": "物件可能為「未定義」。", "Object_literal_may_only_specify_known_properties_and_0_does_not_exist_in_type_1_2353": "物件常值只可指定已知的屬性，且類型 '{1}' 中沒有 '{0}'。", "Object_literal_may_only_specify_known_properties_but_0_does_not_exist_in_type_1_Did_you_mean_to_writ_2561": "物件常值只會指定已知的屬性，但類型 '{1}' 中沒有 '{0}'。您是否想要寫入 '{2}'?", "Object_literal_s_property_0_implicitly_has_an_1_type_7018": "物件常值的屬性 '{0}' 隱含了 '{1}' 類型。", "Objects_that_contain_shorthand_properties_can_t_be_inferred_with_isolatedDeclarations_9016": "無法使用 --isolatedDeclarations 推斷包含速記屬性的物件。", "Objects_that_contain_spread_assignments_can_t_be_inferred_with_isolatedDeclarations_9015": "無法使用 --isolatedDeclarations 推斷包含擴張作業的物件。", "Octal_digit_expected_1178": "必須是八進位數字。", "Octal_escape_sequences_and_backreferences_are_not_allowed_in_a_character_class_If_this_was_intended__1536": "字元類別中不允許八進位逸出序列和反向參考。如果要做為逸出序列，請改用語法 '{0}'。", "Octal_escape_sequences_are_not_allowed_Use_the_syntax_0_1487": "不允許八進位逸出序列。請使用語法 '{0}'。", "Octal_literals_are_not_allowed_Use_the_syntax_0_1121": "不允許八進位常值。請使用語法 '{0}'。", "One_value_of_0_1_is_the_string_2_and_the_other_is_assumed_to_be_an_unknown_numeric_value_4126": "'{0}.{1}' 的一個值是字串 '{2}'，另一個值假設為未知的數值。", "Only_a_single_variable_declaration_is_allowed_in_a_for_in_statement_1091": "'for...in' 陳述式中只可包含一個變數宣告。", "Only_a_single_variable_declaration_is_allowed_in_a_for_of_statement_1188": "'for...of' 陳述式只能包含一個變數宣告。", "Only_a_void_function_can_be_called_with_the_new_keyword_2350": "只有 void 函式可以使用 'new' 關鍵字進行呼叫。", "Only_ambient_modules_can_use_quoted_names_1035": "只有環境模組可以使用括以引號的名稱。", "Only_amd_and_system_modules_are_supported_alongside_0_6082": "只有 'amd' 與 'system' 模組連同受支援 --{0}。", "Only_const_arrays_can_be_inferred_with_isolatedDeclarations_9017": "僅常數陣列可以使用 --isolatedDeclarations 推斷。", "Only_emit_d_ts_declaration_files_6014": "只發出 '.d.ts' 宣告檔案。", "Only_output_d_ts_files_and_not_JavaScript_files_6623": "只輸出 d.ts 檔案，而不是 JavaScript 檔案。", "Only_public_and_protected_methods_of_the_base_class_are_accessible_via_the_super_keyword_2340": "只有基底類別之公開且受保護的方法，才可透過 'super' 關鍵字存取。", "Operator_0_cannot_be_applied_to_type_1_2736": "無法將運算子 '{0}' 套用至類型 '{1}'。", "Operator_0_cannot_be_applied_to_types_1_and_2_2365": "無法將運算子 '{0}' 套用至類型 '{1}' 和 '{2}'。", "Operators_must_not_be_mixed_within_a_character_class_Wrap_it_in_a_nested_class_instead_1519": "運算子不得混合在字元類別中。改為將運算子包裝在巢狀類別中。", "Opt_a_project_out_of_multi_project_reference_checking_when_editing_6619": "在編輯時從多專案參考檢查選擇一個專案。", "Option_0_1_has_been_removed_Please_remove_it_from_your_configuration_5108": "已移除選項 '{0}={1}'。請將該選項從您的設定中移除。", "Option_0_1_is_deprecated_and_will_stop_functioning_in_TypeScript_2_Specify_compilerOption_ignoreDepr_5107": "選項 '{0}={1}' 已被取代，將在 TypeScript {2} 中停止運作。指定 compilerOption '\"ignoreDeprecations\": \"{3}\"' 以沉默此錯誤。", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_false_or_null_on_command_line_6230": "只能在 'tsconfig.json' 檔案中指定 '{0}' 選項，或在命令列上將其設定為 'false' 或 'null'。", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_null_on_command_line_6064": "只能在 'tsconfig.json' 檔案中指定 '{0}' 選項，或在命令列上將其設定為 'null'。", "Option_0_can_only_be_specified_on_command_line_6266": "選項 '{0}' 只能在命令列中指定。", "Option_0_can_only_be_used_when_either_option_inlineSourceMap_or_option_sourceMap_is_provided_5051": "只有在已提供選項 '--inlineSourceMap' 或選項 '--sourceMap' 時，才可使用選項 '{0}'。", "Option_0_can_only_be_used_when_moduleResolution_is_set_to_node16_nodenext_or_bundler_5098": "只有當 'moduleResolution' 設定為 'node16'、'nodenext' 或 'bundler' 時，才可以使用選項 '{0}'。", "Option_0_can_only_be_used_when_module_is_set_to_preserve_or_to_es2015_or_later_5095": "只有當 'module' 設定為 'preserve'、'es2015' 或更新版本時，才能使用 '{0}' 選項。", "Option_0_cannot_be_specified_when_option_jsx_is_1_5089": "當選項 'jsx' 為 '{1}' 時，無法指定選項 '{0}'。", "Option_0_cannot_be_specified_with_option_1_5053": "不得同時指定選項 '{0}' 與選項 '{1}'。", "Option_0_cannot_be_specified_without_specifying_option_1_5052": "必須指定選項 '{1}' 才可指定選項 '{0}'。", "Option_0_cannot_be_specified_without_specifying_option_1_or_option_2_5069": "指定選項 '{0}' 時，必須指定選項 '{1}' 或選項 '{2}'。", "Option_0_has_been_removed_Please_remove_it_from_your_configuration_5102": "已移除選項 '{0}'。請將該選項從您的設定中移除。", "Option_0_is_deprecated_and_will_stop_functioning_in_TypeScript_1_Specify_compilerOption_ignoreDeprec_5101": "選項 '{0}' 已被取代，將在 TypeScript {1} 中停止運作。指定 compilerOption '\"ignoreDeprecations\": \"{2}\"' 以沉默此錯誤。", "Option_0_is_redundant_and_cannot_be_specified_with_option_1_5104": "選項 '{0}' 為多餘且不得與選項 '{1}'一起指定。", "Option_allowImportingTsExtensions_can_only_be_used_when_either_noEmit_or_emitDeclarationOnly_is_set_5096": "僅在已設定 'noEmit' 或 'emitDeclarationOnly' 時，才能使用選項 'allowImportingTsExtensions'。", "Option_build_must_be_the_first_command_line_argument_6369": "選項 '--build' 必須是第一個命令列引數。", "Option_incremental_can_only_be_specified_using_tsconfig_emitting_to_single_file_or_when_option_tsBui_5074": "只有在使用 tsconfig、發出至單一檔案，或指定 '--tsBuildInfoFile' 選項時，才可指定 '--incremental'選項。", "Option_isolatedModules_can_only_be_used_when_either_option_module_is_provided_or_option_target_is_ES_5047": "只有在提供選項 '--module' 或是 'target' 為 'ES2015' 或更高項目時，才可使用選項 'isolatedModules'。", "Option_moduleResolution_must_be_set_to_0_or_left_unspecified_when_option_module_is_set_to_1_5109": "當選項 'module' 設定為 '{1}' 時，選項 'moduleResolution' 必須設定為 '{0}' (或保留為未指定)。", "Option_module_must_be_set_to_0_when_option_moduleResolution_is_set_to_1_5110": "當選項 'moduleResolution' 設定為 '{1}' 時，選項 'module' 必須設定為 '{0}'。", "Option_preserveConstEnums_cannot_be_disabled_when_0_is_enabled_5091": "啟用 '{0}' 時，無法停用選項 'preserveConstEnums'。", "Option_project_cannot_be_mixed_with_source_files_on_a_command_line_5042": "在命令列上，'project' 選項不得與原始程式檔並用。", "Option_resolveJsonModule_cannot_be_specified_when_moduleResolution_is_set_to_classic_5070": "當 'moduleResolution' 設定為 'classic' 時，不得指定 '--resolveJsonModule' 選項。", "Option_resolveJsonModule_cannot_be_specified_when_module_is_set_to_none_system_or_umd_5071": "當 'module' 設定為 'none'、'system' 或 'umd' 時，不得指定 '--resolveJsonModule' 選項。", "Option_verbatimModuleSyntax_cannot_be_used_when_module_is_set_to_UMD_AMD_or_System_5105": "當 'module' 設定為 'UMD'、'AMD' 或 'System' 時，無法使用選項 'verbatimModuleSyntax'。", "Options_0_and_1_cannot_be_combined_6370": "無法合併選項 '{0}' 與 '{1}'。", "Options_Colon_6027": "選項:", "Output_Formatting_6256": "輸出格式", "Output_compiler_performance_information_after_building_6615": "在組建後輸出編譯器效能資訊。", "Output_directory_for_generated_declaration_files_6166": "所產生之宣告檔案的輸出目錄。", "Output_file_0_has_not_been_built_from_source_file_1_6305": "輸出檔 '{0}' 並非從原始程式檔 '{1}' 建置。", "Output_from_referenced_project_0_included_because_1_specified_1411": "因為指定了 '{1}'，所以包含參考的專案 '{0}' 輸出", "Output_from_referenced_project_0_included_because_module_is_specified_as_none_1412": "因為 '--module' 指定為 'none'，所以包含參考的專案 '{0}' 輸出", "Output_more_detailed_compiler_performance_information_after_building_6632": "在組建後輸出更詳細的編譯器效能資訊。", "Overload_0_of_1_2_gave_the_following_error_2772": "多載 {0} (共 {1})，'{2}'，發生下列錯誤。", "Overload_signatures_must_all_be_abstract_or_non_abstract_2512": "多載簽章必須全為抽象或非抽象。", "Overload_signatures_must_all_be_ambient_or_non_ambient_2384": "多載簽章都必須是環境或非環境簽章。", "Overload_signatures_must_all_be_exported_or_non_exported_2383": "多載簽章必須全部匯出或不匯出。", "Overload_signatures_must_all_be_optional_or_required_2386": "多載簽章都必須是選擇性或必要簽章。", "Overload_signatures_must_all_be_public_private_or_protected_2385": "多載簽章必須是公用、私用或受保護。", "Parameter_0_cannot_reference_identifier_1_declared_after_it_2373": "參數 '{0}' 不得參考在其之後宣告的識別碼 '{1}'。", "Parameter_0_cannot_reference_itself_2372": "參數 '{0}' 不得參考自身。", "Parameter_0_implicitly_has_an_1_type_7006": "參數 '{0}' 隱含了 '{1}' 類型。", "Parameter_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7044": "參數 '{0}' 隱含 '{1}' 類型，但可從使用方式推斷更適合的類型。", "Parameter_0_is_not_in_the_same_position_as_parameter_1_1227": "參數 '{0}' 與參數 '{1}' 不在同一個位置。", "Parameter_0_of_accessor_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4108": "存取子的參數 '{0}' 具有 (或正在使用) 來自外部模組 '{2}' 的名稱 '{1}'，但無法予以命名。", "Parameter_0_of_accessor_has_or_is_using_name_1_from_private_module_2_4107": "存取子的參數 '{0}' 具有 (或正在使用) 來自私人模組 '{2}' 的名稱 '{1}'。", "Parameter_0_of_accessor_has_or_is_using_private_name_1_4106": "存取子的參數 '{0}' 具有 (或正在使用) 私人名稱 '{1}'。", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4066": "匯出介面之呼叫簽章的參數 '{0}' 具有或使用私用模組 '{2}' 中的名稱 '{1}'。", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4067": "匯出介面之呼叫簽章的參數 '{0}' 具有或使用私用名稱 '{1}'。", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_can_4061": "匯出類別中建構函式的參數 '{0}' 具有或使用外部模組 {2} 中的名稱 '{1}'，但無法命名。", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_private_module_2_4062": "匯出類別中建構函式的參數 '{0}' 具有或使用私用模組 '{2}' 中的名稱 '{1}'。", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_private_name_1_4063": "匯出類別中建構函式的參數 '{0}' 具有或使用私用名稱 '{1}'。", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_name_1_from_private_mod_4064": "匯出介面中建構函式簽章的參數 '{0}' 具有或使用私用模組 '{2}' 中的名稱 '{1}'。", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4065": "匯出介面中建構函式簽章的參數 '{0}' 具有或使用私用名稱 '{1}'。", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4076": "匯出函式的參數 '{0}' 具有或使用外部模組 {2} 中的名稱 '{1}'，但無法命名。", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_private_module_2_4077": "匯出函式的參數 '{0}' 具有或使用私用模組 '{2}' 中的名稱 '{1}'。", "Parameter_0_of_exported_function_has_or_is_using_private_name_1_4078": "匯出函式的參數 '{0}' 具有或使用私用名稱 '{1}'。", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4091": "匯出介面的索引簽章參數 '{0}' 具有或使用私用模組 '{2}' 中的名稱 '{1}'。", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_private_name_1_4092": "匯出介面的索引簽章參數 '{0}' 具有或使用私用名稱 '{1}'。", "Parameter_0_of_method_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4074": "匯出介面中方法的參數 '{0}' 具有或使用私用模組 '{2}' 中的名稱 '{1}'。", "Parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4075": "匯出介面中方法的參數 '{0}' 具有或使用私用名稱 '{1}'。", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_c_4071": "匯出類別中公用方法的參數 '{0}' 具有或使用外部模組 {2} 中的名稱 '{1}'，但無法命名。", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4072": "匯出類別中公用方法的參數 '{0}' 具有或使用私用模組 '{2}' 中的名稱 '{1}'。", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4073": "匯出類別中公用方法的參數 '{0}' 具有或使用私用名稱 '{1}'。", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_external_module__4068": "匯出類別中公用靜態方法的參數 '{0}' 具有或使用外部模組 {2} 中的名稱 '{1}'，但無法命名。", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4069": "匯出類別中公用靜態方法的參數 '{0}' 具有或使用私用模組 '{2}' 中的名稱 '{1}'。", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4070": "匯出類別中公用靜態方法的參數 '{0}' 具有或使用私用名稱 '{1}'。", "Parameter_cannot_have_question_mark_and_initializer_1015": "參數不得有問號及初始設定式。", "Parameter_declaration_expected_1138": "必須是參數宣告。", "Parameter_has_a_name_but_no_type_Did_you_mean_0_Colon_1_7051": "參數具有名稱但沒有類型。您是指 '{0}: {1}' 嗎?", "Parameter_modifiers_can_only_be_used_in_TypeScript_files_8012": "參數修飾元只可用於 TypeScript 檔案中。", "Parameter_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9011": "參數必須有具備 --isolatedDeclarations 的明確類型註釋。", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4036": "匯出類別中公用 setter '{0}' 的參數類型具有或是正在使用私用模組 '{2}' 中的名稱 '{1}'。", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_private_name_1_4037": "匯出類別中公用 setter '{0}' 的參數類型具有或正在使用私用名稱 '{1}'。", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_name_1_from_private_mod_4034": "匯出類別中公用靜態 setter '{0}' 的參數類型具有或正在使用私用模組 '{2}' 中的名稱 '{1}'。", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_private_name_1_4035": "匯出類別中公用靜態 setter '{0}' 的參數類型具有或正在使用私用名稱 '{1}'。", "Parse_in_strict_mode_and_emit_use_strict_for_each_source_file_6141": "在 strict 模式中進行剖析，並為每個來源檔案發出 \"use strict\"。", "Part_of_files_list_in_tsconfig_json_1409": "tsconfig.json 中的部分 'files' 清單", "Pattern_0_can_have_at_most_one_Asterisk_character_5061": "模式 '{0}' 最多只可有一個 '*' 字元。", "Performance_timings_for_diagnostics_or_extendedDiagnostics_are_not_available_in_this_session_A_nativ_6386": "在此工作階段中無法使用 '--diagnostics ' 或 '--extendedDiagnostics ' 的效能計時。找不到 Web 效能 API 的原生實作。", "Platform_specific_6912": "平台特定", "Prefix_0_with_an_underscore_90025": "具有底線的前置詞 '{0}'", "Prefix_all_incorrect_property_declarations_with_declare_95095": "在所有不正確屬性宣告的開頭放置 'declare'", "Prefix_all_unused_declarations_with_where_possible_95025": "若可行，為所有未使用的宣告加上前置詞 '_'", "Prefix_with_declare_95094": "以 'declare' 開頭", "Preserve_unused_imported_values_in_the_JavaScript_output_that_would_otherwise_be_removed_1449": "保留 JavaScript 輸出中未使用的匯入值，否則將予以移除。", "Print_all_of_the_files_read_during_the_compilation_6653": "列印在編譯期間讀取的所有檔案。", "Print_files_read_during_the_compilation_including_why_it_was_included_6631": "列印在編譯期間讀取的檔案，包括其包含的原因。", "Print_names_of_files_and_the_reason_they_are_part_of_the_compilation_6505": "列印檔案名稱，以及檔案屬於編譯的原因。", "Print_names_of_files_part_of_the_compilation_6155": "列印編譯時檔案部分的名稱。", "Print_names_of_files_that_are_part_of_the_compilation_and_then_stop_processing_6503": "列印屬於編譯一部分的檔案名稱，然後停止處理。", "Print_names_of_generated_files_part_of_the_compilation_6154": "列印編譯時所產生之檔案部分的名稱。", "Print_the_compiler_s_version_6019": "列印編譯器的版本。", "Print_the_final_configuration_instead_of_building_1350": "列印而非建置最終組態。", "Print_the_names_of_emitted_files_after_a_compilation_6652": "在編譯後列印已發出檔案的名稱。", "Print_this_message_6017": "列印這則訊息。", "Private_accessor_was_defined_without_a_getter_2806": "私人存取子已在不使用 getter 的情況下定義。", "Private_field_0_must_be_declared_in_an_enclosing_class_1111": "私人欄位 '{0}' 必須在封閉類別中宣告。", "Private_identifiers_are_not_allowed_in_variable_declarations_18029": "變數宣告中不允許私人識別碼。", "Private_identifiers_are_not_allowed_outside_class_bodies_18016": "不允許私人識別碼位於類別主體外。", "Private_identifiers_are_only_allowed_in_class_bodies_and_may_only_be_used_as_part_of_a_class_member__1451": "私人識別碼只能在類別主體中使用，且只能做為類別成員宣告、屬性存取或 'in' 運算式左側的一部分使用", "Private_identifiers_are_only_available_when_targeting_ECMAScript_2015_and_higher_18028": "只有當目標為 ECMAScript 2015 及更新版本時，才可使用私人識別碼。", "Private_identifiers_cannot_be_used_as_parameters_18009": "私人識別碼不可用作為參數。", "Private_or_protected_member_0_cannot_be_accessed_on_a_type_parameter_4105": "無法在型別參數上存取私人或受保護的成員 '{0}'。", "Project_0_can_t_be_built_because_its_dependency_1_has_errors_6363": "Project '{0}' can't be built because its dependency '{1}' has errors", "Project_0_can_t_be_built_because_its_dependency_1_was_not_built_6383": "Project '{0}' can't be built because its dependency '{1}' was not built", "Project_0_is_being_forcibly_rebuilt_6388": "正在強制重建專案 '{0}'", "Project_0_is_out_of_date_because_1_6420": "由於 {1}，專案 '{0}' 已過期。", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_file_2_was_root_file_of_compilation_6412": "專案 '{0}' 已過期，因為 buildinfo 檔案 '{1}' 表示檔案 '{2}' 曾經是編譯的根檔案，但已不再是。", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_program_needs_to_report_errors_6419": "專案 '{0}' 已過期，因為 buildinfo 檔案 '{1}' 指出程式需要報告錯誤。", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_some_of_the_changes_were_not_emitte_6399": "因為 buildinfo 檔案 '{1}' 指出某些變更並未發出，所以專案 '{0}' 已過期", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_there_is_change_in_compilerOptions_6406": "專案 '{0}' 已過期，因為 buildinfo 檔案 '{1}' 表示 compilerOptions 中有變更", "Project_0_is_out_of_date_because_its_dependency_1_is_out_of_date_6353": "因為專案 '{0}' 的相依性 '{1}' 已過期，所以該專案已過期", "Project_0_is_out_of_date_because_output_1_is_older_than_input_2_6350": "因為輸出 '{1}' 早於輸入 '{2}'，所以專案 '{0}' 已過期", "Project_0_is_out_of_date_because_output_file_1_does_not_exist_6352": "因為輸出檔案 '{1}' 不存在，所以專案 '{0}' 已過期", "Project_0_is_out_of_date_because_output_for_it_was_generated_with_version_1_that_differs_with_curren_6381": "因為專案 '{0}' 的輸出使用版本 '{1}' 產生而成，與目前的版本 '{2}' 不同，所以該專案已過期", "Project_0_is_out_of_date_because_there_was_error_reading_file_1_6401": "專案 '{0}' 已過期，因為讀取檔案 '{1}' 時發生錯誤", "Project_0_is_up_to_date_6361": "專案 '{0}' 為最新狀態", "Project_0_is_up_to_date_because_newest_input_1_is_older_than_output_2_6351": "因為最新的輸入 '{1}' 早於最舊的輸出 '{2}'，所以專案 '{0}' 為最新狀態", "Project_0_is_up_to_date_but_needs_to_update_timestamps_of_output_files_that_are_older_than_input_fil_6400": "專案 '{0}' 為最新狀態，但需要更新比輸入檔案還舊的輸出檔案時間戳記", "Project_0_is_up_to_date_with_d_ts_files_from_its_dependencies_6354": "專案 '{0}' 為最新狀態，且有來自其相依性的 .d.ts 檔案", "Project_references_may_not_form_a_circular_graph_Cycle_detected_Colon_0_6202": "專案參考不會形成循環圖。但偵測到循環: {0}", "Projects_6255": "專案", "Projects_in_this_build_Colon_0_6355": "此組建中的專案: {0}", "Properties_with_the_accessor_modifier_are_only_available_when_targeting_ECMAScript_2015_and_higher_18045": "只有以 ECMAScript 2015 及更新版本為目標時，才可使用具有 'accessor' 修飾詞的屬性。", "Property_0_cannot_have_an_initializer_because_it_is_marked_abstract_1267": "屬性 '{0}' 已標記為抽象，因此不得有初始設定式。", "Property_0_comes_from_an_index_signature_so_it_must_be_accessed_with_0_4111": "屬性 '{0}' 來自索引簽章，因此必須使用 ['{0}'] 存取。", "Property_0_does_not_exist_on_type_1_2339": "類型 '{1}' 沒有屬性 '{0}'。", "Property_0_does_not_exist_on_type_1_Did_you_mean_2_2551": "類型 '{1}' 沒有屬性 '{0}'。您指的是 '{2}' 嗎?", "Property_0_does_not_exist_on_type_1_Did_you_mean_to_access_the_static_member_2_instead_2576": "屬性 '{0}' 不存在於類型 '{1}' 上。您要存取的是靜態成員 '{2}' 嗎?", "Property_0_does_not_exist_on_type_1_Do_you_need_to_change_your_target_library_Try_changing_the_lib_c_2550": "類型 '{1}' 沒有屬性 '{0}'。要變更您的目標程式庫嗎? 請嘗試將 'lib' 編譯器選項變更為 '{2}' 或更新版本。", "Property_0_does_not_exist_on_type_1_Try_changing_the_lib_compiler_option_to_include_dom_2812": "屬性 '{0}' 不存在於類型 '{1}' 上。嘗試將 'lib' 編譯器選項變更為包含 'dom'。", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_a_class_static_block_2817": "屬性 '{0}' 沒有初始設定式，且未在類別靜態區塊中明確指派。", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_the_constructor_2564": "屬性 '{0}' 沒有初始設定式，且未在建構函式中明確指派。", "Property_0_implicitly_has_type_any_because_its_get_accessor_lacks_a_return_type_annotation_7033": "因為屬性 '{0}' 的 get 存取子沒有傳回類型註釋，致使該屬性意味著類型 'any'。", "Property_0_implicitly_has_type_any_because_its_set_accessor_lacks_a_parameter_type_annotation_7032": "因為屬性 '{0}' 的 set 存取子沒有參數類型註釋，致使該屬性意味著類型 'any'。", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_get_accessor_may_be_inferred_from_usage_7048": "屬性 '{0}' 隱含類型 'any'，但可從使用方式推斷更適合其 get 存取子的類型。", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_set_accessor_may_be_inferred_from_usage_7049": "屬性 '{0}' 隱含類型 'any'，但可從使用方式推斷更適合其 set 存取子的類型。", "Property_0_in_type_1_is_not_assignable_to_the_same_property_in_base_type_2_2416": "類型 '{1}' 中的屬性 '{0}' 無法指派給基底類型 '{2}' 中的相同屬性。", "Property_0_in_type_1_is_not_assignable_to_type_2_2603": "不得將類型 '{1}' 的屬性 '{0}' 指派給類型 '{2}'。", "Property_0_in_type_1_refers_to_a_different_member_that_cannot_be_accessed_from_within_type_2_18015": "類型 '{1}' 中的屬性 '{0}' 是無法從類型 '{2}' 中存取的其他成員。", "Property_0_is_declared_but_its_value_is_never_read_6138": "屬性 '{0}' 已宣告但從未讀取其值。", "Property_0_is_incompatible_with_index_signature_2530": "屬性 '{0}' 和索引簽章不相容。", "Property_0_is_missing_in_type_1_2324": "類型 '{1}' 遺漏屬性 '{0}'。", "Property_0_is_missing_in_type_1_but_required_in_type_2_2741": "類型 '{1}' 缺少屬性 '{0}'，但類型 '{2}' 必須有該屬性。", "Property_0_is_not_accessible_outside_class_1_because_it_has_a_private_identifier_18013": "因為屬性 '{0}' 具有私人識別碼，所以無法在類別 '{1}' 外存取該屬性。", "Property_0_is_optional_in_type_1_but_required_in_type_2_2327": "在類型 '{1}' 中，'{0}' 是選擇性屬性，但在類型 '{2}' 中則為必要屬性。", "Property_0_is_private_and_only_accessible_within_class_1_2341": "'{0}' 是私用屬性，只可從類別 '{1}' 中存取。", "Property_0_is_private_in_type_1_but_not_in_type_2_2325": "在類型 '{1}' 中，'{0}' 是私用屬性，但在類型 '{2}' 中不是。", "Property_0_is_protected_and_only_accessible_through_an_instance_of_class_1_This_is_an_instance_of_cl_2446": "屬性 '{0}' 已受到保護，只能透過類別 '{1}' 的執行個體加以存取。這是類別 '{2}' 的執行個體。", "Property_0_is_protected_and_only_accessible_within_class_1_and_its_subclasses_2445": "'{0}' 是受保護屬性，只可從類別 '{1}' 及其子類別中存取。", "Property_0_is_protected_but_type_1_is_not_a_class_derived_from_2_2443": "'{0}' 是受保護屬性，但類型 '{1}' 不是衍生自 '{2}' 的類別。", "Property_0_is_protected_in_type_1_but_public_in_type_2_2444": "在類型 '{1}' 中，'{0}' 是受保護屬性，但在類型 '{2}' 中是公用屬性。", "Property_0_is_used_before_being_assigned_2565": "屬性 '{0}' 已在指派之前使用。", "Property_0_is_used_before_its_initialization_2729": "將屬性 '{0}' 初始化前已使用該屬性。", "Property_0_may_not_exist_on_type_1_Did_you_mean_2_2568": "類型 '{1}' 可能不存在屬性 '{0}'。您指的是 '{2}' 嗎?", "Property_0_of_JSX_spread_attribute_is_not_assignable_to_target_property_2606": "JSX 擴張屬性 (Attribute) 的屬性 (Property) '{0}' 不可指派給目標屬性 (Property)。", "Property_0_of_exported_anonymous_class_type_may_not_be_private_or_protected_4094": "匯出之匿名類別類型的屬性 '{0}' 不可為私人或受保護的。", "Property_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4032": "匯出介面的屬性 '{0}' 具有或使用私用模組 '{2}' 中的名稱 '{1}'。", "Property_0_of_exported_interface_has_or_is_using_private_name_1_4033": "匯出介面的屬性 '{0}' 具有或使用私用名稱 '{1}'。", "Property_0_of_type_1_is_not_assignable_to_2_index_type_3_2411": "類型 '{1}' 的屬性 '{0}' 不可指派給 '{2}' 索引類型 '{3}'。", "Property_0_was_also_declared_here_2733": "屬性 '{0}' 也已定義於此處。", "Property_0_will_overwrite_the_base_property_in_1_If_this_is_intentional_add_an_initializer_Otherwise_2612": "屬性 '{0}' 將會覆寫 '{1}' 中的基底屬性。如果是故意覆寫的，請新增初始設定式。否則，請新增 'declare' 修飾元或移除多餘的宣告。", "Property_assignment_expected_1136": "必須是屬性指派。", "Property_destructuring_pattern_expected_1180": "必須是屬性解構模式。", "Property_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9012": "屬性必須有具備 --isolatedDeclarations 的明確類型註釋。", "Property_or_signature_expected_1131": "必須是屬性或簽章。", "Property_value_can_only_be_string_literal_numeric_literal_true_false_null_object_literal_or_array_li_1328": "屬性值僅能為字串常值、數值常值、'true'、'false'、'null'、物件常值或陣列常值。", "Provide_full_support_for_iterables_in_for_of_spread_and_destructuring_when_targeting_ES5_6179": "當目標為 'ES5' 時，為 'for-of'、擴張及解構中的可疊代物件提供完整的支援。", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4098": "匯出類別的公用方法 '{0}' 具有或使用外部模組 {2} 的名稱 '{1}'，但無法命名。", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4099": "匯出類別的公用方法 '{0}' 具有或使用私用模組 '{2}' 的名稱 '{1}'。", "Public_method_0_of_exported_class_has_or_is_using_private_name_1_4100": "匯出類別的公用方法 '{0}' 具有或使用私用名稱 '{1}'。", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_name_4029": "匯出類別的公用屬性 '{0}' 具有或使用外部模組 {2} 中的名稱 '{1}'，但無法命名。", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4030": "匯出類別的公用屬性 '{0}' 具有或使用私用模組 '{2}' 中的名稱 '{1}'。", "Public_property_0_of_exported_class_has_or_is_using_private_name_1_4031": "匯出類別的公用屬性 '{0}' 具有或使用私用名稱 '{1}'。", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_4095": "匯出類別的公用靜態方法 '{0}' 具有或使用外部模組 {2} 的名稱 '{1}'，但無法命名。", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4096": "匯出類別的公用靜態方法 '{0}' 具有或使用私用模組 '{2}' 的名稱 '{1}'。", "Public_static_method_0_of_exported_class_has_or_is_using_private_name_1_4097": "匯出類別的公用靜態方法 '{0}' 具有或使用私用名稱 '{1}'。", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot__4026": "匯出類別的公用靜態屬性 '{0}' 具有或使用外部模組 {2} 中的名稱 '{1}'，但無法命名。", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4027": "匯出類別的公用靜態屬性 '{0}' 具有或使用私用模組 '{2}' 中的名稱 '{1}'。", "Public_static_property_0_of_exported_class_has_or_is_using_private_name_1_4028": "匯出類別的公用靜態屬性 '{0}' 具有或使用私用名稱 '{1}'。", "Qualified_name_0_is_not_allowed_without_a_leading_param_object_1_8032": "限定名稱 '{0}' 必須以 '@param {object} {1}' 開頭。", "Raise_an_error_when_a_function_parameter_isn_t_read_6676": "當函式參數未讀取時引發錯誤。", "Raise_error_on_expressions_and_declarations_with_an_implied_any_type_6052": "當運算式及宣告包含隱含的 'any' 類型時顯示錯誤。", "Raise_error_on_this_expressions_with_an_implied_any_type_6115": "對具有隱含 'any' 類型的 'this' 運算式引發錯誤。", "Range_out_of_order_in_character_class_1517": "字元類別中的範圍失序。", "Re_exporting_a_type_when_0_is_enabled_requires_using_export_type_1205": "啟用 '{0}' 時重新匯出類型需要使用 'export type'。", "React_components_cannot_include_JSX_namespace_names_2639": "React 元件不得包含 JSX 命名空間名稱", "Redirect_output_structure_to_the_directory_6006": "將輸出結構重新導向至目錄。", "Reduce_the_number_of_projects_loaded_automatically_by_TypeScript_6617": "減少 TypeScript 自動載入的專案數目。", "Referenced_project_0_may_not_disable_emit_6310": "參考的專案 '{0}' 不得停用發出。", "Referenced_project_0_must_have_setting_composite_Colon_true_6306": "參考的專案 '{0}' 之設定 \"composite\" 必須為 true。", "Referenced_via_0_from_file_1_1400": "透過 '{0}' 從檔案 '{1}' 參考", "Relative_import_paths_need_explicit_file_extensions_in_ECMAScript_imports_when_moduleResolution_is_n_2834": "當 '--moduleResolution' 為 'node16' 或 'nodenext' 時，相對匯入路徑在 ECMAScript 匯入中需要明確的副檔名。建議為匯入路徑新增副檔名。", "Relative_import_paths_need_explicit_file_extensions_in_ECMAScript_imports_when_moduleResolution_is_n_2835": "當 '--moduleResolution' 為 'node16' 或 'nodenext' 時，相對匯入路徑在 ECMAScript 匯入中需要明確的副檔名。您是指 '{0}' 嗎?", "Remove_a_list_of_directories_from_the_watch_process_6628": "從監看處理序移除目錄清單。", "Remove_a_list_of_files_from_the_watch_mode_s_processing_6629": "從監視模式的處理移除檔案清單。", "Remove_all_unnecessary_override_modifiers_95163": "移除所有不必要的 'override' 修飾元", "Remove_all_unnecessary_uses_of_await_95087": "移除所有不必要的 'await' 使用方式", "Remove_all_unreachable_code_95051": "移除所有無法連線的程式碼", "Remove_all_unused_labels_95054": "移除所有未使用的標籤", "Remove_braces_from_all_arrow_function_bodies_with_relevant_issues_95115": "從具有相關問題的所有箭號函式主體中移除大括號", "Remove_braces_from_arrow_function_95060": "從箭號函式移除大括號", "Remove_braces_from_arrow_function_body_95112": "從箭號函式主體中移除大括號", "Remove_import_from_0_90005": "從 '{0}' 移除匯入", "Remove_override_modifier_95161": "移除 'override' 修飾元", "Remove_parentheses_95126": "移除括弧", "Remove_template_tag_90011": "移除範本標籤", "Remove_the_20mb_cap_on_total_source_code_size_for_JavaScript_files_in_the_TypeScript_language_server_6618": "移除 TypeScript 語言伺服器中 JavaScript 檔案的總原始程式碼大小 20mb 上限。", "Remove_type_from_import_declaration_from_0_90055": "從 \"{0}\" 移除匯入宣告中的 'type'", "Remove_type_from_import_of_0_from_1_90056": "從 \"{1}\" 移除匯入 '{0}' 中的 'type'", "Remove_type_parameters_90012": "移除型別參數", "Remove_unnecessary_await_95086": "移除不必要的 'await'", "Remove_unreachable_code_95050": "移除無法連線的程式碼", "Remove_unused_declaration_for_Colon_0_90004": "移除下列項目未使用的宣告: '{0}'", "Remove_unused_declarations_for_Colon_0_90041": "移除 '{0}' 未使用的宣告", "Remove_unused_destructuring_declaration_90039": "移除未使用的解構宣告", "Remove_unused_label_95053": "移除未使用的標籤", "Remove_variable_statement_90010": "移除變數陳述式", "Rename_param_tag_name_0_to_1_95173": "將 '@param' 標籤名稱 '{0}' 重新命名為'{1}'", "Replace_0_with_Promise_1_90036": "將 '{0}' 取代為 'Promise<{1}>'", "Replace_all_unused_infer_with_unknown_90031": "以 'unknown' 取代所有未使用的 'infer'", "Replace_import_with_0_95015": "以 '{0}' 取代匯入。", "Replace_infer_0_with_unknown_90030": "以 'unknown' 取代 'infer {0}'", "Report_error_when_not_all_code_paths_in_function_return_a_value_6075": "當函式中的部分程式碼路徑並未傳回值時回報錯誤。", "Report_errors_for_fallthrough_cases_in_switch_statement_6076": "回報 switch 陳述式內 fallthrough 案例的錯誤。", "Report_errors_in_js_files_8019": "報告 .js 檔案中的錯誤。", "Report_errors_on_unused_locals_6134": "回報未使用之區域變數的錯誤。", "Report_errors_on_unused_parameters_6135": "回報未使用之參數的錯誤。", "Require_sufficient_annotation_on_exports_so_other_tools_can_trivially_generate_declaration_files_6719": "匯出時需要足夠的註釋，讓其他工具可以簡單地產生宣告檔案。", "Require_undeclared_properties_from_index_signatures_to_use_element_accesses_6717": "需要來自索引簽章的未宣告屬性，才能使用元素存取。", "Required_type_parameters_may_not_follow_optional_type_parameters_2706": "必要型別參數可能未遵循選擇性型別參數。", "Resolution_for_module_0_was_found_in_cache_from_location_1_6147": "從位置 '{1}' 的快取中找到模組 '{0}' 的解析。", "Resolution_for_type_reference_directive_0_was_found_in_cache_from_location_1_6241": "從位置 '{0}' 的快取記憶體找到類型參照指示詞 '{1}'。", "Resolution_of_non_relative_name_failed_trying_with_modern_Node_resolution_features_disabled_to_see_i_6277": "解析非相對名稱失敗; 嘗試停用新式節點解析功能，以查看 npm 程式庫是否需要更新設定。", "Resolution_of_non_relative_name_failed_trying_with_moduleResolution_bundler_to_see_if_project_may_ne_6279": "解析非相對名稱失敗; 嘗試使用 '--moduleResolution bundler' 查看專案是否可能需要更新設定。", "Resolve_keyof_to_string_valued_property_names_only_no_numbers_or_symbols_6195": "只將 'keyof' 解析為字串值的屬性名稱 (無任何數字或符號)。", "Resolved_under_condition_0_6414": "已在條件 '{0}' 下解析。", "Resolving_in_0_mode_with_conditions_1_6402": "正在以條件 {1} 在 {0} 模式中解析。", "Resolving_module_0_from_1_6086": "======== 正在從 '{1}' 解析模組 '{0}'。========", "Resolving_module_name_0_relative_to_base_url_1_2_6094": "正在解析與基底 URL '{1}' 相對的模組名稱 '{0}' - '{2}'。", "Resolving_real_path_for_0_result_1_6130": "正在解析 '{0}' 的真實路徑，結果 '{1}'。", "Resolving_type_reference_directive_0_containing_file_1_6242": "======== 正在解析類型參考指示詞 '{0}'，包含檔案 '{1}'。========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_2_6116": "======== 正在解析類型參考指示詞 '{0}'，包含檔案 '{1}'，根目錄 '{2}'。========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_not_set_6123": "======== 正在解析類型參考指示詞 '{0}'，包含檔案 '{1}'，未設定根目錄。========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_1_6127": "======== 正在解析類型參考指示詞 '{0}'，未設定包含檔案，根目錄 '{1}'。========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_not_set_6128": "======== 正在解析類型參考指示詞 '{0}'，未設定包含檔案，未設定根目錄。 ========", "Resolving_type_reference_directive_for_program_that_specifies_custom_typeRoots_skipping_lookup_in_no_6265": "正在解析指定自訂 typeRoots 程式的類型參考指示詞，並跳過 'node_modules' 資料夾中的查詢。", "Resolving_with_primary_search_path_0_6121": "正在解析主要搜尋路徑 '{0}'。", "Rest_parameter_0_implicitly_has_an_any_type_7019": "剩餘參數 '{0}' 隱含了 'any[]' 類型。", "Rest_parameter_0_implicitly_has_an_any_type_but_a_better_type_may_be_inferred_from_usage_7047": "其餘參數 '{0}' 隱含 'any[]' 類型，但可從使用方式推斷更適合的類型。", "Rest_types_may_only_be_created_from_object_types_2700": "Rest 類型只能從物件類型建立。", "Return_type_annotation_circularly_references_itself_2577": "傳回型別註解會循環參考自身。", "Return_type_must_be_inferred_from_a_function_95149": "必須從函式推斷傳回型別", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4046": "匯出介面中呼叫簽章的傳回型別具有或使用私用模組 '{1}' 中的名稱 '{0}'。", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_private_name_0_4047": "匯出介面中呼叫簽章的傳回型別具有或使用私用名稱 '{0}'。", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_name_0_from_private_mod_4044": "匯出介面中建構函式簽章的傳回型別具有或使用私用模組 '{1}' 中的名稱 '{0}'。", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_0_4045": "匯出介面中建構函式簽章的傳回型別具有或使用私用名稱 '{0}'。", "Return_type_of_constructor_signature_must_be_assignable_to_the_instance_type_of_the_class_2409": "建構函式簽章的傳回類型必須能夠指派給類別的執行個體類型。", "Return_type_of_exported_function_has_or_is_using_name_0_from_external_module_1_but_cannot_be_named_4058": "匯出函式的傳回型別具有或使用外部模組 {1} 中的名稱 '{0}'，但無法命名。", "Return_type_of_exported_function_has_or_is_using_name_0_from_private_module_1_4059": "匯出函式的傳回型別具有或使用私用模組 '{1}' 中的名稱 '{0}'。", "Return_type_of_exported_function_has_or_is_using_private_name_0_4060": "匯出函式的傳回型別具有或使用私用名稱 '{0}'。", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4048": "匯出介面中索引簽章的傳回型別具有或使用私用模組 '{1}' 中的名稱 '{0}'。", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_private_name_0_4049": "匯出介面中索引簽章的傳回型別具有或使用私用名稱 '{0}'。", "Return_type_of_method_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4056": "匯出介面中方法的傳回型別具有或使用私用模組 '{1}' 中的名稱 '{0}'。", "Return_type_of_method_from_exported_interface_has_or_is_using_private_name_0_4057": "匯出介面中方法的傳回型別具有或使用私用名稱 '{0}'。", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_4041": "匯出類別中公用 getter '{0}' 的傳回型別具有或正在使用外部模組 {2} 中的名稱 '{1}'，但無法命名。", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4042": "匯出類別中公用 getter '{0}' 的傳回型別具有或正在使用私用模組 {2} 中的名稱 '{1}'。", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_private_name_1_4043": "匯出類別中公用 getter '{0}' 的傳回型別具有或正在使用私用名稱 '{1}'。", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_external_module_1_but_c_4053": "匯出類別中公用方法的傳回型別具有或使用外部模組 {1} 中的名稱 '{0}'，但無法命名。", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4054": "匯出類別中公用方法的傳回型別具有或使用私用模組 '{1}' 中的名稱 '{0}'。", "Return_type_of_public_method_from_exported_class_has_or_is_using_private_name_0_4055": "匯出類別中公用方法的傳回型別具有或使用私用名稱 '{0}'。", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_external_modul_4038": "匯出類別中公用靜態 getter '{0}' 的傳回型別具有或正在使用外部模組 {2} 中的名稱 '{1}'，但無法命名。", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_4039": "匯出類別中公用靜態 getter '{0}' 的傳回型別具有或正在使用私用模組 '{2}' 中的名稱 '{1}'。", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_private_name_1_4040": "匯出類別中公用靜態 getter '{0}' 的傳回型別具有或正在使用私用名稱 '{1}'。", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_external_module__4050": "匯出類別中公用靜態方法的傳回型別具有或使用外部模組 {1} 中的名稱 '{0}'，但無法命名。", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4051": "匯出類別中公用靜態方法的傳回型別具有或使用私用模組 '{1}' 中的名稱 '{0}'。", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_private_name_0_4052": "匯出類別中公用靜態方法的傳回型別具有或使用私用名稱 '{0}'。", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_not_resolved_6395": "在位置 '{2}' 的快取中找到的 '{1}' 中重複使用模組 '{0}' 的解析，它尚未加以解析。", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6393": "在位置 '{2}' 的快取中找到的 '{1}' 中重複使用模組 '{0}' 的解析，已成功將其解析為 '{3}'。", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6394": "在位置 '{2}' 的快取中找到的 '{1}' 中重複使用模組 '{0}' 的解析，已成功將其解析為套件識別碼為 '{4}' 的 '{3}'。", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_not_resolved_6389": "在舊程式的 '{1}' 中重複使用模組 '{0}' 的解析，它尚未加以解析。", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_6183": "在舊程式的 '{1}' 中重複使用模組 '{0}' 的解析，已成功將其解析為 '{2}'。", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_with_Package__6184": "在舊程式的 '{1}' 中重複使用模組 '{0}' 的解析，已成功將其解析為套件識別碼為 '{3}' 的 '{2}'。", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_not_re_6398": "在位置 '{2}' 的快取中找到的 '{1}' 中重複使用類型參考指示詞 '{0}' 的解析，它尚未加以解析。", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6396": "在位置 '{2}' 的快取中找到的 '{1}' 中重複使用類型參考指示詞 '{0}' 的解析，已成功將其解析為 '{3}'。", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6397": "在位置 '{2}' 的快取中找到的 '{1}' 中重複使用類型參考指示詞 '{0}' 的解析，已成功將其解析為套件識別碼為 '{4}' 的 '{3}'。", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_not_resolved_6392": "在舊程式的 '{1}' 中重複使用類型參考指示詞 '{0}' 的解析，它尚未加以解析。", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6390": "在舊程式的 '{1}' 中重複使用類型參考指示詞 '{0}' 的解析，已成功將其解析為 '{2}'。", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6391": "在舊程式的 '{1}' 中重複使用類型參考指示詞 '{0}' 的解析，已成功將其解析為套件識別碼為 '{3}' 的 '{2}'。", "Rewrite_all_as_indexed_access_types_95034": "將全部重寫為經過編製索引的存取類型", "Rewrite_as_the_indexed_access_type_0_90026": "重寫為索引存取類型 '{0}'", "Rewrite_ts_tsx_mts_and_cts_file_extensions_in_relative_import_paths_to_their_JavaScript_equivalent_i_6421": "將相對匯入路徑中的 '.ts'、'.tsx'、'.mts'、'.cts' 檔案副檔名重寫為輸出檔案中的 JavaScript 對應檔名。", "Right_operand_of_is_unreachable_because_the_left_operand_is_never_nullish_2869": "?? 的右運算元無法連線，因為左運算元永遠不會是 nullish。", "Root_directory_cannot_be_determined_skipping_primary_search_paths_6122": "無法判斷根目錄，將略過主要搜尋路徑。", "Root_file_specified_for_compilation_1427": "為編譯指定的根檔案", "STRATEGY_6039": "策略", "Save_tsbuildinfo_files_to_allow_for_incremental_compilation_of_projects_6642": "儲存 .tsbuildinfo 檔案，以允許對專案進行累加編譯。", "Saw_non_matching_condition_0_6405": "儲存不相符條件 '{0}'。", "Scoped_package_detected_looking_in_0_6182": "偵測到範圍套件，正於 '{0}' 尋找", "Searching_all_ancestor_node_modules_directories_for_fallback_extensions_Colon_0_6418": "正在搜尋所有上階 node_modules 目錄，以取得後援延伸模組: {0}。", "Searching_all_ancestor_node_modules_directories_for_preferred_extensions_Colon_0_6417": "正在搜尋所有上階 node_modules 目錄，以取得慣用延伸模組: {0}。", "Selection_is_not_a_valid_statement_or_statements_95155": "選取項目非有效的一或多個陳述式", "Selection_is_not_a_valid_type_node_95133": "選取範圍不是有效的類型節點", "Set_the_JavaScript_language_version_for_emitted_JavaScript_and_include_compatible_library_declaratio_6705": "為發出的 JavaScript 設定 JavaScript 語言版本，並包含相容的程式庫宣告。", "Set_the_language_of_the_messaging_from_TypeScript_This_does_not_affect_emit_6654": "設定來自 TypeScript 的訊息語言。這不會影響發出。", "Set_the_module_option_in_your_configuration_file_to_0_95099": "將組態檔中的 'module' 選項設定為 '{0}'", "Set_the_newline_character_for_emitting_files_6659": "設定發出檔案的新行字元。", "Set_the_target_option_in_your_configuration_file_to_0_95098": "將組態檔中的 'target' 選項設定為 '{0}'", "Setters_cannot_return_a_value_2408": "setter 無法傳回值。", "Show_all_compiler_options_6169": "顯示所有的編譯器選項。", "Show_diagnostic_information_6149": "顯示診斷資訊。", "Show_verbose_diagnostic_information_6150": "顯示詳細診斷資訊。", "Show_what_would_be_built_or_deleted_if_specified_with_clean_6367": "顯示將會建置 (或刪除 - 若是指定有 '--clean') 的內容", "Signature_0_must_be_a_type_predicate_1224": "簽章 '{0}' 必須是型別述詞。", "Signature_declarations_can_only_be_used_in_TypeScript_files_8017": "簽章宣告僅能在 TypeScript 檔案中使用。", "Skip_building_downstream_projects_on_error_in_upstream_project_6640": "上游專案發生錯誤時，略過建置下游專案。", "Skip_type_checking_all_d_ts_files_6693": "略過所有 .d.ts 檔案的型別檢查。", "Skip_type_checking_d_ts_files_that_are_included_with_TypeScript_6692": "略過 TypeScript 中包含 .d.ts 檔案的型別檢查。", "Skip_type_checking_of_declaration_files_6012": "跳過宣告檔案的類型檢查。", "Skipping_build_of_project_0_because_its_dependency_1_has_errors_6362": "Skipping build of project '{0}' because its dependency '{1}' has errors", "Skipping_build_of_project_0_because_its_dependency_1_was_not_built_6382": "Skipping build of project '{0}' because its dependency '{1}' was not built", "Skipping_module_0_that_looks_like_an_absolute_URI_target_file_types_Colon_1_6164": "正在跳過看起來像絕對 URI 的模組 '{0}'，目標檔案類型: {1}。", "Source_from_referenced_project_0_included_because_1_specified_1414": "因為指定了 '{1}'，所以包含參考的專案 '{0}' 來源", "Source_from_referenced_project_0_included_because_module_is_specified_as_none_1415": "因為 '--module' 指定為 'none'，所以包含參考的專案 '{0}' 來源", "Source_has_0_element_s_but_target_allows_only_1_2619": "來源具有 {0} 個元素，但目標只允許 {1} 個。", "Source_has_0_element_s_but_target_requires_1_2618": "來源有 {0} 個元素，但目標需要 {1} 個。", "Source_provides_no_match_for_required_element_at_position_0_in_target_2623": "來源未針對目標中位於 {0} 的必要元素提供相符項目。", "Source_provides_no_match_for_variadic_element_at_position_0_in_target_2624": "來源未針對目標中位於 {0} 的可變元素提供相符項目。", "Specify_ECMAScript_target_version_6015": "指定 ECMAScript 目標版本。", "Specify_JSX_code_generation_6080": "指定 JSX 程式碼產生。", "Specify_a_file_that_bundles_all_outputs_into_one_JavaScript_file_If_declaration_is_true_also_designa_6679": "指定將所有輸出組合成一個 JavaScript 檔案的檔案。如果 'declaration' 為 True，則也會指定組合所有 .d.ts 輸出的檔案。", "Specify_a_list_of_glob_patterns_that_match_files_to_be_included_in_compilation_6641": "指定符合編譯中要包含之檔案的 glob 模式清單。", "Specify_a_list_of_language_service_plugins_to_include_6681": "指定要包含的語言服務外掛程式清單。", "Specify_a_set_of_bundled_library_declaration_files_that_describe_the_target_runtime_environment_6651": "指定一組描述目標執行階段環境的配套程式庫宣告檔案。", "Specify_a_set_of_entries_that_re_map_imports_to_additional_lookup_locations_6680": "指定一組將匯入重新對應至其他查閱位置的項目。", "Specify_an_array_of_objects_that_specify_paths_for_projects_Used_in_project_references_6687": "指定為專案指定路徑的物件陣列。用於專案參考。", "Specify_an_output_folder_for_all_emitted_files_6678": "指定所有發出檔案的輸出檔案夾。", "Specify_emit_Slashchecking_behavior_for_imports_that_are_only_used_for_types_6718": "指定僅用於類型之匯入的發出/檢查行為。", "Specify_file_to_store_incremental_compilation_information_6380": "指定要儲存累加編譯資訊的檔案", "Specify_how_TypeScript_looks_up_a_file_from_a_given_module_specifier_6658": "指定 TypeScript 從指定的模組指定名稱查詢檔案的方式。", "Specify_how_directories_are_watched_on_systems_that_lack_recursive_file_watching_functionality_6714": "指定在缺少遞迴檔案監視功能之系統上如何監視目錄。", "Specify_how_the_TypeScript_watch_mode_works_6715": "指定 TypeScript 監看式模式的運作方式。", "Specify_library_files_to_be_included_in_the_compilation_6079": "請指定要併入編譯中的程式庫檔案。", "Specify_module_code_generation_6016": "指定模組程式碼產生。", "Specify_module_specifier_used_to_import_the_JSX_factory_functions_when_using_jsx_Colon_react_jsx_Ast_6649": "指定使用 'jsx: react-jsx*' 時，用來匯入 JSX Factory 函式的模組指定名稱。", "Specify_multiple_folders_that_act_like_Slashnode_modules_Slash_types_6710": "指定多個資料夾，其作用類似 './node_modules/@types'。", "Specify_one_or_more_path_or_node_module_references_to_base_configuration_files_from_which_settings_a_6633": "指定一或多個路徑或節點模組參考至繼承設定的基礎設定檔。", "Specify_options_for_automatic_acquisition_of_declaration_files_6709": "指定用於自動擷取宣告檔案的選項。", "Specify_strategy_for_creating_a_polling_watch_when_it_fails_to_create_using_file_system_events_Colon_6227": "指定當輪詢監看無法使用下列檔案系統事件建立時，加以建立的策略: 'FixedInterval' (預設)、'PriorityInterval'、'DynamicPriority'、'FixedChunkSize'。", "Specify_strategy_for_watching_directory_on_platforms_that_don_t_support_recursive_watching_natively__6226": "指定在未原生支援遞迴監看的平台上，監看目錄的策略: 'UseFsEvents' (預設)、'FixedPollingInterval'、'DynamicPriorityPolling'、'FixedChunkSizePolling'。", "Specify_strategy_for_watching_file_Colon_FixedPollingInterval_default_PriorityPollingInterval_Dynami_6225": "指定監看檔案的策略: 'FixedPollingInterval' (預設)、'PriorityPollingInterval'、'DynamicPriorityPolling'、'FixedChunkSizePolling'、'UseFsEvents'、'UseFsEventsOnParentDirectory'。", "Specify_the_JSX_Fragment_reference_used_for_fragments_when_targeting_React_JSX_emit_e_g_React_Fragme_6648": "指定要在以 React JSX 發出為目標時使用於片段的 JSX 片段參考，例如 'React.Fragment' 或 'Fragment'。", "Specify_the_JSX_factory_function_to_use_when_targeting_react_JSX_emit_e_g_React_createElement_or_h_6146": "請指定要在以 'react' JSX 發出為目標時使用的 JSX factory 函式。例如 'React.createElement' 或 'h'。", "Specify_the_JSX_factory_function_used_when_targeting_React_JSX_emit_e_g_React_createElement_or_h_6647": "請指定要在以 React JSX 發出為目標時使用的 JSX factory 函式。例如 'React.createElement' 或 'h'。", "Specify_the_JSX_fragment_factory_function_to_use_when_targeting_react_JSX_emit_with_jsxFactory_compi_18034": "當指定以 'jsxFactory' 編譯器選項設定 'react' JSX 輸出的目標時，請指定要使用的 JSX 片段處理站函式，例如 'Fragment'。", "Specify_the_base_directory_to_resolve_non_relative_module_names_6607": "指定基礎目錄來解析非相對的模組名稱。", "Specify_the_end_of_line_sequence_to_be_used_when_emitting_files_Colon_CRLF_dos_or_LF_unix_6060": "指定發出檔案時要用的行尾順序: 'CRLF' (DOS) 或 'LF' (UNIX)。", "Specify_the_location_where_debugger_should_locate_TypeScript_files_instead_of_source_locations_6004": "指定偵錯工具尋找 TypeScript 檔案的位置，而非原始檔位置。", "Specify_the_location_where_debugger_should_locate_map_files_instead_of_generated_locations_6655": "指定偵錯工具尋找對應檔的位置，而非產生的位置。", "Specify_the_maximum_folder_depth_used_for_checking_JavaScript_files_from_node_modules_Only_applicabl_6656": "指定用來檢查來自 'node_modules' 之 JavaScript 檔案的資料夾深度上限。僅適用於 'allowJs'。", "Specify_the_module_specifier_to_be_used_to_import_the_jsx_and_jsxs_factory_functions_from_eg_react_6238": "指定用於匯入 `jsx` 與 `jsxs` 工廠函式的模組指定名稱。例如，傳送表情符號", "Specify_the_object_invoked_for_createElement_This_only_applies_when_targeting_react_JSX_emit_6686": "指定 'createElement' 叫用的物件。這僅適用於在以 'react' JSX 發出為目標時。", "Specify_the_output_directory_for_generated_declaration_files_6613": "指定產生的宣告檔案的輸出目錄。", "Specify_the_path_to_tsbuildinfo_incremental_compilation_file_6707": "指定 .tsbuildinfo 累加編譯檔案的路徑。", "Specify_the_root_directory_of_input_files_Use_to_control_the_output_directory_structure_with_outDir_6058": "指定輸入檔的根目錄。用以控制具有 --outDir 的輸出目錄結構。", "Specify_the_root_folder_within_your_source_files_6690": "指定來源檔案內的根資料夾。", "Specify_the_root_path_for_debuggers_to_find_the_reference_source_code_6695": "指定偵錯工具尋找參考原始程式碼的根路徑。", "Specify_type_package_names_to_be_included_without_being_referenced_in_a_source_file_6711": "指定要包含的類型封裝名稱，而不在來源檔案中參考。", "Specify_what_JSX_code_is_generated_6646": "指定要產生的 JSX 程式碼。", "Specify_what_approach_the_watcher_should_use_if_the_system_runs_out_of_native_file_watchers_6634": "指定當系統用盡原生檔案監控程式時，監控程式應使用的方法。", "Specify_what_module_code_is_generated_6657": "指定要產生的模組程式碼。", "Split_all_invalid_type_only_imports_1367": "分割所有無效的僅限類型匯入", "Split_into_two_separate_import_declarations_1366": "分割為兩個獨立的匯入宣告", "Spread_operator_in_new_expressions_is_only_available_when_targeting_ECMAScript_5_and_higher_2472": "只有當目標為 ECMAScript 5 及更高版本時，才可使用 'new' 運算式中的擴張運算子。", "Spread_types_may_only_be_created_from_object_types_2698": "Spread 類型只能從物件類型建立。", "Starting_compilation_in_watch_mode_6031": "在監看模式中開始編譯...", "Statement_expected_1129": "必須是陳述式。", "Statements_are_not_allowed_in_ambient_contexts_1036": "環境內容中不得有陳述式。", "Static_members_cannot_reference_class_type_parameters_2302": "靜態成員不得參考類別類型參數。", "Static_property_0_conflicts_with_built_in_property_Function_0_of_constructor_function_1_2699": "靜態屬性 '{0}' 與建構函式 '{1}' 的內建屬性 'Function.{0}' 相衝突。", "String_literal_expected_1141": "必須是字串常值。", "String_literal_import_and_export_names_are_not_supported_when_the_module_flag_is_set_to_es2015_or_es_18057": "當 '--module' 旗標設定為 'es2015' 或 'es2020' 時，不支援字串常值匯入和匯出名稱。", "String_literal_with_double_quotes_expected_1327": "應有具雙引號的字串常值。", "Stylize_errors_and_messages_using_color_and_context_experimental_6073": "使用色彩及內容來設計錯誤與訊息的風格 (實驗)。", "Subpattern_flags_must_be_present_when_there_is_a_minus_sign_1504": "當有減號時，必須有子模式旗標。", "Subsequent_property_declarations_must_have_the_same_type_Property_0_must_be_of_type_1_but_here_has_t_2717": "後續的屬性宣告必須具有相同的類型。屬性 '{0}' 的類型必須是 '{1}'，但此處卻是類型 '{2}'。", "Subsequent_variable_declarations_must_have_the_same_type_Variable_0_must_be_of_type_1_but_here_has_t_2403": "後續的變數宣告必須具有相同的類型。變數 '{0}' 的類型必須是 '{1}' 但卻是 '{2}'。", "Substitution_0_for_pattern_1_has_incorrect_type_expected_string_got_2_5064": "模式 '{1}' 的替代 '{0}' 類型不正確，必須為 'string'，但得到 '{2}'。", "Substitution_0_in_pattern_1_can_have_at_most_one_Asterisk_character_5062": "模式 '{1}' 中的替代 '{0}' 最多可有一個 '*' 字元。", "Substitutions_for_pattern_0_should_be_an_array_5063": "模式 '{0}' 的替代應為陣列。", "Substitutions_for_pattern_0_shouldn_t_be_an_empty_array_5066": "模式 '{0}' 的替代項目不應為空陣列。", "Successfully_created_a_tsconfig_json_file_6071": "已成功建立 tsconfig.json 檔案。", "Super_calls_are_not_permitted_outside_constructors_or_in_nested_functions_inside_constructors_2337": "建構函式外部或建構函式內的巢狀函式中不允許 super 呼叫。", "Suppress_excess_property_checks_for_object_literals_6072": "不對物件常值進行多餘的屬性檢查。", "Suppress_noImplicitAny_errors_for_indexing_objects_lacking_index_signatures_6055": "針對缺少索引簽章的索引物件隱藏 noImplicitAny 錯誤。", "Suppress_noImplicitAny_errors_when_indexing_objects_that_lack_index_signatures_6703": "針對缺少索引簽章的物件編製索引時，隱藏 'noImplicitAny' 錯誤。", "Switch_each_misused_0_to_1_95138": "將每個誤用的 '{0}' 切換成 '{1}'", "Synchronously_call_callbacks_and_update_the_state_of_directory_watchers_on_platforms_that_don_t_supp_6704": "在不支援原生遞迴監視之平台上，同步呼叫回呼並更新目錄監控程式的狀態。", "Syntax_Colon_0_6023": "語法: {0}", "Tag_0_expects_at_least_1_arguments_but_the_JSX_factory_2_provides_at_most_3_6229": "標籤 '{0}' 至少需要 '{1}' 個引數，但 JSX factory '{2}' 最多只提供 '{3}' 個。", "Tagged_template_expressions_are_not_permitted_in_an_optional_chain_1358": "選擇性鏈結中不允許已標記的範本運算式。", "Target_allows_only_0_element_s_but_source_may_have_more_2621": "目標只允許 {0} 個元素，但來源的元素可能較多。", "Target_requires_0_element_s_but_source_may_have_fewer_2620": "目標需要 {0} 個元素，但來源的元素可能較少。", "Target_signature_provides_too_few_arguments_Expected_0_or_more_but_got_1_2849": "目標特徵標記提供的引數過少。應有 {0} 個或更多，但只取得 {1} 個。", "The_0_modifier_can_only_be_used_in_TypeScript_files_8009": "'{0}' 修飾元只可用於 TypeScript 檔案中。", "The_0_operator_cannot_be_applied_to_type_symbol_2469": "無法將 '{0}' 運算子套用至類型 'symbol'。", "The_0_operator_is_not_allowed_for_boolean_types_Consider_using_1_instead_2447": "布林類型不允許有 '{0}' 運算子。請考慮改用 '{1}'。", "The_0_property_of_an_async_iterator_must_be_a_method_2768": "非同步迭代器的 '{0}' 屬性必須為方法。", "The_0_property_of_an_iterator_must_be_a_method_2767": "迭代器的 '{0}' 屬性必須為方法。", "The_Object_type_is_assignable_to_very_few_other_types_Did_you_mean_to_use_the_any_type_instead_2696": "'Object' 類型可指派給極少數的其他類型。要改用 'any' 類型嗎?", "The_Unicode_u_flag_and_the_Unicode_Sets_v_flag_cannot_be_set_simultaneously_1502": "無法同時設定 Unicode (u) 旗標和 Unicode Sets (v) 旗標。", "The_arguments_object_cannot_be_referenced_in_an_arrow_function_in_ES5_Consider_using_a_standard_func_2496": "在 ES5 中，箭號函式內無法參考 'arguments' 物件。建議使用標準函式運算式。", "The_arguments_object_cannot_be_referenced_in_an_async_function_or_method_in_ES5_Consider_using_a_sta_2522": "在 ES5 中，非同步函式或方法無法參考 'arguments' 物件。建議使用標準函式或方法。", "The_body_of_an_if_statement_cannot_be_the_empty_statement_1313": "'if' 陳述式的主體不能是空白陳述式。", "The_call_would_have_succeeded_against_this_implementation_but_implementation_signatures_of_overloads_2793": "對此實作的呼叫會成功，但多載的實作簽章未向外部顯示。", "The_character_set_of_the_input_files_6163": "輸入檔的字元集。", "The_containing_arrow_function_captures_the_global_value_of_this_7041": "包含的箭號函式會擷取 'this' 的全域值。", "The_containing_function_or_module_body_is_too_large_for_control_flow_analysis_2563": "內含的函式或模組主體對控制流程分析而言過大。", "The_current_file_is_a_CommonJS_module_and_cannot_use_await_at_the_top_level_1309": "目前的檔案是 CommonJS 模組，無法在最上層使用 'await'。", "The_current_file_is_a_CommonJS_module_whose_imports_will_produce_require_calls_however_the_reference_1479": "目前的檔案是 CommonJS 模組，其匯入將會產生 'require' 呼叫;不過，參考的檔案是 ECMAScript 模組，無法以 'require' 匯入。請考慮改為撰寫動態 'import(\"{0}\")' 呼叫。", "The_current_host_does_not_support_the_0_option_5001": "目前的主機不支援 '{0}' 選項。", "The_declaration_of_0_that_you_probably_intended_to_use_is_defined_here_18018": "您可能要使用的 '{0}' 宣告定義於此處", "The_declaration_was_marked_as_deprecated_here_2798": "該宣告在這裡標示為已淘汰。", "The_expected_type_comes_from_property_0_which_is_declared_here_on_type_1_6500": "所需類型來自屬性 '{0}'，該屬性宣告於此處的類型 '{1}' 上", "The_expected_type_comes_from_the_return_type_of_this_signature_6502": "所需類型來自此簽章的傳回型別。", "The_expected_type_comes_from_this_index_signature_6501": "所需類型來自此索引簽章。", "The_expression_of_an_export_assignment_must_be_an_identifier_or_qualified_name_in_an_ambient_context_2714": "匯出指派的運算式必須是環境內容中的識別碼或完整名稱。", "The_file_is_in_the_program_because_Colon_1430": "檔案在程式中，因為:", "The_files_list_in_config_file_0_is_empty_18002": "設定檔 '{0}' 中的 'files' 清單是空的。", "The_first_export_default_is_here_2752": "第一個匯出預設位於此處。", "The_first_parameter_of_the_then_method_of_a_promise_must_be_a_callback_1060": "Promise 的 'then' 方法第一個參數必須為回撥。", "The_global_type_JSX_0_may_not_have_more_than_one_property_2608": "全域類型 'JSX.{0}' 的屬性不得超過一個。", "The_implementation_signature_is_declared_here_2750": "實作簽章宣告於此處。", "The_import_meta_meta_property_is_not_allowed_in_files_which_will_build_into_CommonJS_output_1470": "將會組建至 CommonJS 輸出的檔案中不允許 'import.meta' 中繼屬性。", "The_import_meta_meta_property_is_only_allowed_when_the_module_option_is_es2020_es2022_esnext_system__1343": "只有當 '--module' 選項為 'es2020'、'es2022'、'esnext'、'system'、'node16'、'node18' 或 'nodenext' 時，才允許 'import.meta' 中繼屬性。", "The_inferred_type_of_0_cannot_be_named_without_a_reference_to_1_This_is_likely_not_portable_A_type_a_2742": "'{0}' 的推斷類型無法在沒有 '{1}' 參考的情況下命名。其可能非可攜式。必須有型別註解。", "The_inferred_type_of_0_references_a_type_with_a_cyclic_structure_which_cannot_be_trivially_serialize_5088": "'{0}' 的推斷型別參考了具有迴圈結構且不是可完整序列化的型別。必須有型別註解。", "The_inferred_type_of_0_references_an_inaccessible_1_type_A_type_annotation_is_necessary_2527": "'{0}' 的推斷型別參考了無法存取的 '{1}' 型別。必須有型別註解。", "The_inferred_type_of_this_node_exceeds_the_maximum_length_the_compiler_will_serialize_An_explicit_ty_7056": "此節點的推斷型別超過編譯器將序列化的長度上限。需要明確的型別註解。", "The_initializer_of_a_using_declaration_must_be_either_an_object_with_a_Symbol_dispose_method_or_be_n_2850": "'using' 宣告的初始設定式必須是具備 '[Symbol.dispose]()' 方法的物件，或是 'null' 或 'undefined'。", "The_initializer_of_an_await_using_declaration_must_be_either_an_object_with_a_Symbol_asyncDispose_or_2851": "'await using' 宣告的初始設定式必須是具備 '[Symbol.asyncDispose]()' 方法或 '[Symbol.dispose]5D;()' 方法的物件，或是 'null' 或 'undefined'。", "The_intersection_0_was_reduced_to_never_because_property_1_exists_in_multiple_constituents_and_is_pr_18032": "因為屬性 '{1}' 存在於多個部分，而且在某些部分為私人性質，所以交集 '{0}' 已縮減為 'never'。", "The_intersection_0_was_reduced_to_never_because_property_1_has_conflicting_types_in_some_constituent_18031": "因為屬性 '{1}' 在某些部分有衝突的類型，所以交集 '{0}' 已縮減為 'never'。", "The_intrinsic_keyword_can_only_be_used_to_declare_compiler_provided_intrinsic_types_2795": "'intrinsic' 關鍵字只可用於宣告編譯器提供的內建類型。", "The_jsxFragmentFactory_compiler_option_must_be_provided_to_use_JSX_fragments_with_the_jsxFactory_com_17016": "必須提供 'jsxFragmentFactory' 編譯器選項，才能使用具有 'jsxFactory' 編譯器選項的 JSX 片段。", "The_last_overload_gave_the_following_error_2770": "最後一個多載出現下列錯誤。", "The_last_overload_is_declared_here_2771": "最後一個多載宣告於此處。", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_destructuring_pattern_2491": "'for...in' 陳述式的左側不得為解構模式。", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_using_declaration_1493": "'for...in' 陳述式左側不可為 'using' 宣告。", "The_left_hand_side_of_a_for_in_statement_cannot_be_an_await_using_declaration_1494": "'for...in' 陳述式左側不可為 'await using' 宣告。", "The_left_hand_side_of_a_for_in_statement_cannot_use_a_type_annotation_2404": "'for...in' 陳述式左側不得使用類型註釋。", "The_left_hand_side_of_a_for_in_statement_may_not_be_an_optional_property_access_2780": "'for...in' 陳述式的左側不可為選擇性屬性存取。", "The_left_hand_side_of_a_for_in_statement_must_be_a_variable_or_a_property_access_2406": "'for...in' 陳述式的左邊必須是變數或屬性存取。", "The_left_hand_side_of_a_for_in_statement_must_be_of_type_string_or_any_2405": "'for...in' 陳述式的左側必須是類型 'string' 或 'any'。", "The_left_hand_side_of_a_for_of_statement_cannot_use_a_type_annotation_2483": "'for...of' 陳述式的左側不得使用類型註釋。", "The_left_hand_side_of_a_for_of_statement_may_not_be_an_optional_property_access_2781": "'for...of' 陳述式的左側不可為選擇性屬性存取。", "The_left_hand_side_of_a_for_of_statement_may_not_be_async_1106": "'for...of' 陳述式的左側不可為 'async'。", "The_left_hand_side_of_a_for_of_statement_must_be_a_variable_or_a_property_access_2487": "'for...of' 陳述式的左邊必須是變數或屬性存取。", "The_left_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2362": "算術運算的左側內容必須屬於 'any'、'number'、'bigint' 或列舉類型。", "The_left_hand_side_of_an_assignment_expression_may_not_be_an_optional_property_access_2779": "指派運算式的左側不可為選擇性屬性存取。", "The_left_hand_side_of_an_assignment_expression_must_be_a_variable_or_a_property_access_2364": "指派運算式的左邊必須是變數或屬性存取。", "The_left_hand_side_of_an_instanceof_expression_must_be_assignable_to_the_first_argument_of_the_right_2860": "'instanceof' 運算式的左側必須可指派至右側 '[Symbol.hasInstance]' 方法的第一個引數。", "The_left_hand_side_of_an_instanceof_expression_must_be_of_type_any_an_object_type_or_a_type_paramete_2358": "'instanceof' 運算式左側必須是類型 'any'、物件類型或型別參數。", "The_locale_used_when_displaying_messages_to_the_user_e_g_en_us_6156": "對使用者顯示訊息時所使用的地區設定 (例如 'zh-tw')", "The_maximum_dependency_depth_to_search_under_node_modules_and_load_JavaScript_files_6136": "在 node_modules 及載入 JavaScript 檔案下搜尋時的最大相依性深度。", "The_operand_of_a_delete_operator_cannot_be_a_private_identifier_18011": "'delete' 運算子的運算元不可為私人識別碼。", "The_operand_of_a_delete_operator_cannot_be_a_read_only_property_2704": "'delete' 運算子的運算元不可為唯讀屬性。", "The_operand_of_a_delete_operator_must_be_a_property_reference_2703": "'delete' 運算子的運算元必須為屬性參考。", "The_operand_of_a_delete_operator_must_be_optional_2790": "'delete' 運算子的運算元必須是非必須。", "The_operand_of_an_increment_or_decrement_operator_may_not_be_an_optional_property_access_2777": "遞增或遞減運算子的運算元不可為選擇性屬性存取。", "The_operand_of_an_increment_or_decrement_operator_must_be_a_variable_or_a_property_access_2357": "遞增或遞減運算子的運算元必須是變數或屬性存取。", "The_parser_expected_to_find_a_1_to_match_the_0_token_here_1007": "剖析器需要找到 '{1}'，以對應此處的 '{0}' 權杖。", "The_project_root_is_ambiguous_but_is_required_to_resolve_export_map_entry_0_in_file_1_Supply_the_roo_2209": "專案根目錄模棱兩可，但需要用以解決檔案 '{1}' 中的匯出對應項目 '{0}'。請提供 'rootDir' 編譯器選項來釐清。", "The_project_root_is_ambiguous_but_is_required_to_resolve_import_map_entry_0_in_file_1_Supply_the_roo_2210": "專案根目錄模稜兩可，但需要在檔案 '{1}' 中解析匯入對應項目 '{0}'。請提供 'rootDir' 編譯器選項來釐清。", "The_property_0_cannot_be_accessed_on_type_1_within_this_class_because_it_is_shadowed_by_another_priv_18014": "無法在此類別內的類型 '{1}' 上存取屬性 '{0}'，原因是另一個拼字相同的私人識別碼已將其陰影。", "The_return_type_of_a_parameter_decorator_function_must_be_either_void_or_any_1237": "參數裝飾項目函式的傳回型別必須是 'void' 或 'any'。", "The_return_type_of_a_property_decorator_function_must_be_either_void_or_any_1236": "屬性裝飾項目函式的傳回型別必須是 'void' 或 'any'。", "The_return_type_of_an_async_function_must_either_be_a_valid_promise_or_must_not_contain_a_callable_t_1058": "非同步函式的傳回型別必須是有效的 Promise，或不得包含可呼叫的 'then' 成員。", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_1065": "非同步函式或方法的傳回類型必須為全域 Promise<T> 類型。", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_Did_you_mean_to_wri_1064": "非同步函式或方法的傳回型別，必須為全域 Promise<T> 類型。是否要寫入 'Promise<{0}>'?", "The_right_hand_side_of_a_for_in_statement_must_be_of_type_any_an_object_type_or_a_type_parameter_but_2407": "'for...in' 陳述式的右方必須是類型 'any'、物件類型或型別參數，但此處為類型 '{0}'。", "The_right_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2363": "算術運算的右側內容必須屬於 'any'、'number'、'bigint' 或列舉類型。", "The_right_hand_side_of_an_instanceof_expression_must_be_either_of_type_any_a_class_function_or_other_2359": "'instanceof' 運算式右側必須為 'any' 類型、類別、函式或其他可指派至 'Function' 介面型別的型別，或是具備 'Symbol.hasInstance' 方法的物件類型。", "The_right_hand_side_of_an_instanceof_expression_must_not_be_an_instantiation_expression_2848": "'instanceof' 運算式右側不可為具現化運算式。", "The_root_value_of_a_0_file_must_be_an_object_5092": "'{0}' 檔案的根值必須是物件。", "The_runtime_will_invoke_the_decorator_with_1_arguments_but_the_decorator_expects_0_1278": "執行階段會以 {1} 個引數叫用裝飾項目，但裝飾項目需要 {0} 個。", "The_runtime_will_invoke_the_decorator_with_1_arguments_but_the_decorator_expects_at_least_0_1279": "執行階段會以 {1} 個引數叫用裝飾項目，但裝飾項目至少需要 {0} 個。", "The_shadowing_declaration_of_0_is_defined_here_18017": "'{0}' 的隱蔽宣告定義於此處", "The_signature_0_of_1_is_deprecated_6387": "'{1}' 的特徵標記 '{0}' 已淘汰。", "The_specified_path_does_not_exist_Colon_0_5058": "指定的路徑不存在: '{0}'。", "The_tag_was_first_specified_here_8034": "此標籤第一次指定於此處。", "The_target_of_an_object_rest_assignment_may_not_be_an_optional_property_access_2778": "物件其餘指派的目標不可為選擇性屬性存取。", "The_target_of_an_object_rest_assignment_must_be_a_variable_or_a_property_access_2701": "物件剩餘指派的目標必須為變數或屬性存取。", "The_this_context_of_type_0_is_not_assignable_to_method_s_this_of_type_1_2684": "類型 '{0}' 的 'this' 內容無法指派給方法之類型 '{1}' 的 'this'。", "The_this_types_of_each_signature_are_incompatible_2685": "各個簽章的 'this' 類型不相容。", "The_type_0_is_readonly_and_cannot_be_assigned_to_the_mutable_type_1_4104": "類型 '{0}' 為 'readonly'，因此無法指派給可變動的類型 '{1}'。", "The_type_modifier_cannot_be_used_on_a_named_export_when_export_type_is_used_on_its_export_statement_2207": "[類型修飾元] 無法在 [匯出類型]5D; 於其匯出陳述式上使用時，在命名的匯出上使用。", "The_type_modifier_cannot_be_used_on_a_named_import_when_import_type_is_used_on_its_import_statement_2206": "[類型修飾元] 無法在 [匯入類型]5D; 於其匯入陳述式上使用時，在命名的匯入上使用。", "The_type_of_a_function_declaration_must_match_the_function_s_signature_8030": "函式宣告的類型必須與函式的簽章相符。", "The_type_of_this_node_cannot_be_serialized_because_its_property_0_cannot_be_serialized_4118": "無法將此節點的類型序列化，因為無法將其屬性 '{0}' 序列化。", "The_type_returned_by_the_0_method_of_an_async_iterator_must_be_a_promise_for_a_type_with_a_value_pro_2547": "非同步迭代器 '{0}()' 方法所傳回的類型，對具有 'value' 屬性的類型必須為 Promise。", "The_type_returned_by_the_0_method_of_an_iterator_must_have_a_value_property_2490": "迭代器 '{0}()' 方法所傳回的類型必須具有 'value' 屬性。", "The_types_of_0_are_incompatible_between_these_types_2200": "'{0}' 的類型在這些類型之間不相容。", "The_types_returned_by_0_are_incompatible_between_these_types_2201": "'{0}' 所傳回的類型在這些類型之間不相容。", "The_value_0_cannot_be_used_here_18050": "此處無法使用值 '{0}'。", "The_variable_declaration_of_a_for_in_statement_cannot_have_an_initializer_1189": "'for...in' 陳述式的變數宣告不得有初始設定式。", "The_variable_declaration_of_a_for_of_statement_cannot_have_an_initializer_1190": "'for...of' 陳述式的變數宣告不得有初始設定式。", "The_with_statement_is_not_supported_All_symbols_in_a_with_block_will_have_type_any_2410": "不支援 'with' 陳述式。'with' 區塊中的所有符號都會有類型 'any'。", "There_are_types_at_0_but_this_result_could_not_be_resolved_under_your_current_moduleResolution_setti_6280": "'{0}' 具有型別，不過在目前的 'moduleResolution' 設定下，無法解析此結果。建議更新為 'node16'、'nodenext' 或 'bundler'。", "There_are_types_at_0_but_this_result_could_not_be_resolved_when_respecting_package_json_exports_The__6278": "'{0}' 具有型別，不過在採用 package.json \"exports\" 的狀態下，無法解析此結果。'{1}' 程式庫可能需要更新其 package.json 或輸入。", "There_is_no_capturing_group_named_0_in_this_regular_expression_1532": "此規則運算式中沒有名為 '{0}' 的擷取群組。", "There_is_nothing_available_for_repetition_1507": "沒有可重複的內容。", "This_JSX_tag_requires_0_to_be_in_scope_but_it_could_not_be_found_2874": "此 JSX 標籤需要 '{0}' 在範圍內，但無法找到。", "This_JSX_tag_requires_the_module_path_0_to_exist_but_none_could_be_found_Make_sure_you_have_types_fo_2875": "此 JSX 標籤需要模組路徑 '{0}' 存在，但無法找到。請確定您已安裝適當的套件類型。", "This_JSX_tag_s_0_prop_expects_a_single_child_of_type_1_but_multiple_children_were_provided_2746": "此 JSX 標籤的 '{0}' 屬性只能有一個 '{1}' 類型的子系，但提供的子系卻有多個。", "This_JSX_tag_s_0_prop_expects_type_1_which_requires_multiple_children_but_only_a_single_child_was_pr_2745": "此 JSX 標籤的 '{0}' 屬性需要必須有多個子系的類型 '{1}'，但僅提供的子系只有一個。", "This_backreference_refers_to_a_group_that_does_not_exist_There_are_no_capturing_groups_in_this_regul_1534": "此反向參考參照的群組不存在。此規則運算式中沒有任何擷取群組。", "This_backreference_refers_to_a_group_that_does_not_exist_There_are_only_0_capturing_groups_in_this_r_1533": "此反向參考參照的群組不存在。此規則運算式中只有 {0} 個擷取群組。", "This_binary_expression_is_never_nullish_Are_you_missing_parentheses_2870": "此二進位運算式一律不會是 nullish。是否缺少括弧?", "This_character_cannot_be_escaped_in_a_regular_expression_1535": "此字元無法在規則運算式中逸出。", "This_comparison_appears_to_be_unintentional_because_the_types_0_and_1_have_no_overlap_2367": "此比較似乎是無意的，因為類型 '{0}' 和 '{1}' 沒有重疊。", "This_condition_will_always_return_0_2845": "此條件一律傳回 '{0}'。", "This_condition_will_always_return_0_since_JavaScript_compares_objects_by_reference_not_value_2839": "此條件一律會傳回 '{0}'，因為 JavaScript 會依參照而非值比較物件。", "This_condition_will_always_return_true_since_this_0_is_always_defined_2801": "因為此 '{0}' 一律會被定義，所以此條件一律傳回 True。", "This_condition_will_always_return_true_since_this_function_is_always_defined_Did_you_mean_to_call_it_2774": "因為永遠會定義此函式，所以此條件永遠會傳回 true。您是要改為呼叫該條件嗎?", "This_constructor_function_may_be_converted_to_a_class_declaration_80002": "此建構函式可轉換為類別宣告。", "This_expression_is_always_nullish_2871": "此運算式一律為 nullish.", "This_expression_is_not_callable_2349": "無法呼叫此運算式。", "This_expression_is_not_callable_because_it_is_a_get_accessor_Did_you_mean_to_use_it_without_6234": "因為此運算式為 'get' 存取子，所以無法呼叫。要在沒有 '()' 的情況下，使用該運算式嗎?", "This_expression_is_not_constructable_2351": "無法建構此運算式。", "This_file_already_has_a_default_export_95130": "此檔案已有預設匯出", "This_import_path_is_unsafe_to_rewrite_because_it_resolves_to_another_project_and_the_relative_path_b_2878": "重寫此匯入路徑並不安全，因為其解析到另一個專案，而專案輸出檔案之間的相對路徑與其輸入檔案之間的相對路徑不同。", "This_import_uses_a_0_extension_to_resolve_to_an_input_TypeScript_file_but_will_not_be_rewritten_duri_2877": "這個匯入使用 '{0}' 副檔名來解析到輸入的 TypeScript 檔案，但在發出時不會重寫，因為其不是相對路徑。", "This_is_the_declaration_being_augmented_Consider_moving_the_augmenting_declaration_into_the_same_fil_6233": "此宣告正在增加中。請考慮將正在增加的宣告移至相同的檔案中。", "This_kind_of_expression_is_always_falsy_2873": "此種運算式的值一律為 false。", "This_kind_of_expression_is_always_truthy_2872": "此種運算式的值一律為 true。", "This_may_be_converted_to_an_async_function_80006": "這可以轉換為非同步函式。", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4122": "此成員不能包含具有 '@override' 標籤的 JSDoc 註解，因為並未在基底類別 '{0}' 中宣告此成員。", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4123": "此成員不能包含具有 'override' 標籤的 JSDoc 註解，因為並未在基底類別 '{0}' 中宣告此成員。您指的是 '{1}' 嗎?", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_containing_class_0_does_not_4121": "此成員不能包含具有 '@override' 標籤的 JSDoc 註解，因為包含此成員的類別 '{0}' 並未延伸另一個類別。", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_name_is_dynamic_4128": "此成員不能包含具有 '@override' 標籤的 JSDoc 註解，因為其名稱為動態。", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_4113": "因為此成員並未在基底類別 '{0}' 中宣告，所以其不得具有 'override' 修飾元。", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_Did_you__4117": "此成員不能具有 'override' 修飾元，因為並未在基底類別 '{0}' 中宣告此成員。您指的是 '{1}' 嗎?", "This_member_cannot_have_an_override_modifier_because_its_containing_class_0_does_not_extend_another__4112": "因為此成員包含的類別 '{0}' 並未延伸其他類別，所以其不得具有 'override' 修飾元。", "This_member_cannot_have_an_override_modifier_because_its_name_is_dynamic_4127": "此成員的名稱為動態，因此不能有 『override』 修飾元。", "This_member_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_in_the_base_4119": "此成員必須包含具有 '@override' 標籤的 JSDoc 註解，因為其會覆寫基底類別 '{0}' 中的成員。", "This_member_must_have_an_override_modifier_because_it_overrides_a_member_in_the_base_class_0_4114": "因為此成員會覆寫基底類別 '{0}' 中的成員，所以其必須具有 'override' 修飾元。", "This_member_must_have_an_override_modifier_because_it_overrides_an_abstract_method_that_is_declared__4116": "因為此成員會覆寫基底類別 '{0}' 中宣告的抽象方法，所以其必須具有 'override' 修飾元。", "This_module_can_only_be_referenced_with_ECMAScript_imports_Slashexports_by_turning_on_the_0_flag_and_2497": "只能以 ECMAScript 匯入/匯出來參考此模組，方法為開啟 '{0}' 旗標並參考其預設匯出。", "This_module_is_declared_with_export_and_can_only_be_used_with_a_default_import_when_using_the_0_flag_2594": "此模組使用 'export =' 宣告，只能在使用 '{0}' 旗標時搭配預設匯入使用。", "This_operation_can_be_simplified_This_shift_is_identical_to_0_1_2_6807": "此作業可簡化。此班次與 `{0} {1} {2}` 完全相同。", "This_overload_implicitly_returns_the_type_0_because_it_lacks_a_return_type_annotation_7012": "由於缺少傳回型別註解，故此多載會隱含傳回 '{0}' 型別。", "This_overload_signature_is_not_compatible_with_its_implementation_signature_2394": "此多載簽章與其實作簽章不相容。", "This_parameter_is_not_allowed_with_use_strict_directive_1346": "不允許此參數搭配 'use strict' 指示詞使用。", "This_parameter_property_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_4120": "此參數屬性必須包含具有 '@override' 標籤的 JSDoc 註解，因為其會覆寫基底類別 '{0}' 中的成員。", "This_parameter_property_must_have_an_override_modifier_because_it_overrides_a_member_in_base_class_0_4115": "因為此參數屬性會覆寫基底類別 '{0}' 中的成員，所以其必須具有 'override' 修飾元。", "This_regular_expression_flag_cannot_be_toggled_within_a_subpattern_1509": "無法在子模式內切換此規則運算式旗標。", "This_regular_expression_flag_is_only_available_when_targeting_0_or_later_1501": "只有以 '{0}' 或更新版本作為目標時，才能使用規則運算式旗標。", "This_relative_import_path_is_unsafe_to_rewrite_because_it_looks_like_a_file_name_but_actually_resolv_2876": "此相對匯入路徑在重寫時是不安全的，因為其看起來像檔案名稱，但實際上解析為 \"{0}\"。", "This_spread_always_overwrites_this_property_2785": "此展開會永遠覆寫此屬性。", "This_syntax_is_not_allowed_when_erasableSyntaxOnly_is_enabled_1294": "啟用 'erasableSyntaxOnly' 時，不允許使用此語法。", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Add_a_trailing_comma_or_explicit_cons_7060": "此語法是保留在具有 mts 或 cts 副檔名的檔案中。新增尾端逗號或明確條件約束。", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Use_an_as_expression_instead_7059": "此語法會保留在具有 mts 或 cts 副檔名的檔案中。請改用 `as` 運算式。", "This_syntax_requires_an_imported_helper_but_module_0_cannot_be_found_2354": "此語法需要已匯入的協助程式，但找不到模組 '{0}'。", "This_syntax_requires_an_imported_helper_named_1_which_does_not_exist_in_0_Consider_upgrading_your_ve_2343": "此語法需要名為 '{1}' 的已匯入協助程式，但其不存在於 '{0}' 中。請考慮升級您的 '{0}' 版本。", "This_syntax_requires_an_imported_helper_named_1_with_2_parameters_which_is_not_compatible_with_the_o_2807": "此語法需要名為 '{1}' 且具有 {2} 參數的匯入協助程式，其與 '{0}' 中的參數不相容。請考慮升級 '{0}' 的版本。", "This_type_parameter_might_need_an_extends_0_constraint_2208": "此類型參數可能需要 'extends {0}' 限制式。", "This_use_of_import_is_invalid_import_calls_can_be_written_but_they_must_have_parentheses_and_cannot__1326": "此 'import' 的使用方式無效。'import()' 呼叫可以寫入，但必須有括弧，而且不能有類型引數。", "To_convert_this_file_to_an_ECMAScript_module_add_the_field_type_Colon_module_to_0_1482": "若要將此檔案轉換為 ECMAScript 模組，請將欄位 `{ \"type\": \"module\" }` 新增至 '{0}'。", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_add_the_field_type_Co_1481": "若要將此檔案轉換為 ECMAScript 模組，請將其副檔名變更為 '{0}'，或將欄位 `\"type\": \"module\"` 新增至 '{1}'。", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_create_a_local_packag_1480": "若要將此檔案轉換為 ECMAScript 模組，請將其副檔名變更為 '{0}'，或使用 `{ \"type\": \"module\" }` 建立本機 package.json 檔案。", "To_convert_this_file_to_an_ECMAScript_module_create_a_local_package_json_file_with_type_Colon_module_1483": "若要將此檔案轉換為 ECMAScript 模組，請建立具有 `{ \"type\": \"module\" }` 的本機 package.json 檔案。", "Top_level_await_expressions_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_n_1378": "只有在 'module' 選項設為 'es2022'、'esnext'、'system'、'node16'、'node18'、'nodenext' 或 'preserve'，而且 'target' 選項設為 'es2017' 或更高版本時，才允許最上層的 'await' 運算式。", "Top_level_await_using_statements_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_sys_2854": "只有當 'module' 選項設為 'es2022'、'esnext'、'system'、'node16'、'node18'、'nodenext' 或 'preserve'，且 'target' 選項設為 'es2017' 或更高版本時，才能在最上層使用 'await using' 陳述式。", "Top_level_declarations_in_d_ts_files_must_start_with_either_a_declare_or_export_modifier_1046": ".d.ts 檔案中的最上層宣告必須以 'declare' 或 'export' 修飾元開頭。", "Top_level_for_await_loops_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_nod_1432": "只有在 'module' 選項設為 'es2022'、'esnext'、'system'、'node16'、'node18'、'nodenext' 或 'preserve'，而且 'target' 選項設為 'es2017' 或更高版本時，才允許最上層的 'for await' 迴圈。", "Trailing_comma_not_allowed_1009": "尾端不得為逗號。", "Transpile_each_file_as_a_separate_module_similar_to_ts_transpileModule_6153": "以個別模組的形式轉換每個檔案的語言 (類似於 'ts.transpileModule')。", "Try_npm_i_save_dev_types_Slash_1_if_it_exists_or_add_a_new_declaration_d_ts_file_containing_declare__7035": "如有 `npm i --save-dev @types/{1}`，請嘗試使用，或新增包含 `declare module '{0}';` 的宣告 (.d.ts) 檔案", "Trying_other_entries_in_rootDirs_6110": "正在嘗試 'rootDirs' 中的其他項目。", "Trying_substitution_0_candidate_module_location_Colon_1_6093": "正在嘗試替代 '{0}'，候選模組位置: '{1}'。", "Tuple_type_0_of_length_1_has_no_element_at_index_2_2493": "長度為 '{1}' 的元組類型 '{0}' 在索引 '{2}' 沒有項目。", "Tuple_type_arguments_circularly_reference_themselves_4110": "元組類型引數會循環參考自身。", "Type_0_can_only_be_iterated_through_when_using_the_downlevelIteration_flag_or_with_a_target_of_es201_2802": "只有使用 '--downlevelIteration' 旗標或 'es2015' 或更新版本的 '--target' 時，才能逐一查看類型 '{0}'。", "Type_0_cannot_be_used_as_an_index_type_2538": "類型 '{0}' 無法作為索引類型。", "Type_0_cannot_be_used_to_index_type_1_2536": "類型 '{0}' 無法用來為類型 '{1}' 編制索引。", "Type_0_does_not_satisfy_the_constraint_1_2344": "類型 '{0}' 不符合條件約束 '{1}'。", "Type_0_does_not_satisfy_the_expected_type_1_1360": "類型 '{0}' 不符合預期類型 '{1}'。", "Type_0_has_no_call_signatures_2757": "類型 '{0}' 沒有任何呼叫簽章。", "Type_0_has_no_construct_signatures_2761": "類型 '{0}' 沒有任何建構簽章。", "Type_0_has_no_matching_index_signature_for_type_1_2537": "類型 '{0}' 沒有與類型 '{1}' 相符的索引簽章。", "Type_0_has_no_properties_in_common_with_type_1_2559": "類型 '{0}' 與類型 '{1}' 沒有任何共通的屬性。", "Type_0_has_no_signatures_for_which_the_type_argument_list_is_applicable_2635": "類型 '{0}' 沒有適用類型引數清單的簽章。", "Type_0_is_generic_and_can_only_be_indexed_for_reading_2862": "類型 '{0}' 為泛型型別，只能針對讀取編製索引。", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_2739": "類型 '{0}' 在類型 '{1}' 中缺少下列屬性: {2}", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_and_3_more_2740": "類型 '{0}' 在類型 '{1}' 中缺少下列屬性: {2}，以及另外 {3} 個。", "Type_0_is_not_a_constructor_function_type_2507": "類型 '{0}' 不是建構函式類型。", "Type_0_is_not_a_valid_async_function_return_type_in_ES5_because_it_does_not_refer_to_a_Promise_compa_1055": "在 ES5 中，'{0}' 型別並非有效的非同步傳回型別，因為其不會參考與 Promise 相容的建構函式值。", "Type_0_is_not_an_array_type_2461": "類型 '{0}' 不是陣列類型。", "Type_0_is_not_an_array_type_or_a_string_type_2495": "類型 '{0}' 不是陣列類型或字串類型。", "Type_0_is_not_an_array_type_or_a_string_type_or_does_not_have_a_Symbol_iterator_method_that_returns__2549": "類型 '{0}' 不是陣列類型或字串類型，或沒有會傳回迭代器的 '[Symbol.iterator]()' 方法。", "Type_0_is_not_an_array_type_or_does_not_have_a_Symbol_iterator_method_that_returns_an_iterator_2548": "類型 '{0}' 不是陣列類型，或沒有會傳回迭代器的 '[Symbol.iterator]()' 方法。", "Type_0_is_not_assignable_to_type_1_2322": "類型 '{0}' 不可指派給類型 '{1}'。", "Type_0_is_not_assignable_to_type_1_Did_you_mean_2_2820": "不得將類型 '{0}' 指派給類型 '{1}'。您指的是 '{2}' 嗎?", "Type_0_is_not_assignable_to_type_1_Two_different_types_with_this_name_exist_but_they_are_unrelated_2719": "無法將類型 '{0}' 指派給類型 '{1}'。有兩種使用此名稱的不同類型存在，但彼此並不相關。", "Type_0_is_not_assignable_to_type_1_as_implied_by_variance_annotation_2636": "無法將型別 '{0}' 指派給型別 '{1}'，如變異數註釋所隱含。", "Type_0_is_not_assignable_to_type_1_as_required_for_computed_enum_member_values_18033": "無法將類型 '{0}' 指派給計算之列舉成員值所需的 '{1}'。", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2375": "類型 '{0}' 無法指派給類型為具有 'exactOptionalPropertyTypes: true' 的類型 '{1}'。請考慮將 'undefined' 新增到目標屬性的類型。", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2412": "類型 '{0}' 無法指派給類型為具有 'exactOptionalPropertyTypes: true' 的類型 '{1}'。請考慮將 'undefined' 新增到目標的類型。", "Type_0_is_not_comparable_to_type_1_2678": "類型 '{0}' 無法和類型 '{1}' 比較。", "Type_0_is_not_generic_2315": "'{0}' 不是泛型類型。", "Type_0_may_represent_a_primitive_value_which_is_not_permitted_as_the_right_operand_of_the_in_operato_2638": "類型 '{0}' 可能代表基本值，但不允許做為 'in' 運算子的右運算元。", "Type_0_must_have_a_Symbol_asyncIterator_method_that_returns_an_async_iterator_2504": "類型 '{0}' 必須具備會傳回非同步迭代器的 '[Symbol.asyncIterator]()' 方法。", "Type_0_must_have_a_Symbol_iterator_method_that_returns_an_iterator_2488": "類型 '{0}' 必須具備會傳回迭代器的 '[Symbol.iterator]()' 方法。", "Type_0_provides_no_match_for_the_signature_1_2658": "類型 '{0}' 沒有符合特徵標記 '{1}' 的項目。", "Type_0_recursively_references_itself_as_a_base_type_2310": "類型 '{0}' 將自己當做基底類型遞迴參考。", "Type_Checking_6248": "類型檢查", "Type_alias_0_circularly_references_itself_2456": "類型別名 '{0}' 會循環參考自己。", "Type_alias_must_be_given_a_name_1439": "必須為類型別名指定名稱。", "Type_alias_name_cannot_be_0_2457": "類型別名不得為 '{0}'。", "Type_aliases_can_only_be_used_in_TypeScript_files_8008": "類型別名只可用於 TypeScript 檔案中。", "Type_annotation_cannot_appear_on_a_constructor_declaration_1093": "建構函式宣告不得有類型註釋。", "Type_annotations_can_only_be_used_in_TypeScript_files_8010": "型別註解只可用於 TypeScript 檔案中。", "Type_argument_expected_1140": "必須是型別引數。", "Type_argument_list_cannot_be_empty_1099": "型別引數清單不得為空白。", "Type_arguments_can_only_be_used_in_TypeScript_files_8011": "類型引數只可用於 TypeScript 檔案中。", "Type_arguments_for_0_circularly_reference_themselves_4109": "'{0}' 的類型引數會循環參考自身。", "Type_assertion_expressions_can_only_be_used_in_TypeScript_files_8016": "類型判斷提示運算式只可用於 TypeScript 檔案中。", "Type_at_position_0_in_source_is_not_compatible_with_type_at_position_1_in_target_2626": "來源中位於 {0} 的類型與目標中位於 {1} 的類型不相容。", "Type_at_positions_0_through_1_in_source_is_not_compatible_with_type_at_position_2_in_target_2627": "來源中位於 {0} 到 {1} 的類型與目標中位於 {2} 的類型不相容。", "Type_containing_private_name_0_can_t_be_used_with_isolatedDeclarations_9039": "型別如包含私人名稱 '{0}'，則無法搭配 --isolatedDeclarations 使用。", "Type_declaration_files_to_be_included_in_compilation_6124": "要包含在編譯內的類型宣告檔案。", "Type_expected_1110": "必須是類型。", "Type_import_assertions_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1456": "輸入匯入判斷提示應該只有一個索引鍵 - 'resolution-mode' - 值為 'import' 或 'require'。", "Type_import_attributes_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1464": "型別匯入屬性應只有一個索引鍵 'resolution-mode'，且值為 'import' 或 'require'。", "Type_import_of_an_ECMAScript_module_from_a_CommonJS_module_must_have_a_resolution_mode_attribute_1542": "從 CommonJS 模組匯入 ECMAScript 模組的類型必須有 'resolution-mode' 屬性。", "Type_instantiation_is_excessively_deep_and_possibly_infinite_2589": "類型具現化過深，可能會有無限深度。", "Type_is_referenced_directly_or_indirectly_in_the_fulfillment_callback_of_its_own_then_method_1062": "類型在其本身 'then' 方法的完成回撥中直接或間接受到參考。", "Type_library_referenced_via_0_from_file_1_1402": "透過 '{0}' 從檔案 '{1}' 參考的型別程式庫", "Type_library_referenced_via_0_from_file_1_with_packageId_2_1403": "透過 '{0}' 從檔案 '{1}' (packageId 為 '{2}') 參考的型別程式庫", "Type_of_await_operand_must_either_be_a_valid_promise_or_must_not_contain_a_callable_then_member_1320": "'await' 運算元類型必須是有效的 Promise，或不得包含可呼叫的 'then' 成員。", "Type_of_computed_property_s_value_is_0_which_is_not_assignable_to_type_1_2418": "已計算屬性值的類型為 '{0}'，其無法指派給類型 '{1}'。", "Type_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2844": "執行個體成員變數 '{0}' 的類型不得參考建構函式中所宣告的識別碼 '{1}'。", "Type_of_iterated_elements_of_a_yield_Asterisk_operand_must_either_be_a_valid_promise_or_must_not_con_1322": "'yield*' 運算元的反覆項目類型必須是有效的 Promise，或不得包含可呼叫的 'then' 成員。", "Type_of_property_0_circularly_references_itself_in_mapped_type_1_2615": "屬性 '{0}' 的類型在對應的類型 '{1}' 中會循環參考自己。", "Type_of_yield_operand_in_an_async_generator_must_either_be_a_valid_promise_or_must_not_contain_a_cal_1321": "非同步產生器中的 'yield' 運算元類型必須是有效的 Promise，或不得包含可呼叫的 'then' 成員。", "Type_only_import_of_an_ECMAScript_module_from_a_CommonJS_module_must_have_a_resolution_mode_attribut_1541": "從 CommonJS 模組進行僅限類型匯入 ECMAScript 模組時，必須有 'resolution-mode' 屬性。", "Type_originates_at_this_import_A_namespace_style_import_cannot_be_called_or_constructed_and_will_cau_7038": "類型源自此匯入。無法呼叫或建構命名空間樣式的匯入，而且可能會在執行階段導致失敗。請考慮改用預設匯入或於此處匯入 require。", "Type_parameter_0_has_a_circular_constraint_2313": "類型參數 '{0}' 具有循環條件約束。", "Type_parameter_0_has_a_circular_default_2716": "型別參數 '{0}' 包含循環的預設值。", "Type_parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4008": "匯出介面中呼叫簽章的型別參數 '{0}' 具有或使用私用名稱 '{1}'。", "Type_parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4006": "匯出介面中建構函式簽章的型別參數 '{0}' 具有或使用私用名稱 '{1}'。", "Type_parameter_0_of_exported_class_has_or_is_using_private_name_1_4002": "匯出類別的型別參數 '{0}' 具有或使用私用名稱 '{1}'。", "Type_parameter_0_of_exported_function_has_or_is_using_private_name_1_4016": "匯出函式的型別參數 '{0}' 具有或使用私用名稱 '{1}'。", "Type_parameter_0_of_exported_interface_has_or_is_using_private_name_1_4004": "匯出介面的型別參數 '{0}' 具有或使用私用名稱 '{1}'。", "Type_parameter_0_of_exported_mapped_object_type_is_using_private_name_1_4103": "已匯出對應物件類型的型別參數 '{0}' 正在使用私人名稱 '{1}'。", "Type_parameter_0_of_exported_type_alias_has_or_is_using_private_name_1_4083": "匯出類型別名的型別參數 '{0}' 具有或正在使用私人名稱 '{1}'。", "Type_parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4014": "匯出介面中方法的型別參數 '{0}' 具有或使用私用名稱 '{1}'。", "Type_parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4012": "匯出類別中公用方法的型別參數 '{0}' 具有或使用私用名稱 '{1}'。", "Type_parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4010": "匯出類別中公用靜態方法的型別參數 '{0}' 具有或使用私用名稱 '{1}'。", "Type_parameter_declaration_expected_1139": "必須是型別參數宣告。", "Type_parameter_declarations_can_only_be_used_in_TypeScript_files_8004": "型別參數宣告只可用於 TypeScript 檔案中。", "Type_parameter_defaults_can_only_reference_previously_declared_type_parameters_2744": "型別參數預設只能參考先前宣告的型別參數。", "Type_parameter_list_cannot_be_empty_1098": "型別參數清單不得為空白。", "Type_parameter_name_cannot_be_0_2368": "型別參數名稱不得為 '{0}'。", "Type_parameters_cannot_appear_on_a_constructor_declaration_1092": "建構函式宣告不得有類型參數。", "Type_predicate_0_is_not_assignable_to_1_1226": "型別述詞 '{0}' 不可指派給 '{1}'。", "Type_produces_a_tuple_type_that_is_too_large_to_represent_2799": "類型產生的元組類型太大而無法表示。", "Type_reference_directive_0_was_not_resolved_6120": "======== 類型參考指示詞 '{0}' 未解析。========", "Type_reference_directive_0_was_successfully_resolved_to_1_primary_Colon_2_6119": "======== 類型參考指示詞 '{0}' 已成功解析為 '{1}'，主要: {2}。========", "Type_reference_directive_0_was_successfully_resolved_to_1_with_Package_ID_2_primary_Colon_3_6219": "======== 類型參考指示詞 '{0}' 已成功解析為 '{1}'，套件識別碼為 '{2}'，主要: {3}。========", "Type_satisfaction_expressions_can_only_be_used_in_TypeScript_files_8037": "類型滿足運算式只可用於 TypeScript 檔案。", "Types_cannot_appear_in_export_declarations_in_JavaScript_files_18043": "類型不能出現在 JavaScript 檔案的匯出宣告中。", "Types_have_separate_declarations_of_a_private_property_0_2442": "類型具有私用屬性 '{0}' 的個別宣告。", "Types_of_construct_signatures_are_incompatible_2419": "建構簽章的類型不相容。", "Types_of_parameters_0_and_1_are_incompatible_2328": "參數 '{0}' 和 '{1}' 的類型不相容。", "Types_of_property_0_are_incompatible_2326": "屬性 '{0}' 的類型不相容。", "Unable_to_open_file_0_6050": "無法開啟檔案 '{0}'。", "Unable_to_resolve_signature_of_class_decorator_when_called_as_an_expression_1238": "無法解析以運算式形式呼叫之類別裝飾項目的簽章。", "Unable_to_resolve_signature_of_method_decorator_when_called_as_an_expression_1241": "無法解析以運算式形式呼叫之方法裝飾項目的簽章。", "Unable_to_resolve_signature_of_parameter_decorator_when_called_as_an_expression_1239": "無法解析以運算式形式呼叫之參數裝飾項目的簽章。", "Unable_to_resolve_signature_of_property_decorator_when_called_as_an_expression_1240": "無法解析以運算式形式呼叫之屬性裝飾項目的簽章。", "Undetermined_character_escape_1513": "未定字元逸出。", "Unexpected_0_Did_you_mean_to_escape_it_with_backslash_1508": "未預期的 '{0}'。您是要使用反斜線將其逸出嗎?", "Unexpected_end_of_text_1126": "未預期的文字結尾。", "Unexpected_keyword_or_identifier_1434": "未預期的關鍵字或識別碼。", "Unexpected_token_1012": "未預期的語彙基元。", "Unexpected_token_A_constructor_method_accessor_or_property_was_expected_1068": "未預期的語彙基元。必須是建構函式、方法、存取子或屬性。", "Unexpected_token_A_type_parameter_name_was_expected_without_curly_braces_1069": "權杖錯誤。類型參數名稱不應有大括號。", "Unexpected_token_Did_you_mean_or_gt_1382": "未預期的語彙基元。您是指 `{'>'}` 或 `&gt;` 嗎?", "Unexpected_token_Did_you_mean_or_rbrace_1381": "未預期的語彙基元。您是指 `{'}'}` 或 `&rbrace;` 嗎?", "Unexpected_token_expected_1179": "未預期的語彙基元。必須是 '{'。", "Unicode_escape_sequence_cannot_appear_here_17021": "此處不可出現 Unicode 逸出序列。", "Unicode_escape_sequences_are_only_available_when_the_Unicode_u_flag_or_the_Unicode_Sets_v_flag_is_se_1538": "只有在設定 Unicode (u) 旗標或 Unicode Sets (v) 旗標後，才能使用 Unicode 逸出序列。", "Unicode_property_value_expressions_are_only_available_when_the_Unicode_u_flag_or_the_Unicode_Sets_v__1530": "只有在設定 Unicode (u) 旗標或 Unicode Sets (v) 旗標後，才能使用 Unicode 屬性值運算式。", "Unknown_Unicode_property_name_1524": "未知的 Unicode 屬性名稱。", "Unknown_Unicode_property_name_or_value_1529": "未知的 Unicode 屬性名稱或值。", "Unknown_Unicode_property_value_1526": "未知的 Unicode 屬性值。", "Unknown_build_option_0_5072": "未知的組建選項 '{0}'。", "Unknown_build_option_0_Did_you_mean_1_5077": "未知的組建選項 '{0}'。您是指 '{1}' 嗎?", "Unknown_compiler_option_0_5023": "不明的編譯器選項 '{0}'。", "Unknown_compiler_option_0_Did_you_mean_1_5025": "未知的編譯器選項 '{0}'。您是指 '{1}' 嗎?", "Unknown_keyword_or_identifier_Did_you_mean_0_1435": "未知的關鍵字或識別碼。您是不是指 '{0}'?", "Unknown_option_excludes_Did_you_mean_exclude_6114": "選項 'excludes' 未知。您是指 'exclude' 嗎?", "Unknown_regular_expression_flag_1499": "未知的規則運算式旗標。", "Unknown_type_acquisition_option_0_17010": "未知的類型取得選項 '{0}'。", "Unknown_type_acquisition_option_0_Did_you_mean_1_17018": "未知的類型擷取選項 '{0}'。您是指 '{1}' 嗎?", "Unknown_watch_option_0_5078": "未知的監看選項 '{0}'。", "Unknown_watch_option_0_Did_you_mean_1_5079": "未知的監看選項 '{0}'。您是指 '{1}' 嗎?", "Unreachable_code_detected_7027": "偵測到無法執行到的程式碼。", "Unterminated_Unicode_escape_sequence_1199": "未結束的 Unicode 逸出序列。", "Unterminated_quoted_string_in_response_file_0_6045": "回應檔 '{0}' 中有未結束的括號字串。", "Unterminated_regular_expression_literal_1161": "未結束的規則運算式常值。", "Unterminated_string_literal_1002": "未結束的字串常值。", "Unterminated_template_literal_1160": "未結束的樣板常值。", "Untyped_function_calls_may_not_accept_type_arguments_2347": "不具類型的函式呼叫無法接受類型引數。", "Unused_label_7028": "未使用的標籤。", "Unused_ts_expect_error_directive_2578": "未使用的 '@ts-expect-error' 指示詞。", "Update_import_from_0_90058": "從 \"{0}\" 更新匯入", "Update_modifiers_of_0_90061": "更新 '{0}' 的修飾元", "Updating_output_timestamps_of_project_0_6359": "正在更新專案 '{0}' 的輸出時間戳記...", "Updating_unchanged_output_timestamps_of_project_0_6371": "正在更新專案 '{0}' 的未更變輸出時間戳記...", "Use_0_95174": "使用 `{0}`。", "Use_0_instead_5106": "請改用 '{0}'。", "Use_Number_isNaN_in_all_conditions_95175": "在所有條件中都使用 'Number.isNaN'。", "Use_element_access_for_0_95145": "對 '{0}' 使用元素存取", "Use_element_access_for_all_undeclared_properties_95146": "對所有未宣告的屬性使用元素存取。", "Use_import_type_95180": "請使用 'import type'", "Use_synthetic_default_member_95016": "使用綜合 'default' 成員。", "Use_the_package_json_exports_field_when_resolving_package_imports_6408": "解析套件匯入時，請使用 package.json 'exports' 欄位。", "Use_the_package_json_imports_field_when_resolving_imports_6409": "解析匯入時，請使用 package.json 'imports' 欄位。", "Use_type_0_95181": "請使用 'type {0}'", "Using_0_subpath_1_with_target_2_6404": "使用 '{0}' 子路徑 '{1}' 與目標 '{2}'。", "Using_JSX_fragments_requires_fragment_factory_0_to_be_in_scope_but_it_could_not_be_found_2879": "使用 JSX 片段需要片段中心 '{0}' 在範圍內，但無法找到。", "Using_a_string_in_a_for_of_statement_is_only_supported_in_ECMAScript_5_and_higher_2494": "只有在 ECMAScript 5 及更高版本中，才可在 'for...of' 陳述式中使用字串。", "Using_build_b_will_make_tsc_behave_more_like_a_build_orchestrator_than_a_compiler_This_is_used_to_tr_6915": "使用 --build、-b 會讓 tsc 的行為較編譯器更像是建置協調器。這可用於觸發建置複合專案，您可以在以下位置深入了解: {0}", "Using_compiler_options_of_project_reference_redirect_0_6215": "正在使用專案參考重新導向 '{0}' 的編譯器選項。", "VERSION_6036": "版本", "Value_of_type_0_has_no_properties_in_common_with_type_1_Did_you_mean_to_call_it_2560": "類型為 '{0}' 的值與類型 '{1}' 沒有任何共通的屬性。確定要呼叫嗎?", "Value_of_type_0_is_not_callable_Did_you_mean_to_include_new_2348": "無法呼叫類型 '{0}' 的值。您要包含 'new' 嗎?", "Variable_0_implicitly_has_an_1_type_7005": "變數 '{0}' 隱含有 '{1}' 類型。", "Variable_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7043": "變數 '{0}' 隱含 '{1}' 類型，但可從使用方式推斷更適合的類型。", "Variable_0_implicitly_has_type_1_in_some_locations_but_a_better_type_may_be_inferred_from_usage_7046": "變數 '{0}' 在某些位置隱含類型 '{1}'，但可從使用方式推斷更適合的類型。", "Variable_0_implicitly_has_type_1_in_some_locations_where_its_type_cannot_be_determined_7034": "變數 '{0}' 在某些其類型無法判斷的位置隱含地擁有類型 '{1}'。", "Variable_0_is_used_before_being_assigned_2454": "變數 '{0}' 已在指派之前使用。", "Variable_declaration_expected_1134": "必須是變數宣告。", "Variable_declaration_list_cannot_be_empty_1123": "變數宣告清單不得為空白。", "Variable_declaration_not_allowed_at_this_location_1440": "此位置不允許變數宣告。", "Variable_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9010": "變數必須有具備 --isolatedDeclarations 的明確型別註釋。", "Variables_with_multiple_declarations_cannot_be_inlined_95186": "無法內嵌有多個宣告的變數。", "Variadic_element_at_position_0_in_source_does_not_match_element_at_position_1_in_target_2625": "來源中位於 {0} 的可變元素與目標中位於 {1} 的元素不相符。", "Variance_annotations_are_only_supported_in_type_aliases_for_object_function_constructor_and_mapped_t_2637": "只有物件、函式、建構函式和對應類型的類型別名才支援差異註釋。", "Version_0_6029": "版本 {0}", "Visit_https_Colon_Slash_Slashaka_ms_Slashtsconfig_to_read_more_about_this_file_95110": "瀏覽 https://aka.ms/tsconfig 以閱讀此檔案的詳細資訊", "WATCH_OPTIONS_6918": "監看式選項", "Watch_and_Build_Modes_6250": "觀看及建置模式", "Watch_input_files_6005": "監看輸入檔。", "Watch_option_0_requires_a_value_of_type_1_5080": "監看選項 '{0}' 需要 {1} 類型的值。", "We_can_only_write_a_type_for_0_by_adding_a_type_for_the_entire_parameter_here_2843": "我們只能在此新增整個參數的類型，來寫入 '{0}' 的類型。", "When_assigning_functions_check_to_ensure_parameters_and_the_return_values_are_subtype_compatible_6698": "指派函式時，請檢查以確認參數，而且傳回值是子類型相容的。", "When_type_checking_take_into_account_null_and_undefined_6699": "當型別檢查時，請將 'null' 和 'undefined' 納入考慮。", "Whether_to_keep_outdated_console_output_in_watch_mode_instead_of_clearing_the_screen_6191": "是否要將已過期的主控台輸出，維持在監看模式下，而非清除螢幕。", "Wrap_all_invalid_characters_in_an_expression_container_95109": "將所有無效字元包裝在運算式容器中", "Wrap_all_invalid_decorator_expressions_in_parentheses_95195": "將所有無效的裝飾項目運算式以括弧包住", "Wrap_all_object_literal_with_parentheses_95116": "使用括弧括住所有物件常值", "Wrap_all_unparented_JSX_in_JSX_fragment_95121": "將所有無上層 JSX 包裝至 JSX 片段中", "Wrap_in_JSX_fragment_95120": "包裝至 JSX 片段中", "Wrap_in_parentheses_95194": "以括弧包住", "Wrap_invalid_character_in_an_expression_container_95108": "包裝在運算式容器中的字元無效", "Wrap_the_following_body_with_parentheses_which_should_be_an_object_literal_95113": "使用括弧括住下列必須是物件常值的主體", "You_can_learn_about_all_of_the_compiler_options_at_0_6913": "您可以在以下位置了解所有編譯器選項: {0}", "You_cannot_rename_a_module_via_a_global_import_8031": "您無法透過全域匯入重新命名模組。", "You_cannot_rename_elements_that_are_defined_in_a_node_modules_folder_8035": "您無法重新命名 'node_modules' 資料夾中定義的元素。", "You_cannot_rename_elements_that_are_defined_in_another_node_modules_folder_8036": "您無法重新命名其他 'node_modules' 資料夾中定義的元素。", "You_cannot_rename_elements_that_are_defined_in_the_standard_TypeScript_library_8001": "您無法重新命名標準 TypeScript 程式庫中所定義的項目。", "You_cannot_rename_this_element_8000": "您無法重新命名這個項目。", "_0_accepts_too_few_arguments_to_be_used_as_a_decorator_here_Did_you_mean_to_call_it_first_and_write__1329": "'{0}' 在此只接受極少數的引數用為裝飾項目。要先呼叫此項目，然後再寫入 '@{0}()' 嗎?", "_0_and_1_index_signatures_are_incompatible_2330": "'{0}' 和 '{1}' 索引簽章不相容。", "_0_and_1_operations_cannot_be_mixed_without_parentheses_5076": "'{0}' 與 '{1}' 作業無法在沒有括號的情況下同時使用。", "_0_are_specified_twice_The_attribute_named_0_will_be_overwritten_2710": "'{0}' 指定了兩次。將會覆寫名為 '{0}' 的屬性。", "_0_at_the_end_of_a_type_is_not_valid_TypeScript_syntax_Did_you_mean_to_write_1_17019": "型別以 '{0}' 作為結尾，並非有效的 TypeScript 語法。您是要寫入 '{1}' 嗎?", "_0_at_the_start_of_a_type_is_not_valid_TypeScript_syntax_Did_you_mean_to_write_1_17020": "型別以 '{0}' 作為開頭，並非有效的 TypeScript 語法。您是要寫入 '{1}' 嗎?", "_0_can_only_be_imported_by_turning_on_the_esModuleInterop_flag_and_using_a_default_import_2596": "只能透過開啟 'esModuleInterop' 旗標並使用預設匯入來匯入 '{0}'。", "_0_can_only_be_imported_by_using_a_default_import_2595": "只能使用預設匯入來匯入 '{0}'。", "_0_can_only_be_imported_by_using_a_require_call_or_by_turning_on_the_esModuleInterop_flag_and_using__2598": "只能使用 'require' 呼叫，或透過開啟 'esModuleInterop' 旗標並使用預設匯入，來匯入 '{0}'。", "_0_can_only_be_imported_by_using_a_require_call_or_by_using_a_default_import_2597": "只能使用 'require' 呼叫或預設匯入來匯入 '{0}'。", "_0_can_only_be_imported_by_using_import_1_require_2_or_a_default_import_2616": "只能使用 'import {1} = require({2})' 或預設匯入來匯入 '{0}'。", "_0_can_only_be_imported_by_using_import_1_require_2_or_by_turning_on_the_esModuleInterop_flag_and_us_2617": "只能使用 'import {1} = require({2})'，或透過開啟 'esModuleInterop' 旗標並使用預設匯入，來匯入 '{0}'。", "_0_cannot_be_used_as_a_JSX_component_2786": "'{0}' 不能用作 JSX 元件。", "_0_cannot_be_used_as_a_value_because_it_was_exported_using_export_type_1362": "因為 '{0}' 是使用 'export type' 匯出的，所以無法作為值使用。", "_0_cannot_be_used_as_a_value_because_it_was_imported_using_import_type_1361": "因為 '{0}' 是使用 'import type' 匯入的，所以無法作為值使用。", "_0_components_don_t_accept_text_as_child_elements_Text_in_JSX_has_the_type_string_but_the_expected_t_2747": "'{0}' 元件不接受文字作為子項目。JSX 中的文字具有類型 'string'，但 '{1}' 需要的類型為 '{2}'。", "_0_could_be_instantiated_with_an_arbitrary_type_which_could_be_unrelated_to_1_5082": "'{0}' 可以使用與 '{1}' 無關的任意類型來具現化。", "_0_declarations_can_only_be_declared_inside_a_block_1156": "只能在區塊內宣告 '{0}' 宣告。", "_0_declarations_can_only_be_used_in_TypeScript_files_8006": "'{0}' 宣告只可用於 TypeScript 檔案中。", "_0_declarations_may_not_have_binding_patterns_1492": "'{0}' 宣告可能沒有繫結模式。", "_0_declarations_must_be_initialized_1155": "必須初始化 '{0}' 宣告。", "_0_expected_1005": "必須是 '{0}'。", "_0_has_a_string_type_but_must_have_syntactically_recognizable_string_syntax_when_isolatedModules_is__18055": "'{0}' 具有字串型別，但在啟用 'isolatedModules' 的狀態下，其語法必須是可辨識的字串語法。", "_0_has_no_exported_member_named_1_Did_you_mean_2_2724": "'{0}' 沒有任何名稱為 '{1}' 的已匯出成員。您是指 '{2}' 嗎?", "_0_implicitly_has_an_1_return_type_but_a_better_type_may_be_inferred_from_usage_7050": "'{0}' 隱含 '{1}' 傳回型別，但可從使用方式推斷更適合的類型。", "_0_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_reference_7023": "'{0}' 因為沒有傳回型別註解，且在其中一個傳回運算式中直接或間接參考了自己，所以隱含了傳回型別 'any'。", "_0_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_and_is_referenced_directly_or__7022": "'{0}' 因為沒有類型註釋，且在其本身的初始設定式中直接或間接參考了自己，所以隱含有類型 'any'。", "_0_index_signatures_are_incompatible_2634": "'{0}' 索引簽章不相容。", "_0_index_type_1_is_not_assignable_to_2_index_type_3_2413": "'{0}' 索引類型 '{1}' 無法指派給 '{2}' 索引類型 '{3}'。", "_0_is_a_primitive_but_1_is_a_wrapper_object_Prefer_using_0_when_possible_2692": "'{0}' 為基元，但 '{1}' 為包裝函式物件。建議盡可能使用 '{0}'。", "_0_is_a_type_and_cannot_be_imported_in_JavaScript_files_Use_1_in_a_JSDoc_type_annotation_18042": "'{0}' 為類型，無法匯入 JavaScript 檔案。在 JSDoc 類型註釋中使用 '{1}'。", "_0_is_a_type_and_must_be_imported_using_a_type_only_import_when_verbatimModuleSyntax_is_enabled_1484": "'{0}' 為型別，且在啟用 'verbatimModuleSyntax' 的狀態下，必須透過僅限型別的匯入作業匯入。", "_0_is_an_unused_renaming_of_1_Did_you_intend_to_use_it_as_a_type_annotation_2842": "'{0}' 是未使用的 '{1}' 重新命名。您是否想要使用它作為類型註釋?", "_0_is_assignable_to_the_constraint_of_type_1_but_1_could_be_instantiated_with_a_different_subtype_of_5075": "'{0}' 可指派給 '{1}' 類型的條件約束，但可能會將 '{1}' 以不同的條件約束 '{2}' 子類型來具現化。", "_0_is_automatically_exported_here_18044": "'{0}' 會自動匯出到此處。", "_0_is_declared_but_its_value_is_never_read_6133": "'{0}' 已宣告但從未讀取其值。", "_0_is_declared_but_never_used_6196": "宣告了 '{0}'，但從未使用過。", "_0_is_declared_here_2728": "'{0}' 宣告於此處。", "_0_is_defined_as_a_property_in_class_1_but_is_overridden_here_in_2_as_an_accessor_2611": "'{0}' 在類別 '{1}' 中定義為屬性，但在此處的 '{2}' 中卻覆寫為存取子。", "_0_is_defined_as_an_accessor_in_class_1_but_is_overridden_here_in_2_as_an_instance_property_2610": "'{0}' 在類別 '{1}' 中定義為存取子，但在此處的 '{2}' 中卻覆寫為執行個體屬性。", "_0_is_deprecated_6385": "'{0}' 已淘汰。", "_0_is_not_a_valid_meta_property_for_keyword_1_Did_you_mean_2_17012": "'{0}' 對關鍵字 '{1}' 而言不是有效的中繼屬性。您是指 '{2}' 嗎?", "_0_is_not_allowed_as_a_parameter_name_1390": "不允許 '{0}' 做為參數名稱。", "_0_is_not_allowed_as_a_variable_declaration_name_1389": "'{0}' 不能是變數宣告名稱。", "_0_is_of_type_unknown_18046": "'{0}' 的類型為 'unknown'。", "_0_is_possibly_null_18047": "'{0}' 可能是 'null'。", "_0_is_possibly_null_or_undefined_18049": "'{0}' 可能為「null」或「未定義」。", "_0_is_possibly_undefined_18048": "'{0}' 可能為「未定義」。", "_0_is_referenced_directly_or_indirectly_in_its_own_base_expression_2506": "'{0}' 在其本身的基底運算式中直接或間接受到參考。", "_0_is_referenced_directly_or_indirectly_in_its_own_type_annotation_2502": "'{0}' 在其本身的類型註釋中直接或間接受到參考。", "_0_is_specified_more_than_once_so_this_usage_will_be_overwritten_2783": "多次指定 '{0}'，因此將會覆寫此使用方式。", "_0_list_cannot_be_empty_1097": "'{0}' 清單不得為空白。", "_0_modifier_already_seen_1030": "已有 '{0}' 修飾元。", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_class_interface_or_type_alias_1274": "'{0}' 修飾元只能出現在類別、介面或型別別名的型別參數上", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_function_method_or_class_1277": "'{0}' 修飾元只能出現在方法或類別，或是函式的型別參數中。", "_0_modifier_cannot_appear_on_a_constructor_declaration_1089": "建構函式宣告不得有 '{0}' 修飾元。", "_0_modifier_cannot_appear_on_a_module_or_namespace_element_1044": "模組或命名空間元素不能有 '{0}' 修飾元。", "_0_modifier_cannot_appear_on_a_parameter_1090": "參數不得有 '{0}' 修飾元。", "_0_modifier_cannot_appear_on_a_type_member_1070": "類型成員不能有 '{0}' 修飾元。", "_0_modifier_cannot_appear_on_a_type_parameter_1273": "型別參數上不能出現 '{0}' 修飾元", "_0_modifier_cannot_appear_on_a_using_declaration_1491": "'{0}' 修飾元不可出現在 'using' 宣告中。", "_0_modifier_cannot_appear_on_an_await_using_declaration_1495": "'{0}' 修飾元不可出現在 'await using' 宣告中。", "_0_modifier_cannot_appear_on_an_index_signature_1071": "索引簽章不能有 '{0}' 修飾元。", "_0_modifier_cannot_appear_on_class_elements_of_this_kind_1031": "不得在此種類別項目中使用 '{0}' 修飾元。", "_0_modifier_cannot_be_used_here_1042": "無法在此處使用 '{0}' 修飾元。", "_0_modifier_cannot_be_used_in_an_ambient_context_1040": "無法在環境內容中使用 '{0}' 修飾元。", "_0_modifier_cannot_be_used_with_1_modifier_1243": "'{0}' 修飾元無法與 '{1}' 修飾元並用。", "_0_modifier_cannot_be_used_with_a_private_identifier_18019": "'{0}' 修飾元不可搭配私人識別碼一起使用。", "_0_modifier_must_precede_1_modifier_1029": "'{0}' 修飾元必須在 '{1}' 修飾元之前。", "_0_must_be_followed_by_a_Unicode_property_value_expression_enclosed_in_braces_1531": "'\\{0}' 後必須是以大括弧括住的 Unicode 屬性值運算式。", "_0_needs_an_explicit_type_annotation_2782": "'{0}' 需要明確的型別註解。", "_0_only_refers_to_a_type_but_is_being_used_as_a_namespace_here_2702": "'{0}' 只參考類型，但在這裡用作命名空間。", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_2693": "'{0}' 只會參考類型，但此處將其用為值。", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Did_you_mean_to_use_1_in_0_2690": "'{0}' 僅限於類型，但此處用為值。您要使用 '{1} in {0}' 嗎?", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Do_you_need_to_change_your_target_library_2585": "「{0}」僅指一種類型，但在此卻作為值使用。要變更您的目標程式庫嗎? 請嘗試將 `lib` 編譯器選項變更為 es2015 或更新版本。", "_0_refers_to_a_UMD_global_but_the_current_file_is_a_module_Consider_adding_an_import_instead_2686": "'{0}' 指的是全域的 UMD，但目前的檔案為模組。請考慮改為新增匯入。", "_0_refers_to_a_value_but_is_being_used_as_a_type_here_Did_you_mean_typeof_0_2749": "'{0}' 為值，但在此處卻作為類型使用。您是否是指 'typeof {0}'?", "_0_resolves_to_a_type_and_must_be_marked_type_only_in_this_file_before_re_exporting_when_1_is_enable_1291": "'{0}' 會解析為宣告，且在啟用 '{1}' 的狀態下，必須先在此檔案中必須標記為僅限型別，才可重新匯出。建議使用匯入了 '{0}' 的 'import type'。", "_0_resolves_to_a_type_and_must_be_marked_type_only_in_this_file_before_re_exporting_when_1_is_enable_1292": "'{0}' 會解析為宣告，且在啟用 '{1}' 的狀態下，必須先在此檔案中必須標記為僅限型別，才可重新匯出。建議使用 'export type { {0} as default }'。", "_0_resolves_to_a_type_only_declaration_and_must_be_imported_using_a_type_only_import_when_verbatimMo_1485": "'{0}' 會解析為僅限型別的宣告，且在啟用 'verbatimModuleSyntax' 的狀態下，必須透過僅限型別的匯入作業匯入。", "_0_resolves_to_a_type_only_declaration_and_must_be_marked_type_only_in_this_file_before_re_exporting_1289": "'{0}' 會解析為僅限型別的宣告，且在啟用 '{1}' 的狀態下，必須先在此檔案中必須標記為僅限型別，才可重新匯出。建議使用匯入了 '{0}' 的 'import type'。", "_0_resolves_to_a_type_only_declaration_and_must_be_marked_type_only_in_this_file_before_re_exporting_1290": "'{0}' 會解析為僅限型別的宣告，且在啟用 '{1}' 的狀態下，必須先在此檔案中必須標記為僅限型別，才可重新匯出。建議使用 'export type { {0} as default }'。", "_0_resolves_to_a_type_only_declaration_and_must_be_re_exported_using_a_type_only_re_export_when_1_is_1448": "'{0}' 會解析為僅限型別的宣告，且在啟用 '{1}' 的狀態下，必須透過僅限型別的重新匯出作業重新匯出。", "_0_should_be_set_inside_the_compilerOptions_object_of_the_config_json_file_6258": "'{0}' 應該在設定 json 檔案的 'compilerOptions' 物件內設定。", "_0_tag_already_specified_1223": "已指定 '{0}' 標記。", "_0_was_also_declared_here_6203": "'{0}' 也已宣告於此處。", "_0_was_exported_here_1377": "'{0}' 已匯出到此處。", "_0_was_imported_here_1376": "'{0}' 已匯入到此處。", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_return_type_7010": "缺少傳回型別註解的 '{0}' 隱含了 '{1}' 傳回型別。", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_yield_type_7055": "缺少傳回型別註解的 '{0}' 隱含 '{1}' 產生類型。", "abstract_modifier_can_only_appear_on_a_class_method_or_property_declaration_1242": "'abstract' 修飾元只能出現在類別宣告、方法宣告或屬性宣告。", "accessor_modifier_can_only_appear_on_a_property_declaration_1275": "'accessor' 修飾詞只能出現在屬性宣告。", "and_here_6204": "及此處。", "arguments_cannot_be_referenced_in_property_initializers_2815": "屬性初始設定式中不得參考 'arguments'。", "auto_Colon_Treat_files_with_imports_exports_import_meta_jsx_with_jsx_Colon_react_jsx_or_esm_format_w_1476": "\"auto\": 處理具有 imports、exports、import.meta, jsx (具有 jsx: react-jsx) 的檔案，或以 esm 格式 (具有 module: node16+) 作為模組。", "await_expression_cannot_be_used_inside_a_class_static_block_18037": "'await' 運算式無法在類別靜態區塊內使用。", "await_expressions_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_fi_1375": "當檔案為模組時，只允許 'await' 運算式位於檔案的最上層，但是此檔案沒有匯入或匯出。請考慮新增空白的 'export {}'，使這個檔案成為模組。", "await_expressions_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1308": "只允許在非同步函式中與模組的最上層使用 'await' 運算式。", "await_expressions_cannot_be_used_in_a_parameter_initializer_2524": "'await' 運算式不得用於參數初始設定式。", "await_has_no_effect_on_the_type_of_this_expression_80007": "'await' 對此運算式的類型沒有作用。", "await_using_statements_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_th_2853": "當檔案為模組時，僅能在檔案最上層使用 'await using' 陳述式，但此檔案沒有任何匯入或匯出。建議新增空白的 'export {}'，讓此檔案成為模組。", "await_using_statements_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_2852": "'await using' 陳述式僅能在非同步函式內，以及模組的最上層使用。", "await_using_statements_cannot_be_used_inside_a_class_static_block_18054": "'await using' 陳述式無法在類別靜態區塊內使用。", "baseUrl_option_is_set_to_0_using_this_value_to_resolve_non_relative_module_name_1_6106": "'baseUrl' 選項已設為 '{0}'。此值將用於解析非相對的模組名稱 '{1}'。", "c_must_be_followed_by_an_ASCII_letter_1512": "'\\c' 後必須是 ASCII 字母。", "can_only_be_used_at_the_start_of_a_file_18026": "'#!' 只能用於檔案開頭。", "case_or_default_expected_1130": "必須是 'case' 或 'default'。", "catch_or_finally_expected_1472": "必須是 'catch' 或 'finally'。", "const_enum_member_initializer_was_evaluated_to_a_non_finite_value_2477": "'const' 列舉成員初始設定式已評估為非有限值。", "const_enum_member_initializer_was_evaluated_to_disallowed_value_NaN_2478": "'const' 列舉成員初始設定式已評估為不允許的值 'NaN'。", "const_enum_member_initializers_must_be_constant_expressions_2474": "常數列舉成員初始設定式必須為常數運算式。", "const_enums_can_only_be_used_in_property_or_index_access_expressions_or_the_right_hand_side_of_an_im_2475": "'const' 列舉只可用於屬性或索引存取運算式中，或用於匯入宣告、匯出指派或類型查詢的右側。", "constructor_cannot_be_used_as_a_parameter_property_name_2398": "'constructor' 不能作為參數屬性名稱使用。", "constructor_is_a_reserved_word_18012": "'#constructor' 為保留字。", "default_Colon_6903": "預設:", "delete_cannot_be_called_on_an_identifier_in_strict_mode_1102": "不得在 strict 模式中對識別碼呼叫 'delete'。", "export_Asterisk_does_not_re_export_a_default_1195": "'export *' 不會重新匯出預設匯出。", "export_can_only_be_used_in_TypeScript_files_8003": "'export =' 只可用於 TypeScript 檔案中。", "export_modifier_cannot_be_applied_to_ambient_modules_and_module_augmentations_since_they_are_always__2668": "'export' 修飾元無法套用至環境模組或模組增強指定，原因是這二者永遠會顯示。", "extends_clause_already_seen_1172": "已經有 'extends' 子句。", "extends_clause_must_precede_implements_clause_1173": "'extends' 子句必須在 'implements' 子句之前。", "extends_clause_of_exported_class_0_has_or_is_using_private_name_1_4020": "匯出類別 '{0}' 的 'extends' 子句具有或使用私用名稱 '{1}'。", "extends_clause_of_exported_class_has_or_is_using_private_name_0_4021": "匯出類別的 'extends' 子句包含或使用了私人名稱 '{0}'。", "extends_clause_of_exported_interface_0_has_or_is_using_private_name_1_4022": "匯出介面 '{0}' 的 'extends' 子句具有或使用私用名稱 '{1}'。", "false_unless_composite_is_set_6906": "`false`，除非已設定 `composite`", "false_unless_strict_is_set_6905": "`false`，除非已設定 `strict`", "file_6025": "檔案", "for_await_loops_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_file_1431": "當檔案為模組時，只允許 'for await' 迴圈位於檔案的最上層，但是此檔案沒有匯入或匯出。請考慮新增空白的 'export {}'，使這個檔案成為模組。", "for_await_loops_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1103": "只允許在非同步函式中與模組的最上層使用 'for await' 迴圈。", "for_await_loops_cannot_be_used_inside_a_class_static_block_18038": "'for await' 迴圈無法在類別靜態區塊內使用。", "get_and_set_accessors_cannot_declare_this_parameters_2784": "'get' 和 'set' 存取子不可宣告 'this' 參數。", "if_files_is_specified_otherwise_Asterisk_Asterisk_Slash_Asterisk_6908": "如果指定 `files`，則為 `[]`，否則為 `[\"**/*\"]5D;`", "implements_clause_already_seen_1175": "已經有 'implements' 子句。", "implements_clauses_can_only_be_used_in_TypeScript_files_8005": "'implements' 子句只可用於 TypeScript 檔案中。", "import_can_only_be_used_in_TypeScript_files_8002": "'import ... =' 只可用於 TypeScript 檔案中。", "infer_declarations_are_only_permitted_in_the_extends_clause_of_a_conditional_type_1338": "只允許在條件式類型的 'extends' 子句中使用 'infer' 宣告。", "k_must_be_followed_by_a_capturing_group_name_enclosed_in_angle_brackets_1510": "'\\k' 後必須是以角括弧括住的擷取群組名稱。", "let_is_not_allowed_to_be_used_as_a_name_in_let_or_const_declarations_2480": "'let' 或 'const' 宣告中不得使用 'let' 作為名稱。", "module_AMD_or_UMD_or_System_or_ES6_then_Classic_Otherwise_Node_69010": "模組 === `AMD` 或 `UMD` 或 `System` 或 `ES6`，則為 `Classic`，否則為 `Node`", "module_system_or_esModuleInterop_6904": "模組 === \"system\" 或 esModuleInterop", "new_expression_whose_target_lacks_a_construct_signature_implicitly_has_an_any_type_7009": "目標缺少建構簽章的 'new' 運算式隱含了 'any' 類型。", "node_modules_bower_components_jspm_packages_plus_the_value_of_outDir_if_one_is_specified_6907": "`[\"node_modules\", \"bower_components\", \"jspm_packages\"]`，加上 `outDir` 的值 (如果有指定)。", "one_of_Colon_6900": "以下其中一個:", "one_or_more_Colon_6901": "以下一或多個:", "options_6024": "選項", "or_JSX_element_expected_1145": "必須是 '{' 或 JSX 元素。", "or_expected_1144": "必須是 '{' 或 ';'。", "package_json_does_not_have_a_0_field_6100": "'package.json' 沒有 '{0}' 欄位。", "package_json_does_not_have_a_typesVersions_entry_that_matches_version_0_6207": "'package.json' 沒有任何符合 '{0}' 版本的 'typesVersions' 項目。", "package_json_had_a_falsy_0_field_6220": "'package.json' 具有假的 '{0}' 欄位。", "package_json_has_0_field_1_that_references_2_6101": "'package.json' 有參考 '{2}' 的 '{0}' 欄位 '{1}'。", "package_json_has_a_peerDependencies_field_6281": "'package.json' 具有 'peerDependencies' 欄位。", "package_json_has_a_typesVersions_entry_0_that_is_not_a_valid_semver_range_6209": "'package.json' 具有 'typesVersions' 項目 '{0}'，其非有效的 SemVer 範圍。", "package_json_has_a_typesVersions_entry_0_that_matches_compiler_version_1_looking_for_a_pattern_to_ma_6208": "'package.json' 具有符合編譯器版本 '{1}' 的 'typesVersions' 項目 '{0}'，要尋找符合模組名稱 '{2}' 的模式。", "package_json_has_a_typesVersions_field_with_version_specific_path_mappings_6206": "'package.json' 具有限定版本路徑對應的 'typesVersions' 欄位。", "package_json_scope_0_explicitly_maps_specifier_1_to_null_6274": "package.js 範圍 '{0}' 將指定名稱 '{1}' 明確對應到 Null。", "package_json_scope_0_has_invalid_type_for_target_of_specifier_1_6275": "package.js 範圍 '{0}' 對指定名稱 '{1}' 的目標具有無效類型", "package_json_scope_0_has_no_imports_defined_6273": "package.js 範圍 '{0}' 沒有定義的匯入。", "paths_option_is_specified_looking_for_a_pattern_to_match_module_name_0_6091": "'paths' 選項已指定，將尋找符合模組名稱 '{0}' 的模式。", "q_is_only_available_inside_character_class_1511": "'\\q' 僅可在字元類別中使用。", "q_must_be_followed_by_string_alternatives_enclosed_in_braces_1521": "'\\q' 後必須是字串 (可選擇以大括弧括住該字串)。", "readonly_modifier_can_only_appear_on_a_property_declaration_or_index_signature_1024": "'readonly' 修飾元只能出現在屬性宣告或索引簽章。", "readonly_type_modifier_is_only_permitted_on_array_and_tuple_literal_types_1354": "'readonly' 類型修飾元只可用於陣列與元組常值類型。", "require_call_may_be_converted_to_an_import_80005": "'require' 呼叫可能會轉換為匯入。", "resolution_mode_can_only_be_set_for_type_only_imports_1454": "只能針對僅限輸入的匯入設定 'resolution-mode'。", "resolution_mode_is_the_only_valid_key_for_type_import_assertions_1455": "'resolution-mode' 是輸入匯入判斷提示唯一有效的索引鍵。", "resolution_mode_is_the_only_valid_key_for_type_import_attributes_1463": "'resolution-mode' 是型別匯入屬性唯一有效的索引鍵。", "resolution_mode_should_be_either_require_or_import_1453": "`resolution-mode` 應該是 `require` 或 `import`。", "rootDirs_option_is_set_using_it_to_resolve_relative_module_name_0_6107": "'rootDirs' 選項已設定。該選項將用於解析相對的模組名稱 '{0}'。", "super_can_only_be_referenced_in_a_derived_class_2335": "只有衍生類別中才可參考 'super'。", "super_can_only_be_referenced_in_members_of_derived_classes_or_object_literal_expressions_2660": "只有在衍生類別或物件常值運算式的成員中才可參考 'super'。", "super_cannot_be_referenced_in_a_computed_property_name_2466": "計算的屬性名稱中不得參考 'super'。", "super_cannot_be_referenced_in_constructor_arguments_2336": "建構函式引數中不得參考 'super'。", "super_is_only_allowed_in_members_of_object_literal_expressions_when_option_target_is_ES2015_or_highe_2659": "當選項 'target' 為 'ES2015' 或更高時，只有在物件常值運算式的成員中才允許 'super'。", "super_may_not_use_type_arguments_2754": "'super' 不可以使用類型引數。", "super_must_be_called_before_accessing_a_property_of_super_in_the_constructor_of_a_derived_class_17011": "必須先呼叫 'super' 才能存取衍生類別建構函式中 'super' 的屬性。", "super_must_be_called_before_accessing_this_in_the_constructor_of_a_derived_class_17009": "必須先呼叫 'super' 才能存取衍生類別中建構函式的 'this'。", "super_must_be_followed_by_an_argument_list_or_member_access_1034": "'super' 之後必須接引數清單或成員存取。", "super_property_access_is_permitted_only_in_a_constructor_member_function_or_member_accessor_of_a_der_2338": "只有在建構函式、成員函式或衍生類別的成員存取子中，才能存取 'super' 屬性。", "this_cannot_be_referenced_in_a_computed_property_name_2465": "計算的屬性名稱中不得參考 'this'。", "this_cannot_be_referenced_in_a_module_or_namespace_body_2331": "模組或命名空間主體中不得參考 'this'。", "this_cannot_be_referenced_in_a_static_property_initializer_2334": "靜態屬性初始設定式中不得參考 'this'。", "this_cannot_be_referenced_in_current_location_2332": "目前的位置中不得參考 'this'。", "this_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_2683": "因為 'this' 沒有型別註解，所以隱含具有類型 'any'。", "true_for_ES2022_and_above_including_ESNext_6930": "ES2022 及以上為 true，包括 ESNext。", "true_if_composite_false_otherwise_6909": "如果為 `composite`，則為 `true`，否則為 `false`", "true_when_moduleResolution_is_node16_nodenext_or_bundler_otherwise_false_6411": "當 'moduleResolution' 為 'node16'、'nodenext' 或 'bundler' 時，為 `true`; 否則為 `false`。", "tsc_Colon_The_TypeScript_Compiler_6922": "tsc: TypeScript 編譯器", "type_Colon_6902": "輸入:", "unique_symbol_types_are_not_allowed_here_1335": "這裡不允許 'unique symbol' 型別。", "unique_symbol_types_are_only_allowed_on_variables_in_a_variable_statement_1334": "只有變數陳述式中的變數允許 'unique symbol' 型別。", "unique_symbol_types_may_not_be_used_on_a_variable_declaration_with_a_binding_name_1333": "'unique symbol' 型別無法用在具有繫結名稱的變數宣告上。", "use_strict_directive_cannot_be_used_with_non_simple_parameter_list_1347": "'use strict' 指示詞不可搭配非簡易參數清單使用。", "use_strict_directive_used_here_1349": "'use strict' 指示詞已用於此處。", "with_statements_are_not_allowed_in_an_async_function_block_1300": "非同步函式區塊中不允許 'with' 陳述式。", "with_statements_are_not_allowed_in_strict_mode_1101": "不得在 strict 模式中使用 'with' 陳述式。", "yield_expression_implicitly_results_in_an_any_type_because_its_containing_generator_lacks_a_return_t_7057": "因為 'yield' 運算式包含的產生器缺少傳回型別註解，所以其隱含導致 'any' 類型。", "yield_expressions_cannot_be_used_in_a_parameter_initializer_2523": "'yield' 運算式不得用於參數初始設定式。"}