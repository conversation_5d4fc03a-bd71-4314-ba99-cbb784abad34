{"version": 3, "file": "child-require.js", "sourceRoot": "", "sources": ["../../src/child/child-require.ts"], "names": [], "mappings": ";AAGA,MAAM,QAAQ,GAAG,OAAuC,CAAC;AAEzD,sIAAsI;AAEtI,IAAI,iBAAuC,CAAC;AAC5C,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;IAC3C,iBAAiB,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAChD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;CACzC;KAAM;IACL,iBAAiB,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC;IAC7C,QAAQ,CAAC,OAAO,CAAC,OAAO,GAAG,SAAS,CAAC;CACtC;AAED,MAAM,YAAY,GAAG,yDAAyD,CAAC;AAC/E,SAAS,SAAS,CAAY,OAAc,EAAE,GAAG,IAAW;IAC1D,wDAAwD;IACxD,IACE,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,MAAK,qBAAqB;QACvC,YAAY,CAAC,IAAI,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,CAAC;QAEnC,OAAO;IACT,uCAAuC;IACvC,OAAO,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;AACzD,CAAC", "sourcesContent": ["interface EventEmitterInternals {\n  _events: Record<string, Function | Array<Function>>;\n}\nconst _process = process as any as EventEmitterInternals;\n\n// Not shown here: Additional logic to correctly interact with process's events, either using this direct manipulation, or via the API\n\nlet originalOnWarning: Function | undefined;\nif (Array.isArray(_process._events.warning)) {\n  originalOnWarning = _process._events.warning[0];\n  _process._events.warning[0] = onWarning;\n} else {\n  originalOnWarning = _process._events.warning;\n  _process._events.warning = onWarning;\n}\n\nconst messageMatch = /(?:--(?:experimental-)?loader\\b|\\bCustom ESM Loaders\\b)/;\nfunction onWarning(this: any, warning: Error, ...rest: any[]) {\n  // Suppress warning about how `--loader` is experimental\n  if (\n    warning?.name === 'ExperimentalWarning' &&\n    messageMatch.test(warning?.message)\n  )\n    return;\n  // Will be undefined if `--no-warnings`\n  return originalOnWarning?.call(this, warning, ...rest);\n}\n"]}